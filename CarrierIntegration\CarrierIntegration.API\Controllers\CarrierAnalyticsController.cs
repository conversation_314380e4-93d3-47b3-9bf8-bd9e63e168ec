using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Application.Features.Analytics.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    [Authorize]
    public class CarrierAnalyticsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CarrierAnalyticsController> _logger;

        public CarrierAnalyticsController(IMediator mediator, ILogger<CarrierAnalyticsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get comprehensive carrier analytics
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierId">Optional carrier ID for specific carrier analytics</param>
        /// <param name="startDate">Analysis start date</param>
        /// <param name="endDate">Analysis end date</param>
        /// <param name="analyticsType">Type of analytics (Performance, Cost, Volume, Quality)</param>
        /// <param name="granularity">Data granularity (Hourly, Daily, Weekly, Monthly)</param>
        /// <param name="includePredictions">Include predictive analytics</param>
        /// <param name="includeComparisons">Include comparative analysis</param>
        /// <returns>Comprehensive analytics data</returns>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierAnalyticsDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierAnalyticsDto>>> GetCarrierAnalytics(
            [FromQuery] Guid organizationId,
            [FromQuery] Guid? carrierId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] string analyticsType = "Performance",
            [FromQuery] string granularity = "Daily",
            [FromQuery] bool includePredictions = false,
            [FromQuery] bool includeComparisons = true)
        {
            var query = new GetCarrierAnalyticsQuery
            {
                OrganizationId = organizationId,
                CarrierId = carrierId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow,
                AnalyticsType = analyticsType,
                Granularity = granularity,
                IncludePredictions = includePredictions,
                IncludeComparisons = includeComparisons
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get carrier dashboard data
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="dashboardType">Dashboard type (Executive, Operational, Financial)</param>
        /// <param name="asOfDate">Dashboard as of date</param>
        /// <param name="carrierIds">Optional list of carrier IDs to include</param>
        /// <param name="includeAlerts">Include alert information</param>
        /// <param name="includeTrends">Include trend analysis</param>
        /// <param name="includeForecasts">Include forecast data</param>
        /// <returns>Dashboard data</returns>
        [HttpGet("dashboard")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierDashboardDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierDashboardDto>>> GetCarrierDashboard(
            [FromQuery] Guid organizationId,
            [FromQuery] string dashboardType = "Executive",
            [FromQuery] DateTime? asOfDate = null,
            [FromQuery] List<Guid>? carrierIds = null,
            [FromQuery] bool includeAlerts = true,
            [FromQuery] bool includeTrends = true,
            [FromQuery] bool includeForecasts = false)
        {
            var query = new GetCarrierDashboardQuery
            {
                OrganizationId = organizationId,
                DashboardType = dashboardType,
                AsOfDate = asOfDate,
                CarrierIds = carrierIds,
                IncludeAlerts = includeAlerts,
                IncludeTrends = includeTrends,
                IncludeForecasts = includeForecasts
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get predictive analytics
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierId">Optional carrier ID for specific predictions</param>
        /// <param name="predictionType">Type of prediction (Performance, Cost, Volume, Capacity)</param>
        /// <param name="forecastDays">Number of days to forecast</param>
        /// <param name="confidenceLevel">Confidence level for predictions (0.0-1.0)</param>
        /// <param name="includeScenarios">Include scenario analysis</param>
        /// <returns>Predictive analytics data</returns>
        [HttpGet("predictions")]
        [ProducesResponseType(typeof(ApiResponseDto<PredictiveAnalyticsDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<PredictiveAnalyticsDto>>> GetPredictiveAnalytics(
            [FromQuery] Guid organizationId,
            [FromQuery] Guid? carrierId = null,
            [FromQuery] string predictionType = "Performance",
            [FromQuery] int forecastDays = 30,
            [FromQuery] decimal confidenceLevel = 0.95m,
            [FromQuery] bool includeScenarios = true)
        {
            var query = new GetPredictiveAnalyticsQuery
            {
                OrganizationId = organizationId,
                CarrierId = carrierId,
                PredictionType = predictionType,
                ForecastDays = forecastDays,
                ConfidenceLevel = confidenceLevel,
                IncludeScenarios = includeScenarios
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get carrier performance insights
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierId">Optional carrier ID</param>
        /// <param name="startDate">Analysis start date</param>
        /// <param name="endDate">Analysis end date</param>
        /// <returns>Performance insights</returns>
        [HttpGet("performance-insights")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierPerformanceInsightsDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierPerformanceInsightsDto>>> GetPerformanceInsights(
            [FromQuery] Guid organizationId,
            [FromQuery] Guid? carrierId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var query = new GetCarrierPerformanceInsightsQuery
            {
                OrganizationId = organizationId,
                CarrierId = carrierId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-90),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get cost analysis
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierId">Optional carrier ID</param>
        /// <param name="startDate">Analysis start date</param>
        /// <param name="endDate">Analysis end date</param>
        /// <returns>Cost analysis data</returns>
        [HttpGet("cost-analysis")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierCostAnalysisDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierCostAnalysisDto>>> GetCostAnalysis(
            [FromQuery] Guid organizationId,
            [FromQuery] Guid? carrierId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var query = new GetCarrierCostAnalysisQuery
            {
                OrganizationId = organizationId,
                CarrierId = carrierId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-90),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get volume analysis
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierId">Optional carrier ID</param>
        /// <param name="startDate">Analysis start date</param>
        /// <param name="endDate">Analysis end date</param>
        /// <returns>Volume analysis data</returns>
        [HttpGet("volume-analysis")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierVolumeAnalysisDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierVolumeAnalysisDto>>> GetVolumeAnalysis(
            [FromQuery] Guid organizationId,
            [FromQuery] Guid? carrierId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var query = new GetCarrierVolumeAnalysisQuery
            {
                OrganizationId = organizationId,
                CarrierId = carrierId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-90),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get carrier benchmarking data
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierIds">List of carrier IDs to benchmark</param>
        /// <param name="benchmarkType">Type of benchmark (Performance, Cost, Service)</param>
        /// <param name="startDate">Benchmark period start date</param>
        /// <param name="endDate">Benchmark period end date</param>
        /// <param name="includeIndustryBenchmarks">Include industry benchmarks</param>
        /// <returns>Benchmarking data</returns>
        [HttpPost("benchmark")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierBenchmarkDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierBenchmarkDto>>> GetCarrierBenchmark([FromBody] CarrierBenchmarkRequest request)
        {
            var query = new GetCarrierBenchmarkQuery
            {
                OrganizationId = request.OrganizationId,
                CarrierIds = request.CarrierIds,
                BenchmarkType = request.BenchmarkType,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                IncludeIndustryBenchmarks = request.IncludeIndustryBenchmarks
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Export analytics data
        /// </summary>
        /// <param name="request">Export request parameters</param>
        /// <returns>Exported data file</returns>
        [HttpPost("export")]
        [ProducesResponseType(typeof(FileResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult> ExportAnalytics([FromBody] AnalyticsExportRequest request)
        {
            var query = new ExportCarrierAnalyticsQuery
            {
                OrganizationId = request.OrganizationId,
                CarrierId = request.CarrierId,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                ExportFormat = request.ExportFormat,
                IncludeCharts = request.IncludeCharts,
                DataTypes = request.DataTypes
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            var contentType = request.ExportFormat.ToLower() switch
            {
                "pdf" => "application/pdf",
                "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "csv" => "text/csv",
                _ => "application/octet-stream"
            };

            return File(result.Data!.FileContent, contentType, result.Data.FileName);
        }
    }

    // Request models
    public class CarrierBenchmarkRequest
    {
        public Guid OrganizationId { get; set; }
        public List<Guid> CarrierIds { get; set; } = new();
        public string BenchmarkType { get; set; } = "Performance";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IncludeIndustryBenchmarks { get; set; } = true;
    }

    public class AnalyticsExportRequest
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string ExportFormat { get; set; } = "PDF"; // PDF, Excel, CSV
        public bool IncludeCharts { get; set; } = true;
        public List<string> DataTypes { get; set; } = new(); // Performance, Cost, Volume, etc.
    }

    // Placeholder query classes
    public class GetCarrierPerformanceInsightsQuery : IRequest<ApiResponseDto<CarrierPerformanceInsightsDto>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class GetCarrierCostAnalysisQuery : IRequest<ApiResponseDto<CarrierCostAnalysisDto>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class GetCarrierVolumeAnalysisQuery : IRequest<ApiResponseDto<CarrierVolumeAnalysisDto>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class GetCarrierBenchmarkQuery : IRequest<ApiResponseDto<CarrierBenchmarkDto>>
    {
        public Guid OrganizationId { get; set; }
        public List<Guid> CarrierIds { get; set; } = new();
        public string BenchmarkType { get; set; } = "Performance";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IncludeIndustryBenchmarks { get; set; } = true;
    }

    public class ExportCarrierAnalyticsQuery : IRequest<ApiResponseDto<AnalyticsExportResultDto>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string ExportFormat { get; set; } = "PDF";
        public bool IncludeCharts { get; set; } = true;
        public List<string> DataTypes { get; set; } = new();
    }

    public class AnalyticsExportResultDto
    {
        public byte[] FileContent { get; set; } = null!;
        public string FileName { get; set; } = null!;
        public string ContentType { get; set; } = null!;
        public long FileSize { get; set; }
    }
}
