using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace CarrierIntegration.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    [Authorize]
    public class CarrierBookingsController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CarrierBookingsController> _logger;

        public CarrierBookingsController(IMediator mediator, ILogger<CarrierBookingsController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all carrier bookings for an organization
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 20, max: 100)</param>
        /// <param name="searchTerm">Search term for booking reference or tracking number</param>
        /// <param name="carrierId">Filter by carrier ID</param>
        /// <param name="status">Filter by booking status</param>
        /// <param name="fromDate">Filter bookings from this date</param>
        /// <param name="toDate">Filter bookings to this date</param>
        /// <returns>Paginated list of carrier bookings</returns>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponseDto<PagedResultDto<CarrierBookingDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<PagedResultDto<CarrierBookingDto>>>> GetCarrierBookings(
            [FromQuery] Guid organizationId,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? searchTerm = null,
            [FromQuery] Guid? carrierId = null,
            [FromQuery] BookingStatus? status = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            var query = new GetCarrierBookingsQuery
            {
                OrganizationId = organizationId,
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchTerm = searchTerm,
                CarrierId = carrierId,
                Status = status,
                FromDate = fromDate,
                ToDate = toDate
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get a specific carrier booking by ID
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <returns>Booking details</returns>
        [HttpGet("{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierBookingDto>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierBookingDto>>> GetCarrierBooking(Guid id)
        {
            var query = new GetCarrierBookingByIdQuery { Id = id };
            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get a carrier booking by reference
        /// </summary>
        /// <param name="reference">Booking reference</param>
        /// <param name="organizationId">Organization ID</param>
        /// <returns>Booking details</returns>
        [HttpGet("by-reference/{reference}")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierBookingDto>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierBookingDto>>> GetCarrierBookingByReference(
            string reference, 
            [FromQuery] Guid organizationId)
        {
            var query = new GetCarrierBookingByReferenceQuery 
            { 
                BookingReference = reference,
                OrganizationId = organizationId
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Create a new carrier booking
        /// </summary>
        /// <param name="command">Booking creation details</param>
        /// <returns>Created booking</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierBookingDto>), 201)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierBookingDto>>> CreateCarrierBooking([FromBody] CreateCarrierBookingCommand command)
        {
            command.CreatedBy = User.Identity?.Name ?? "System";

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return CreatedAtAction(nameof(GetCarrierBooking), new { id = result.Data!.Id }, result);
        }

        /// <summary>
        /// Update an existing carrier booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="command">Booking update details</param>
        /// <returns>Updated booking</returns>
        [HttpPut("{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierBookingDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierBookingDto>>> UpdateCarrierBooking(Guid id, [FromBody] UpdateCarrierBookingCommand command)
        {
            if (id != command.Id)
            {
                return BadRequest(ApiResponseDto<CarrierBookingDto>.ErrorResult("ID mismatch"));
            }

            command.UpdatedBy = User.Identity?.Name ?? "System";

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Confirm a carrier booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Confirmation details</param>
        /// <returns>Success response</returns>
        [HttpPost("{id:guid}/confirm")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> ConfirmCarrierBooking(Guid id, [FromBody] ConfirmBookingRequest request)
        {
            var command = new ConfirmCarrierBookingCommand
            {
                Id = id,
                ConfirmedPickupDate = request.ConfirmedPickupDate,
                ConfirmedDeliveryDate = request.ConfirmedDeliveryDate,
                CarrierBookingReference = request.CarrierBookingReference,
                UpdatedBy = User.Identity?.Name ?? "System"
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Cancel a carrier booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Cancellation details</param>
        /// <returns>Success response</returns>
        [HttpPost("{id:guid}/cancel")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> CancelCarrierBooking(Guid id, [FromBody] CancelBookingRequest request)
        {
            var command = new CancelCarrierBookingCommand
            {
                Id = id,
                Reason = request.Reason,
                UpdatedBy = User.Identity?.Name ?? "System"
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Complete a carrier booking
        /// </summary>
        /// <param name="id">Booking ID</param>
        /// <param name="request">Completion details</param>
        /// <returns>Success response</returns>
        [HttpPost("{id:guid}/complete")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> CompleteCarrierBooking(Guid id, [FromBody] CompleteBookingRequest request)
        {
            var command = new CompleteCarrierBookingCommand
            {
                Id = id,
                CompletionNotes = request.CompletionNotes,
                UpdatedBy = User.Identity?.Name ?? "System"
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get bookings by carrier
        /// </summary>
        /// <param name="carrierId">Carrier ID</param>
        /// <param name="organizationId">Organization ID</param>
        /// <returns>List of bookings for the carrier</returns>
        [HttpGet("by-carrier/{carrierId:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierBookingDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierBookingDto>>>> GetBookingsByCarrier(
            Guid carrierId, 
            [FromQuery] Guid organizationId)
        {
            var query = new GetBookingsByCarrierQuery
            {
                CarrierId = carrierId,
                OrganizationId = organizationId
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get bookings by status
        /// </summary>
        /// <param name="status">Booking status</param>
        /// <param name="organizationId">Organization ID</param>
        /// <returns>List of bookings with the specified status</returns>
        [HttpGet("by-status/{status}")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierBookingDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierBookingDto>>>> GetBookingsByStatus(
            BookingStatus status, 
            [FromQuery] Guid organizationId)
        {
            var query = new GetBookingsByStatusQuery
            {
                Status = status,
                OrganizationId = organizationId
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
    }

    // Request/Response models
    public class ConfirmBookingRequest
    {
        public DateTime? ConfirmedPickupDate { get; set; }
        public DateTime? ConfirmedDeliveryDate { get; set; }
        public string? CarrierBookingReference { get; set; }
    }

    public class CancelBookingRequest
    {
        public string Reason { get; set; } = null!;
    }

    public class CompleteBookingRequest
    {
        public string? CompletionNotes { get; set; }
    }

    // Placeholder command/query classes - these would be implemented in the Application layer
    public class GetCarrierBookingsQuery : IRequest<ApiResponseDto<PagedResultDto<CarrierBookingDto>>>
    {
        public Guid OrganizationId { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public string? SearchTerm { get; set; }
        public Guid? CarrierId { get; set; }
        public BookingStatus? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    public class GetCarrierBookingByIdQuery : IRequest<ApiResponseDto<CarrierBookingDto>>
    {
        public Guid Id { get; set; }
    }

    public class GetCarrierBookingByReferenceQuery : IRequest<ApiResponseDto<CarrierBookingDto>>
    {
        public string BookingReference { get; set; } = null!;
        public Guid OrganizationId { get; set; }
    }

    public class CreateCarrierBookingCommand : IRequest<ApiResponseDto<CarrierBookingDto>>
    {
        public string CreatedBy { get; set; } = null!;
        // Other properties would be added here
    }

    public class UpdateCarrierBookingCommand : IRequest<ApiResponseDto<CarrierBookingDto>>
    {
        public Guid Id { get; set; }
        public string UpdatedBy { get; set; } = null!;
        // Other properties would be added here
    }

    public class ConfirmCarrierBookingCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public DateTime? ConfirmedPickupDate { get; set; }
        public DateTime? ConfirmedDeliveryDate { get; set; }
        public string? CarrierBookingReference { get; set; }
        public string UpdatedBy { get; set; } = null!;
    }

    public class CancelCarrierBookingCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public string Reason { get; set; } = null!;
        public string UpdatedBy { get; set; } = null!;
    }

    public class CompleteCarrierBookingCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public string? CompletionNotes { get; set; }
        public string UpdatedBy { get; set; } = null!;
    }

    public class GetBookingsByCarrierQuery : IRequest<ApiResponseDto<List<CarrierBookingDto>>>
    {
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
    }

    public class GetBookingsByStatusQuery : IRequest<ApiResponseDto<List<CarrierBookingDto>>>
    {
        public BookingStatus Status { get; set; }
        public Guid OrganizationId { get; set; }
    }
}
