using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Application.Features.Optimization.Commands;
using CarrierIntegration.Application.Features.Optimization.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    [Authorize]
    public class CarrierOptimizationController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CarrierOptimizationController> _logger;

        public CarrierOptimizationController(IMediator mediator, ILogger<CarrierOptimizationController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get optimized carrier selection recommendation
        /// </summary>
        /// <param name="request">Carrier optimization request</param>
        /// <returns>Carrier selection recommendation with optimization scores</returns>
        [HttpPost("carrier-selection")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierSelectionRecommendationDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierSelectionRecommendationDto>>> OptimizeCarrierSelection([FromBody] CarrierOptimizationRequest request)
        {
            var command = new OptimizeCarrierSelectionCommand
            {
                OrganizationId = request.OrganizationId,
                OriginAddress = request.OriginAddress,
                DestinationAddress = request.DestinationAddress,
                ShipmentCharacteristics = request.ShipmentCharacteristics,
                OptimizationCriteria = request.OptimizationCriteria,
                PreferredCarriers = request.PreferredCarriers,
                ExcludedCarriers = request.ExcludedCarriers,
                RequiredServiceLevel = request.RequiredServiceLevel,
                MaxCost = request.MaxCost,
                MaxTransitTime = request.MaxTransitTime
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Optimize route for multiple stops
        /// </summary>
        /// <param name="request">Route optimization request</param>
        /// <returns>Optimized route with distance and cost savings</returns>
        [HttpPost("route-optimization")]
        [ProducesResponseType(typeof(ApiResponseDto<RouteOptimizationResultDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<RouteOptimizationResultDto>>> OptimizeRoute([FromBody] RouteOptimizationRequest request)
        {
            var command = new OptimizeRouteCommand
            {
                OrganizationId = request.OrganizationId,
                Stops = request.Stops,
                OptimizationType = request.OptimizationType,
                Constraints = request.Constraints,
                OptimizationGoal = request.OptimizationGoal,
                VehicleConstraints = request.VehicleConstraints
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Optimize costs across all carriers
        /// </summary>
        /// <param name="request">Cost optimization request</param>
        /// <returns>Cost optimization recommendations and potential savings</returns>
        [HttpPost("cost-optimization")]
        [ProducesResponseType(typeof(ApiResponseDto<CostOptimizationResultDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CostOptimizationResultDto>>> OptimizeCosts([FromBody] CostOptimizationRequest request)
        {
            var command = new OptimizeCostCommand
            {
                OrganizationId = request.OrganizationId,
                OptimizationPeriod = request.OptimizationPeriod,
                IncludeCarriers = request.IncludeCarriers,
                ExcludeCarriers = request.ExcludeCarriers,
                OptimizationScope = request.OptimizationScope,
                RiskTolerance = request.RiskTolerance,
                MinimumSavingsThreshold = request.MinimumSavingsThreshold
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Optimize capacity utilization
        /// </summary>
        /// <param name="request">Capacity optimization request</param>
        /// <returns>Capacity optimization recommendations</returns>
        [HttpPost("capacity-optimization")]
        [ProducesResponseType(typeof(ApiResponseDto<CapacityOptimizationResultDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CapacityOptimizationResultDto>>> OptimizeCapacity([FromBody] CapacityOptimizationRequest request)
        {
            var command = new OptimizeCapacityCommand
            {
                OrganizationId = request.OrganizationId,
                OptimizationPeriod = request.OptimizationPeriod,
                ForecastPeriod = request.ForecastPeriod,
                CapacityConstraints = request.CapacityConstraints,
                OptimizationObjective = request.OptimizationObjective
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get smart recommendations based on AI analysis
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="recommendationType">Type of recommendations (All, Cost, Performance, Service)</param>
        /// <param name="priority">Priority filter (High, Medium, Low)</param>
        /// <param name="limit">Maximum number of recommendations to return</param>
        /// <returns>List of smart recommendations</returns>
        [HttpGet("smart-recommendations")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierRecommendationDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierRecommendationDto>>>> GetSmartRecommendations(
            [FromQuery] Guid organizationId,
            [FromQuery] string recommendationType = "All",
            [FromQuery] string? priority = null,
            [FromQuery] int limit = 10)
        {
            var query = new GetSmartRecommendationsQuery
            {
                OrganizationId = organizationId,
                RecommendationType = recommendationType,
                Priority = priority,
                Limit = limit
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get optimization history and results
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="optimizationType">Type of optimization (CarrierSelection, Route, Cost, Capacity)</param>
        /// <param name="startDate">History start date</param>
        /// <param name="endDate">History end date</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Optimization history</returns>
        [HttpGet("history")]
        [ProducesResponseType(typeof(ApiResponseDto<PaginatedListDto<OptimizationHistoryDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<PaginatedListDto<OptimizationHistoryDto>>>> GetOptimizationHistory(
            [FromQuery] Guid organizationId,
            [FromQuery] string? optimizationType = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var query = new GetOptimizationHistoryQuery
            {
                OrganizationId = organizationId,
                OptimizationType = optimizationType,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get optimization performance metrics
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="startDate">Metrics start date</param>
        /// <param name="endDate">Metrics end date</param>
        /// <returns>Optimization performance metrics</returns>
        [HttpGet("performance-metrics")]
        [ProducesResponseType(typeof(ApiResponseDto<OptimizationPerformanceDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<OptimizationPerformanceDto>>> GetOptimizationPerformance(
            [FromQuery] Guid organizationId,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var query = new GetOptimizationPerformanceQuery
            {
                OrganizationId = organizationId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-90),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Run batch optimization for multiple shipments
        /// </summary>
        /// <param name="request">Batch optimization request</param>
        /// <returns>Batch optimization results</returns>
        [HttpPost("batch-optimization")]
        [ProducesResponseType(typeof(ApiResponseDto<BatchOptimizationResultDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<BatchOptimizationResultDto>>> RunBatchOptimization([FromBody] BatchOptimizationRequest request)
        {
            var command = new RunBatchOptimizationCommand
            {
                OrganizationId = request.OrganizationId,
                Shipments = request.Shipments,
                OptimizationCriteria = request.OptimizationCriteria,
                GroupingStrategy = request.GroupingStrategy,
                MaxBatchSize = request.MaxBatchSize,
                OptimizationTimeout = request.OptimizationTimeout
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Validate optimization configuration
        /// </summary>
        /// <param name="request">Optimization configuration to validate</param>
        /// <returns>Validation results</returns>
        [HttpPost("validate-configuration")]
        [ProducesResponseType(typeof(ApiResponseDto<OptimizationValidationResultDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<OptimizationValidationResultDto>>> ValidateOptimizationConfiguration([FromBody] OptimizationConfigurationDto request)
        {
            var command = new ValidateOptimizationConfigurationCommand
            {
                Configuration = request
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
    }

    // Placeholder command and query classes
    public class OptimizeCarrierSelectionCommand : IRequest<ApiResponseDto<CarrierSelectionRecommendationDto>>
    {
        public Guid OrganizationId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public ShipmentCharacteristicsDto ShipmentCharacteristics { get; set; } = null!;
        public OptimizationCriteriaDto OptimizationCriteria { get; set; } = null!;
        public List<Guid>? PreferredCarriers { get; set; }
        public List<Guid>? ExcludedCarriers { get; set; }
        public string? RequiredServiceLevel { get; set; }
        public decimal? MaxCost { get; set; }
        public int? MaxTransitTime { get; set; }
    }

    public class OptimizeRouteCommand : IRequest<ApiResponseDto<RouteOptimizationResultDto>>
    {
        public Guid OrganizationId { get; set; }
        public List<RouteStopDto> Stops { get; set; } = new();
        public string OptimizationType { get; set; } = "TSP";
        public RouteConstraintsDto? Constraints { get; set; }
        public string OptimizationGoal { get; set; } = "Distance";
        public VehicleConstraintsDto? VehicleConstraints { get; set; }
    }

    public class OptimizeCostCommand : IRequest<ApiResponseDto<CostOptimizationResultDto>>
    {
        public Guid OrganizationId { get; set; }
        public DateRangeDto OptimizationPeriod { get; set; } = null!;
        public List<Guid>? IncludeCarriers { get; set; }
        public List<Guid>? ExcludeCarriers { get; set; }
        public string OptimizationScope { get; set; } = "All";
        public string RiskTolerance { get; set; } = "Medium";
        public decimal? MinimumSavingsThreshold { get; set; }
    }

    public class OptimizeCapacityCommand : IRequest<ApiResponseDto<CapacityOptimizationResultDto>>
    {
        public Guid OrganizationId { get; set; }
        public DateRangeDto OptimizationPeriod { get; set; } = null!;
        public int ForecastPeriod { get; set; } = 30;
        public CapacityConstraintsDto? CapacityConstraints { get; set; }
        public string OptimizationObjective { get; set; } = "Utilization";
    }

    public class GetSmartRecommendationsQuery : IRequest<ApiResponseDto<List<CarrierRecommendationDto>>>
    {
        public Guid OrganizationId { get; set; }
        public string RecommendationType { get; set; } = "All";
        public string? Priority { get; set; }
        public int Limit { get; set; } = 10;
    }

    public class GetOptimizationHistoryQuery : IRequest<ApiResponseDto<PaginatedListDto<OptimizationHistoryDto>>>
    {
        public Guid OrganizationId { get; set; }
        public string? OptimizationType { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class GetOptimizationPerformanceQuery : IRequest<ApiResponseDto<OptimizationPerformanceDto>>
    {
        public Guid OrganizationId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class RunBatchOptimizationCommand : IRequest<ApiResponseDto<BatchOptimizationResultDto>>
    {
        public Guid OrganizationId { get; set; }
        public List<ShipmentOptimizationRequestDto> Shipments { get; set; } = new();
        public OptimizationCriteriaDto OptimizationCriteria { get; set; } = null!;
        public string GroupingStrategy { get; set; } = "Route";
        public int MaxBatchSize { get; set; } = 100;
        public TimeSpan OptimizationTimeout { get; set; } = TimeSpan.FromMinutes(5);
    }

    public class ValidateOptimizationConfigurationCommand : IRequest<ApiResponseDto<OptimizationValidationResultDto>>
    {
        public OptimizationConfigurationDto Configuration { get; set; } = null!;
    }

    // Additional DTOs
    public class OptimizationHistoryDto
    {
        public Guid Id { get; set; }
        public string OptimizationType { get; set; } = null!;
        public DateTime ExecutedAt { get; set; }
        public string Status { get; set; } = null!;
        public decimal? SavingsAchieved { get; set; }
        public string? Results { get; set; }
        public TimeSpan ExecutionTime { get; set; }
    }

    public class OptimizationPerformanceDto
    {
        public decimal TotalSavingsAchieved { get; set; }
        public int OptimizationsRun { get; set; }
        public decimal AverageExecutionTime { get; set; }
        public decimal SuccessRate { get; set; }
        public Dictionary<string, decimal> SavingsByType { get; set; } = new();
    }

    public class BatchOptimizationResultDto
    {
        public Guid BatchId { get; set; }
        public int TotalShipments { get; set; }
        public int OptimizedShipments { get; set; }
        public decimal TotalSavings { get; set; }
        public List<ShipmentOptimizationResultDto> Results { get; set; } = new();
        public TimeSpan ProcessingTime { get; set; }
    }

    public class OptimizationValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<string> ValidationErrors { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
        public Dictionary<string, object> RecommendedSettings { get; set; } = new();
    }
}
