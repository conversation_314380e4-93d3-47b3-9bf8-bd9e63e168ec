using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Application.Features.CarrierRates.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    [Authorize]
    public class CarrierRatesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CarrierRatesController> _logger;

        public CarrierRatesController(IMediator mediator, ILogger<CarrierRatesController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get carrier rates for a shipment
        /// </summary>
        /// <param name="request">Rate request details</param>
        /// <returns>List of available rates from different carriers</returns>
        [HttpPost("quote")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierRateDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierRateDto>>>> GetRates([FromBody] GetCarrierRatesQuery request)
        {
            var result = await _mediator.Send(request);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get rates from a specific carrier
        /// </summary>
        /// <param name="carrierId">Carrier ID</param>
        /// <param name="request">Rate request details</param>
        /// <returns>Rates from the specified carrier</returns>
        [HttpPost("quote/carrier/{carrierId:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierRateDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierRateDto>>>> GetRatesFromCarrier(
            Guid carrierId, 
            [FromBody] GetCarrierRatesQuery request)
        {
            // Override the preferred carriers to only include the specified carrier
            request.PreferredCarrierIds = new List<Guid> { carrierId };

            var result = await _mediator.Send(request);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            if (!result.Data?.Any() == true)
            {
                return NotFound(ApiResponseDto<List<CarrierRateDto>>.ErrorResult("No rates available from the specified carrier"));
            }

            return Ok(result);
        }

        /// <summary>
        /// Compare rates across multiple carriers
        /// </summary>
        /// <param name="request">Rate comparison request</param>
        /// <returns>Detailed rate comparison with recommendations</returns>
        [HttpPost("compare")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierComparisonDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierComparisonDto>>>> CompareRates([FromBody] CompareCarrierRatesQuery request)
        {
            var result = await _mediator.Send(request);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get the best rate based on selection criteria
        /// </summary>
        /// <param name="request">Rate request with selection criteria</param>
        /// <returns>Best rate recommendation</returns>
        [HttpPost("best")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierRateDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierRateDto>>> GetBestRate([FromBody] GetBestCarrierRateQuery request)
        {
            var result = await _mediator.Send(request);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            if (result.Data == null)
            {
                return NotFound(ApiResponseDto<CarrierRateDto>.ErrorResult("No suitable rates found"));
            }

            return Ok(result);
        }

        /// <summary>
        /// Get transit time estimates
        /// </summary>
        /// <param name="request">Transit time request</param>
        /// <returns>Transit time estimates from carriers</returns>
        [HttpPost("transit-time")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierTransitTimeDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierTransitTimeDto>>>> GetTransitTimes([FromBody] GetCarrierTransitTimesQuery request)
        {
            var result = await _mediator.Send(request);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Check service availability for a route
        /// </summary>
        /// <param name="request">Service availability request</param>
        /// <returns>Service availability information</returns>
        [HttpPost("service-availability")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierServiceAvailabilityDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierServiceAvailabilityDto>>>> CheckServiceAvailability([FromBody] CheckServiceAvailabilityQuery request)
        {
            var result = await _mediator.Send(request);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Validate shipping addresses
        /// </summary>
        /// <param name="request">Address validation request</param>
        /// <returns>Address validation results</returns>
        [HttpPost("validate-address")]
        [ProducesResponseType(typeof(ApiResponseDto<AddressValidationResultDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<AddressValidationResultDto>>> ValidateAddress([FromBody] ValidateAddressQuery request)
        {
            var result = await _mediator.Send(request);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
    }

    // Placeholder query classes - these would be implemented in the Application layer
    public class CompareCarrierRatesQuery : IRequest<ApiResponseDto<List<CarrierComparisonDto>>>
    {
        public Guid OrganizationId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public List<PackageDto> Packages { get; set; } = new();
        public DateTime ShipDate { get; set; }
        public CarrierSelectionCriteriaDto? SelectionCriteria { get; set; }
        public bool IncludePerformanceData { get; set; } = true;
    }

    public class GetBestCarrierRateQuery : IRequest<ApiResponseDto<CarrierRateDto>>
    {
        public Guid OrganizationId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public List<PackageDto> Packages { get; set; } = new();
        public DateTime ShipDate { get; set; }
        public CarrierSelectionCriteriaDto SelectionCriteria { get; set; } = null!;
        public string OptimizationStrategy { get; set; } = "Cost"; // Cost, Speed, Reliability
    }

    public class GetCarrierTransitTimesQuery : IRequest<ApiResponseDto<List<CarrierTransitTimeDto>>>
    {
        public Guid OrganizationId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public DateTime ShipDate { get; set; }
        public List<Guid>? CarrierIds { get; set; }
        public List<string>? ServiceCodes { get; set; }
    }

    public class CheckServiceAvailabilityQuery : IRequest<ApiResponseDto<List<CarrierServiceAvailabilityDto>>>
    {
        public Guid OrganizationId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public DateTime ShipDate { get; set; }
        public List<Guid>? CarrierIds { get; set; }
        public List<string>? ServiceCodes { get; set; }
        public List<PackageDto>? Packages { get; set; }
    }

    public class ValidateAddressQuery : IRequest<ApiResponseDto<AddressValidationResultDto>>
    {
        public AddressDto Address { get; set; } = null!;
        public List<Guid>? PreferredCarrierIds { get; set; }
        public bool ReturnSuggestions { get; set; } = true;
    }

    // Additional DTOs
    public class CarrierTransitTimeDto
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public string ServiceCode { get; set; } = null!;
        public string ServiceName { get; set; } = null!;
        public int TransitDays { get; set; }
        public DateTime EstimatedDeliveryDate { get; set; }
        public bool IsGuaranteed { get; set; }
        public string? Notes { get; set; }
    }

    public class CarrierServiceAvailabilityDto
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public string ServiceCode { get; set; } = null!;
        public string ServiceName { get; set; } = null!;
        public bool IsAvailable { get; set; }
        public string? UnavailableReason { get; set; }
        public List<string>? Restrictions { get; set; }
        public Dictionary<string, object>? AdditionalInfo { get; set; }
    }

    public class AddressValidationResultDto
    {
        public bool IsValid { get; set; }
        public AddressDto? ValidatedAddress { get; set; }
        public List<AddressDto>? SuggestedAddresses { get; set; }
        public List<string>? ValidationMessages { get; set; }
        public string? ValidationProvider { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }
}
