using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Application.Features.CarrierRelationship.Commands;
using CarrierIntegration.Application.Features.CarrierRelationship.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    [Authorize]
    public class CarrierRelationshipController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CarrierRelationshipController> _logger;

        public CarrierRelationshipController(IMediator mediator, ILogger<CarrierRelationshipController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all carrier contracts with filtering and pagination
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierId">Optional carrier ID filter</param>
        /// <param name="status">Optional status filter</param>
        /// <param name="contractType">Optional contract type filter</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Paginated list of carrier contracts</returns>
        [HttpGet("contracts")]
        [ProducesResponseType(typeof(ApiResponseDto<PaginatedListDto<CarrierContractDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<PaginatedListDto<CarrierContractDto>>>> GetCarrierContracts(
            [FromQuery] Guid organizationId,
            [FromQuery] Guid? carrierId = null,
            [FromQuery] string? status = null,
            [FromQuery] string? contractType = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var query = new GetCarrierContractsQuery
            {
                OrganizationId = organizationId,
                CarrierId = carrierId,
                Status = status,
                ContractType = contractType,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get a specific carrier contract by ID
        /// </summary>
        /// <param name="id">Contract ID</param>
        /// <returns>Carrier contract details</returns>
        [HttpGet("contracts/{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierContractDto>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierContractDto>>> GetCarrierContract(Guid id)
        {
            var query = new GetCarrierContractByIdQuery { Id = id };
            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Create a new carrier contract
        /// </summary>
        /// <param name="command">Contract creation details</param>
        /// <returns>Created carrier contract</returns>
        [HttpPost("contracts")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierContractDto>), 201)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierContractDto>>> CreateCarrierContract([FromBody] CreateCarrierContractCommand command)
        {
            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return CreatedAtAction(
                nameof(GetCarrierContract),
                new { id = result.Data!.Id },
                result);
        }

        /// <summary>
        /// Update an existing carrier contract
        /// </summary>
        /// <param name="id">Contract ID</param>
        /// <param name="command">Updated contract details</param>
        /// <returns>Updated carrier contract</returns>
        [HttpPut("contracts/{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierContractDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierContractDto>>> UpdateCarrierContract(
            Guid id,
            [FromBody] UpdateCarrierContractCommand command)
        {
            if (id != command.Id)
            {
                return BadRequest(ApiResponseDto<CarrierContractDto>.ErrorResult("ID mismatch"));
            }

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Activate a carrier contract
        /// </summary>
        /// <param name="id">Contract ID</param>
        /// <returns>Success status</returns>
        [HttpPost("contracts/{id:guid}/activate")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> ActivateCarrierContract(Guid id)
        {
            var command = new ActivateCarrierContractCommand { Id = id };
            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Terminate a carrier contract
        /// </summary>
        /// <param name="id">Contract ID</param>
        /// <param name="request">Termination details</param>
        /// <returns>Success status</returns>
        [HttpPost("contracts/{id:guid}/terminate")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> TerminateCarrierContract(
            Guid id,
            [FromBody] TerminateContractRequest request)
        {
            var command = new TerminateCarrierContractCommand 
            { 
                Id = id,
                Reason = request.Reason,
                TerminationDate = request.TerminationDate
            };
            
            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get carrier relationship scores
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierId">Optional carrier ID filter</param>
        /// <param name="startDate">Score period start date</param>
        /// <param name="endDate">Score period end date</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Paginated list of relationship scores</returns>
        [HttpGet("relationship-scores")]
        [ProducesResponseType(typeof(ApiResponseDto<PaginatedListDto<CarrierRelationshipScoreDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<PaginatedListDto<CarrierRelationshipScoreDto>>>> GetRelationshipScores(
            [FromQuery] Guid organizationId,
            [FromQuery] Guid? carrierId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var query = new GetCarrierRelationshipScoresQuery
            {
                OrganizationId = organizationId,
                CarrierId = carrierId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-90),
                EndDate = endDate ?? DateTime.UtcNow,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Calculate relationship score for a carrier
        /// </summary>
        /// <param name="request">Score calculation request</param>
        /// <returns>Calculated relationship score</returns>
        [HttpPost("relationship-scores/calculate")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierRelationshipScoreDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierRelationshipScoreDto>>> CalculateRelationshipScore([FromBody] CalculateRelationshipScoreRequest request)
        {
            var command = new CalculateCarrierRelationshipScoreCommand
            {
                CarrierId = request.CarrierId,
                OrganizationId = request.OrganizationId,
                PeriodStart = request.PeriodStart,
                PeriodEnd = request.PeriodEnd,
                CalculationMethod = request.CalculationMethod,
                CustomWeights = request.CustomWeights
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get carrier performance SLA compliance
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierId">Optional carrier ID filter</param>
        /// <param name="startDate">Compliance period start date</param>
        /// <param name="endDate">Compliance period end date</param>
        /// <returns>SLA compliance data</returns>
        [HttpGet("sla-compliance")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierSlaComplianceDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierSlaComplianceDto>>>> GetSlaCompliance(
            [FromQuery] Guid organizationId,
            [FromQuery] Guid? carrierId = null,
            [FromQuery] DateTime? startDate = null,
            [FromQuery] DateTime? endDate = null)
        {
            var query = new GetCarrierSlaComplianceQuery
            {
                OrganizationId = organizationId,
                CarrierId = carrierId,
                StartDate = startDate ?? DateTime.UtcNow.AddDays(-30),
                EndDate = endDate ?? DateTime.UtcNow
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get carrier relationship insights and recommendations
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="carrierId">Optional carrier ID filter</param>
        /// <returns>Relationship insights and recommendations</returns>
        [HttpGet("insights")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierRelationshipInsightsDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierRelationshipInsightsDto>>> GetRelationshipInsights(
            [FromQuery] Guid organizationId,
            [FromQuery] Guid? carrierId = null)
        {
            var query = new GetCarrierRelationshipInsightsQuery
            {
                OrganizationId = organizationId,
                CarrierId = carrierId
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get contract renewal recommendations
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="daysAhead">Days ahead to check for renewals</param>
        /// <returns>Contract renewal recommendations</returns>
        [HttpGet("renewal-recommendations")]
        [ProducesResponseType(typeof(ApiResponseDto<List<ContractRenewalRecommendationDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<ContractRenewalRecommendationDto>>>> GetRenewalRecommendations(
            [FromQuery] Guid organizationId,
            [FromQuery] int daysAhead = 90)
        {
            var query = new GetContractRenewalRecommendationsQuery
            {
                OrganizationId = organizationId,
                DaysAhead = daysAhead
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Generate carrier relationship report
        /// </summary>
        /// <param name="request">Report generation request</param>
        /// <returns>Generated report file</returns>
        [HttpPost("reports/generate")]
        [ProducesResponseType(typeof(FileResult), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult> GenerateRelationshipReport([FromBody] RelationshipReportRequest request)
        {
            var command = new GenerateCarrierRelationshipReportCommand
            {
                OrganizationId = request.OrganizationId,
                CarrierIds = request.CarrierIds,
                ReportType = request.ReportType,
                StartDate = request.StartDate,
                EndDate = request.EndDate,
                IncludeCharts = request.IncludeCharts,
                Format = request.Format
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            var contentType = request.Format.ToLower() switch
            {
                "pdf" => "application/pdf",
                "excel" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                _ => "application/octet-stream"
            };

            return File(result.Data!.FileContent, contentType, result.Data.FileName);
        }
    }

    // Request models
    public class TerminateContractRequest
    {
        public string Reason { get; set; } = null!;
        public DateTime? TerminationDate { get; set; }
    }

    public class CalculateRelationshipScoreRequest
    {
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
        public string CalculationMethod { get; set; } = "Standard";
        public Dictionary<string, decimal>? CustomWeights { get; set; }
    }

    public class RelationshipReportRequest
    {
        public Guid OrganizationId { get; set; }
        public List<Guid>? CarrierIds { get; set; }
        public string ReportType { get; set; } = "Comprehensive";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IncludeCharts { get; set; } = true;
        public string Format { get; set; } = "PDF";
    }

    // Placeholder command and query classes
    public class GetCarrierContractsQuery : IRequest<ApiResponseDto<PaginatedListDto<CarrierContractDto>>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public string? Status { get; set; }
        public string? ContractType { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class GetCarrierContractByIdQuery : IRequest<ApiResponseDto<CarrierContractDto>>
    {
        public Guid Id { get; set; }
    }

    public class CreateCarrierContractCommand : IRequest<ApiResponseDto<CarrierContractDto>>
    {
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
        public string ContractNumber { get; set; } = null!;
        public string ContractName { get; set; } = null!;
        public string ContractType { get; set; } = null!;
        public DateTime EffectiveDate { get; set; }
        public DateTime ExpirationDate { get; set; }
        public string PaymentTerms { get; set; } = null!;
        public int PaymentDays { get; set; }
    }

    public class UpdateCarrierContractCommand : IRequest<ApiResponseDto<CarrierContractDto>>
    {
        public Guid Id { get; set; }
        public string ContractName { get; set; } = null!;
        public string ContractType { get; set; } = null!;
        public DateTime EffectiveDate { get; set; }
        public DateTime ExpirationDate { get; set; }
        public string PaymentTerms { get; set; } = null!;
        public int PaymentDays { get; set; }
    }

    public class ActivateCarrierContractCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
    }

    public class TerminateCarrierContractCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public string Reason { get; set; } = null!;
        public DateTime? TerminationDate { get; set; }
    }

    public class GetCarrierRelationshipScoresQuery : IRequest<ApiResponseDto<PaginatedListDto<CarrierRelationshipScoreDto>>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class CalculateCarrierRelationshipScoreCommand : IRequest<ApiResponseDto<CarrierRelationshipScoreDto>>
    {
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
        public string CalculationMethod { get; set; } = "Standard";
        public Dictionary<string, decimal>? CustomWeights { get; set; }
    }

    public class GetCarrierSlaComplianceQuery : IRequest<ApiResponseDto<List<CarrierSlaComplianceDto>>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
    }

    public class GetCarrierRelationshipInsightsQuery : IRequest<ApiResponseDto<CarrierRelationshipInsightsDto>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
    }

    public class GetContractRenewalRecommendationsQuery : IRequest<ApiResponseDto<List<ContractRenewalRecommendationDto>>>
    {
        public Guid OrganizationId { get; set; }
        public int DaysAhead { get; set; } = 90;
    }

    public class GenerateCarrierRelationshipReportCommand : IRequest<ApiResponseDto<ReportFileDto>>
    {
        public Guid OrganizationId { get; set; }
        public List<Guid>? CarrierIds { get; set; }
        public string ReportType { get; set; } = "Comprehensive";
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public bool IncludeCharts { get; set; } = true;
        public string Format { get; set; } = "PDF";
    }

    public class ReportFileDto
    {
        public byte[] FileContent { get; set; } = null!;
        public string FileName { get; set; } = null!;
        public string ContentType { get; set; } = null!;
        public long FileSize { get; set; }
    }
}
