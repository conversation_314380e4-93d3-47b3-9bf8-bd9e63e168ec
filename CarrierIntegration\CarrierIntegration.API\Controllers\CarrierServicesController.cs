using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Application.Features.CarrierServices.Commands;
using CarrierIntegration.Application.Features.CarrierServices.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    [Authorize]
    public class CarrierServicesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CarrierServicesController> _logger;

        public CarrierServicesController(IMediator mediator, ILogger<CarrierServicesController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all carrier services with optional filtering
        /// </summary>
        /// <param name="carrierId">Filter by carrier ID</param>
        /// <param name="serviceLevel">Filter by service level</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="pageNumber">Page number for pagination</param>
        /// <param name="pageSize">Page size for pagination</param>
        /// <returns>Paginated list of carrier services</returns>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponseDto<PaginatedListDto<CarrierServiceDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<PaginatedListDto<CarrierServiceDto>>>> GetCarrierServices(
            [FromQuery] Guid? carrierId = null,
            [FromQuery] string? serviceLevel = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20)
        {
            var query = new GetCarrierServicesQuery
            {
                CarrierId = carrierId,
                ServiceLevel = serviceLevel,
                IsActive = isActive,
                PageNumber = pageNumber,
                PageSize = pageSize
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get a specific carrier service by ID
        /// </summary>
        /// <param name="id">Carrier service ID</param>
        /// <returns>Carrier service details</returns>
        [HttpGet("{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierServiceDto>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierServiceDto>>> GetCarrierService(Guid id)
        {
            var query = new GetCarrierServiceByIdQuery { Id = id };
            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get carrier services by carrier ID
        /// </summary>
        /// <param name="carrierId">Carrier ID</param>
        /// <param name="isActive">Filter by active status</param>
        /// <returns>List of carrier services for the specified carrier</returns>
        [HttpGet("carrier/{carrierId:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierServiceDto>>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierServiceDto>>>> GetCarrierServicesByCarrier(
            Guid carrierId,
            [FromQuery] bool? isActive = null)
        {
            var query = new GetCarrierServicesByCarrierQuery 
            { 
                CarrierId = carrierId,
                IsActive = isActive
            };
            
            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Create a new carrier service
        /// </summary>
        /// <param name="command">Carrier service creation details</param>
        /// <returns>Created carrier service</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierServiceDto>), 201)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierServiceDto>>> CreateCarrierService([FromBody] CreateCarrierServiceCommand command)
        {
            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return CreatedAtAction(
                nameof(GetCarrierService),
                new { id = result.Data!.Id },
                result);
        }

        /// <summary>
        /// Update an existing carrier service
        /// </summary>
        /// <param name="id">Carrier service ID</param>
        /// <param name="command">Updated carrier service details</param>
        /// <returns>Updated carrier service</returns>
        [HttpPut("{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierServiceDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierServiceDto>>> UpdateCarrierService(
            Guid id,
            [FromBody] UpdateCarrierServiceCommand command)
        {
            if (id != command.Id)
            {
                return BadRequest(ApiResponseDto<CarrierServiceDto>.ErrorResult("ID mismatch"));
            }

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Activate a carrier service
        /// </summary>
        /// <param name="id">Carrier service ID</param>
        /// <returns>Success status</returns>
        [HttpPost("{id:guid}/activate")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> ActivateCarrierService(Guid id)
        {
            var command = new ActivateCarrierServiceCommand { Id = id };
            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Deactivate a carrier service
        /// </summary>
        /// <param name="id">Carrier service ID</param>
        /// <param name="reason">Deactivation reason</param>
        /// <returns>Success status</returns>
        [HttpPost("{id:guid}/deactivate")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> DeactivateCarrierService(
            Guid id,
            [FromBody] DeactivateCarrierServiceRequest request)
        {
            var command = new DeactivateCarrierServiceCommand 
            { 
                Id = id,
                Reason = request.Reason
            };
            
            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Delete a carrier service
        /// </summary>
        /// <param name="id">Carrier service ID</param>
        /// <returns>Success status</returns>
        [HttpDelete("{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> DeleteCarrierService(Guid id)
        {
            var command = new DeleteCarrierServiceCommand { Id = id };
            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Check service availability for a route
        /// </summary>
        /// <param name="request">Service availability check request</param>
        /// <returns>Service availability information</returns>
        [HttpPost("check-availability")]
        [ProducesResponseType(typeof(ApiResponseDto<List<ServiceAvailabilityDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<ServiceAvailabilityDto>>>> CheckServiceAvailability(
            [FromBody] CheckServiceAvailabilityRequest request)
        {
            var query = new CheckServiceAvailabilityQuery
            {
                CarrierId = request.CarrierId,
                OriginAddress = request.OriginAddress,
                DestinationAddress = request.DestinationAddress,
                ShipDate = request.ShipDate,
                Packages = request.Packages
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
    }

    // Request/Response models
    public class DeactivateCarrierServiceRequest
    {
        public string Reason { get; set; } = null!;
    }

    public class CheckServiceAvailabilityRequest
    {
        public Guid? CarrierId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public DateTime ShipDate { get; set; }
        public List<PackageDto> Packages { get; set; } = new();
    }

    public class ServiceAvailabilityDto
    {
        public Guid ServiceId { get; set; }
        public string ServiceCode { get; set; } = null!;
        public string ServiceName { get; set; } = null!;
        public string CarrierName { get; set; } = null!;
        public bool IsAvailable { get; set; }
        public string? UnavailableReason { get; set; }
        public int? EstimatedTransitDays { get; set; }
        public DateTime? EstimatedDeliveryDate { get; set; }
        public List<string>? Restrictions { get; set; }
        public Dictionary<string, object>? AdditionalInfo { get; set; }
    }

    // Placeholder command and query classes
    public class GetCarrierServicesQuery : IRequest<ApiResponseDto<PaginatedListDto<CarrierServiceDto>>>
    {
        public Guid? CarrierId { get; set; }
        public string? ServiceLevel { get; set; }
        public bool? IsActive { get; set; }
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }

    public class GetCarrierServiceByIdQuery : IRequest<ApiResponseDto<CarrierServiceDto>>
    {
        public Guid Id { get; set; }
    }

    public class GetCarrierServicesByCarrierQuery : IRequest<ApiResponseDto<List<CarrierServiceDto>>>
    {
        public Guid CarrierId { get; set; }
        public bool? IsActive { get; set; }
    }

    public class CreateCarrierServiceCommand : IRequest<ApiResponseDto<CarrierServiceDto>>
    {
        public Guid CarrierId { get; set; }
        public string ServiceCode { get; set; } = null!;
        public string ServiceName { get; set; } = null!;
        public string ServiceLevel { get; set; } = null!;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
    }

    public class UpdateCarrierServiceCommand : IRequest<ApiResponseDto<CarrierServiceDto>>
    {
        public Guid Id { get; set; }
        public string ServiceName { get; set; } = null!;
        public string? Description { get; set; }
        public bool IsActive { get; set; }
    }

    public class ActivateCarrierServiceCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
    }

    public class DeactivateCarrierServiceCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public string Reason { get; set; } = null!;
    }

    public class DeleteCarrierServiceCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
    }

    public class CheckServiceAvailabilityQuery : IRequest<ApiResponseDto<List<ServiceAvailabilityDto>>>
    {
        public Guid? CarrierId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public DateTime ShipDate { get; set; }
        public List<PackageDto> Packages { get; set; } = new();
    }
}
