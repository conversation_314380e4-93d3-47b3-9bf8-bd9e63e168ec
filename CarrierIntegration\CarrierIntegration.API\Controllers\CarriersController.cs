using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Application.Features.Carriers.Commands;
using CarrierIntegration.Application.Features.Carriers.Queries;
using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Enums;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace CarrierIntegration.API.Controllers
{
    [ApiController]
    [Route("api/v1/[controller]")]
    [Authorize]
    public class CarriersController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<CarriersController> _logger;

        public CarriersController(IMediator mediator, ILogger<CarriersController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all carriers for an organization
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 20, max: 100)</param>
        /// <param name="searchTerm">Search term for name, code, or description</param>
        /// <param name="type">Filter by carrier type</param>
        /// <param name="status">Filter by carrier status</param>
        /// <param name="isActive">Filter by active status</param>
        /// <param name="supportsTracking">Filter by tracking support</param>
        /// <param name="supportsRating">Filter by rating support</param>
        /// <param name="supportsLabeling">Filter by labeling support</param>
        /// <param name="supportsPickup">Filter by pickup support</param>
        /// <param name="supportsInternational">Filter by international support</param>
        /// <returns>Paginated list of carriers</returns>
        [HttpGet]
        [ProducesResponseType(typeof(ApiResponseDto<PagedResultDto<CarrierDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<PagedResultDto<CarrierDto>>>> GetCarriers(
            [FromQuery] Guid organizationId,
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? searchTerm = null,
            [FromQuery] CarrierType? type = null,
            [FromQuery] CarrierStatus? status = null,
            [FromQuery] bool? isActive = null,
            [FromQuery] bool? supportsTracking = null,
            [FromQuery] bool? supportsRating = null,
            [FromQuery] bool? supportsLabeling = null,
            [FromQuery] bool? supportsPickup = null,
            [FromQuery] bool? supportsInternational = null)
        {
            var query = new GetCarriersQuery
            {
                OrganizationId = organizationId,
                PageNumber = pageNumber,
                PageSize = pageSize,
                SearchTerm = searchTerm,
                Type = type,
                Status = status,
                IsActive = isActive,
                SupportsTracking = supportsTracking,
                SupportsRating = supportsRating,
                SupportsLabeling = supportsLabeling,
                SupportsPickup = supportsPickup,
                SupportsInternational = supportsInternational
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get a specific carrier by ID
        /// </summary>
        /// <param name="id">Carrier ID</param>
        /// <returns>Carrier details</returns>
        [HttpGet("{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierDto>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierDto>>> GetCarrier(Guid id)
        {
            var query = new GetCarrierByIdQuery { Id = id };
            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Create a new carrier
        /// </summary>
        /// <param name="command">Carrier creation details</param>
        /// <returns>Created carrier</returns>
        [HttpPost]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierDto>), 201)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierDto>>> CreateCarrier([FromBody] CreateCarrierCommand command)
        {
            // Set the created by from the current user context
            command.CreatedBy = User.Identity?.Name ?? "System";

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return CreatedAtAction(nameof(GetCarrier), new { id = result.Data!.Id }, result);
        }

        /// <summary>
        /// Update an existing carrier
        /// </summary>
        /// <param name="id">Carrier ID</param>
        /// <param name="command">Carrier update details</param>
        /// <returns>Updated carrier</returns>
        [HttpPut("{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierDto>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierDto>>> UpdateCarrier(Guid id, [FromBody] UpdateCarrierCommand command)
        {
            if (id != command.Id)
            {
                return BadRequest(ApiResponseDto<CarrierDto>.ErrorResult("ID mismatch"));
            }

            // Set the updated by from the current user context
            command.UpdatedBy = User.Identity?.Name ?? "System";

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Delete a carrier (soft delete)
        /// </summary>
        /// <param name="id">Carrier ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("{id:guid}")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> DeleteCarrier(Guid id)
        {
            var command = new DeleteCarrierCommand
            {
                Id = id,
                DeletedBy = User.Identity?.Name ?? "System"
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Activate a carrier
        /// </summary>
        /// <param name="id">Carrier ID</param>
        /// <returns>Success response</returns>
        [HttpPost("{id:guid}/activate")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> ActivateCarrier(Guid id)
        {
            var command = new ActivateCarrierCommand
            {
                Id = id,
                UpdatedBy = User.Identity?.Name ?? "System"
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Deactivate a carrier
        /// </summary>
        /// <param name="id">Carrier ID</param>
        /// <param name="request">Deactivation request with reason</param>
        /// <returns>Success response</returns>
        [HttpPost("{id:guid}/deactivate")]
        [ProducesResponseType(typeof(ApiResponseDto<bool>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<bool>>> DeactivateCarrier(Guid id, [FromBody] DeactivateCarrierRequest request)
        {
            var command = new DeactivateCarrierCommand
            {
                Id = id,
                Reason = request.Reason,
                UpdatedBy = User.Identity?.Name ?? "System"
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Perform health check on a carrier
        /// </summary>
        /// <param name="id">Carrier ID</param>
        /// <returns>Health check result</returns>
        [HttpPost("{id:guid}/health-check")]
        [ProducesResponseType(typeof(ApiResponseDto<CarrierHealthCheckResult>), 200)]
        [ProducesResponseType(404)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<CarrierHealthCheckResult>>> PerformHealthCheck(Guid id)
        {
            var command = new PerformCarrierHealthCheckCommand
            {
                CarrierId = id,
                UpdatedBy = User.Identity?.Name ?? "System"
            };

            var result = await _mediator.Send(command);

            if (!result.Success)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get active carriers for an organization
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <returns>List of active carriers</returns>
        [HttpGet("active")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierDto>>>> GetActiveCarriers([FromQuery] Guid organizationId)
        {
            var query = new GetActiveCarriersQuery { OrganizationId = organizationId };
            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Get carriers by type
        /// </summary>
        /// <param name="organizationId">Organization ID</param>
        /// <param name="type">Carrier type</param>
        /// <returns>List of carriers of the specified type</returns>
        [HttpGet("by-type/{type}")]
        [ProducesResponseType(typeof(ApiResponseDto<List<CarrierDto>>), 200)]
        [ProducesResponseType(400)]
        [ProducesResponseType(401)]
        [ProducesResponseType(500)]
        public async Task<ActionResult<ApiResponseDto<List<CarrierDto>>>> GetCarriersByType(
            [FromQuery] Guid organizationId,
            CarrierType type)
        {
            var query = new GetCarriersByTypeQuery
            {
                OrganizationId = organizationId,
                Type = type
            };

            var result = await _mediator.Send(query);

            if (!result.Success)
            {
                return BadRequest(result);
            }

            return Ok(result);
        }
    }

    public class DeactivateCarrierRequest
    {
        public string Reason { get; set; } = null!;
    }

    // Placeholder command classes - these would be implemented in the Application layer
    public class GetCarrierByIdQuery : IRequest<ApiResponseDto<CarrierDto>>
    {
        public Guid Id { get; set; }
    }

    public class UpdateCarrierCommand : IRequest<ApiResponseDto<CarrierDto>>
    {
        public Guid Id { get; set; }
        public string UpdatedBy { get; set; } = null!;
        // Other properties would be added here
    }

    public class DeleteCarrierCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public string DeletedBy { get; set; } = null!;
    }

    public class ActivateCarrierCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public string UpdatedBy { get; set; } = null!;
    }

    public class DeactivateCarrierCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public string Reason { get; set; } = null!;
        public string UpdatedBy { get; set; } = null!;
    }

    public class PerformCarrierHealthCheckCommand : IRequest<ApiResponseDto<CarrierHealthCheckResult>>
    {
        public Guid CarrierId { get; set; }
        public string UpdatedBy { get; set; } = null!;
    }

    public class GetActiveCarriersQuery : IRequest<ApiResponseDto<List<CarrierDto>>>
    {
        public Guid OrganizationId { get; set; }
    }

    public class GetCarriersByTypeQuery : IRequest<ApiResponseDto<List<CarrierDto>>>
    {
        public Guid OrganizationId { get; set; }
        public CarrierType Type { get; set; }
    }
}
