{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=TriTrackzCarrierIntegration;Username=timescale;Password=timescale", "Redis": "localhost:6379"}, "IdentityApi": {"BaseUrl": "https://localhost:7001/api/v1"}, "UserManagementApi": {"BaseUrl": "https://localhost:7002/api/v1"}, "MasterManagementApi": {"BaseUrl": "https://localhost:7003/api/v1"}, "CustomerManagementApi": {"BaseUrl": "https://localhost:7004/api/v1"}, "ShipperManagementApi": {"BaseUrl": "https://localhost:7006/api/v1"}, "OrderManagementApi": {"BaseUrl": "https://localhost:7007/api/v1"}, "ShipmentManagementApi": {"BaseUrl": "https://localhost:7008/api/v1"}, "PricingManagementApi": {"BaseUrl": "https://localhost:7009/api/v1"}, "ApiGateway": {"Url": "https://localhost:7000"}, "JwtSettings": {"Secret": "ThisIsAVerySecureKeyThatShouldBeStoredInASecureVault", "Issuer": "TriTrackzIdentity", "Audience": "TriTrackzServices"}, "RabbitMQ": {"Host": "localhost"}, "CarrierApiSettings": {"UPS": {"BaseUrl": "https://onlinetools.ups.com/api", "TestBaseUrl": "https://wwwcie.ups.com/api", "ApiVersion": "v1", "TimeoutSeconds": 30, "RetryAttempts": 3, "RateLimitPerMinute": 100}, "FedEx": {"BaseUrl": "https://apis.fedex.com", "TestBaseUrl": "https://apis-sandbox.fedex.com", "ApiVersion": "v1", "TimeoutSeconds": 30, "RetryAttempts": 3, "RateLimitPerMinute": 100}, "USPS": {"BaseUrl": "https://api.usps.com", "TestBaseUrl": "https://api-cat.usps.com", "ApiVersion": "v3", "TimeoutSeconds": 30, "RetryAttempts": 3, "RateLimitPerMinute": 100}, "DHL": {"BaseUrl": "https://api-eu.dhl.com", "TestBaseUrl": "https://api-test.dhl.com", "ApiVersion": "v1", "TimeoutSeconds": 30, "RetryAttempts": 3, "RateLimitPerMinute": 100}}, "FeatureFlags": {"EnableCaching": true, "EnableRealTimeTracking": true, "EnableCarrierHealthChecks": true, "EnablePerformanceMetrics": true, "EnableEventPublishing": true, "EnableRateLimiting": true, "EnableCarrierFailover": true, "EnableAdvancedRouting": true}, "BusinessRules": {"MaxBookingsPerCarrier": 1000, "MaxRetryAttempts": 5, "BookingExpirationHours": 24, "HealthCheckIntervalMinutes": 15, "DefaultTimeoutSeconds": 30, "MaxConcurrentApiCalls": 10, "CarrierSelectionTimeoutSeconds": 10, "DefaultTransitDays": 5}, "ExternalServices": {"AddressValidationProvider": "Google", "GoogleMapsApiKey": "your-google-maps-api-key", "WeatherApiUrl": "https://api.openweathermap.org/data/2.5/", "WeatherApiKey": "your-weather-api-key", "CurrencyApiUrl": "https://api.exchangerate-api.com/v4/latest/", "TrafficApiUrl": "https://api.tomtom.com/traffic/services/4/", "TrafficApiKey": "your-traffic-api-key"}, "ApplicationInsights": {"ConnectionString": "InstrumentationKey=your-instrumentation-key-here"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning", "CarrierIntegration": "Information"}}, "AllowedHosts": "*"}