using MediatR;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application
{
    public class LoggingBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
        where TRequest : IRequest<TResponse>
    {
        private readonly ILogger<LoggingBehavior<TRequest, TResponse>> _logger;

        public LoggingBehavior(ILogger<LoggingBehavior<TRequest, TResponse>> logger)
        {
            _logger = logger;
        }

        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            var requestName = typeof(TRequest).Name;
            var requestGuid = System.Guid.NewGuid().ToString();

            _logger.LogInformation("Starting request {RequestName} ({RequestGuid})", requestName, requestGuid);

            var stopwatch = Stopwatch.StartNew();

            try
            {
                var response = await next();

                stopwatch.Stop();
                _logger.LogInformation("Completed request {RequestName} ({RequestGuid}) in {ElapsedMilliseconds}ms",
                    requestName, requestGuid, stopwatch.ElapsedMilliseconds);

                return response;
            }
            catch (System.Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Request {RequestName} ({RequestGuid}) failed after {ElapsedMilliseconds}ms",
                    requestName, requestGuid, stopwatch.ElapsedMilliseconds);
                throw;
            }
        }
    }
}
