using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Application.Features.Analytics.Queries;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Common.Interfaces
{
    public interface ICarrierAnalyticsService
    {
        Task<CarrierAnalyticsDto> GenerateAnalyticsAsync(GetCarrierAnalyticsQuery request);
        Task<List<PredictionDto>> GeneratePredictionsAsync(GetCarrierAnalyticsQuery request);
        Task<List<ComparisonDto>> GenerateComparisonsAsync(GetCarrierAnalyticsQuery request);
        Task<CarrierPerformanceInsightsDto> GeneratePerformanceInsightsAsync(Guid organizationId, Guid? carrierId, DateTime startDate, DateTime endDate);
        Task<CarrierCostAnalysisDto> GenerateCostAnalysisAsync(Guid organizationId, Guid? carrierId, DateTime startDate, DateTime endDate);
        Task<CarrierVolumeAnalysisDto> GenerateVolumeAnalysisAsync(Guid organizationId, Guid? carrierId, DateTime startDate, DateTime endDate);
    }

    public interface ICarrierDashboardService
    {
        Task<CarrierDashboardDto> GenerateDashboardAsync(GetCarrierDashboardQuery request);
        Task<List<DashboardWidgetDto>> GetWidgetsAsync(string dashboardType, Guid organizationId);
        Task<List<AlertDto>> GetDashboardAlertsAsync(Guid organizationId);
        Task<object> GetTrendsDataAsync(Guid organizationId, string dashboardType);
    }

    public interface IPredictiveAnalyticsService
    {
        Task<PredictiveAnalyticsDto> GeneratePredictionsAsync(GetPredictiveAnalyticsQuery request);
        Task<List<ForecastDataPointDto>> ForecastMetricAsync(string metricName, Guid organizationId, Guid? carrierId, int forecastDays);
        Task<List<ScenarioDto>> GenerateScenariosAsync(Guid organizationId, string predictionType);
        Task<object> GetModelMetricsAsync(string predictionType);
    }

    public interface ICachingService
    {
        Task<T?> GetAsync<T>(string key) where T : class;
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        Task RemoveAsync(string key);
        Task RemoveByPatternAsync(string pattern);
        string GenerateKey(string prefix, params object[] identifiers);
    }

    public interface ICarrierOptimizationEngine
    {
        Task<CarrierSelectionRecommendationDto> OptimizeCarrierSelectionAsync(CarrierOptimizationRequest request);
        Task<RouteOptimizationResultDto> OptimizeRouteAsync(RouteOptimizationRequest request);
        Task<CostOptimizationResultDto> OptimizeCostAsync(CostOptimizationRequest request);
        Task<CapacityOptimizationResultDto> OptimizeCapacityAsync(CapacityOptimizationRequest request);
        Task<List<CarrierRecommendationDto>> GetSmartRecommendationsAsync(SmartRecommendationRequest request);
    }

    public interface IMachineLearningService
    {
        Task<List<CarrierPerformancePredictionDto>> PredictCarrierPerformanceAsync(CarrierPerformancePredictionRequest request);
        Task<CapacityForecastDto> PredictCapacityNeedsAsync(CapacityPredictionRequest request);
        Task<List<PersonalizedRecommendationDto>> GeneratePersonalizedRecommendationsAsync(PersonalizedRecommendationRequest request);
        Task<CarrierRiskAssessmentDto> AssessCarrierRiskAsync(Guid carrierId, Guid organizationId);
    }

    public interface ICarrierSpecificApiService
    {
        string CarrierCode { get; }
        Task<CarrierApiResponse<List<CarrierRateDto>>> GetRatesAsync(CarrierRateRequest request);
        Task<CarrierApiResponse<CarrierBookingResponseDto>> CreateBookingAsync(CarrierBookingRequestDto request);
        Task<CarrierApiResponse<CarrierTrackingDto>> GetTrackingAsync(string trackingNumber);
        Task<CarrierApiResponse<bool>> CancelBookingAsync(string bookingReference, string reason);
        Task<CarrierApiResponse<bool>> ValidateAddressAsync(AddressDto address);
    }

    public interface IEventPublishingService
    {
        Task PublishDomainEventAsync<T>(T domainEvent) where T : Domain.Common.IDomainEvent;
        Task PublishIntegrationEventAsync<T>(T integrationEvent) where T : class;
        Task PublishCarrierEventAsync(string eventType, object eventData, Guid carrierId, Guid organizationId);
    }
}

// Supporting DTOs for the interfaces
namespace CarrierIntegration.Application.DTOs
{
    public class CarrierOptimizationRequest
    {
        public Guid OrganizationId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public ShipmentCharacteristicsDto ShipmentCharacteristics { get; set; } = null!;
        public OptimizationCriteriaDto OptimizationCriteria { get; set; } = null!;
        public List<Guid>? PreferredCarriers { get; set; }
        public List<Guid>? ExcludedCarriers { get; set; }
        public string? RequiredServiceLevel { get; set; }
        public decimal? MaxCost { get; set; }
        public int? MaxTransitTime { get; set; }
    }

    public class RouteOptimizationRequest
    {
        public Guid OrganizationId { get; set; }
        public List<RouteStopDto> Stops { get; set; } = new();
        public string OptimizationType { get; set; } = "TSP";
        public RouteConstraintsDto? Constraints { get; set; }
        public string OptimizationGoal { get; set; } = "Distance";
        public VehicleConstraintsDto? VehicleConstraints { get; set; }
    }

    public class CostOptimizationRequest
    {
        public Guid OrganizationId { get; set; }
        public DateRangeDto OptimizationPeriod { get; set; } = null!;
        public List<Guid>? IncludeCarriers { get; set; }
        public List<Guid>? ExcludeCarriers { get; set; }
        public string OptimizationScope { get; set; } = "All";
        public string RiskTolerance { get; set; } = "Medium";
        public decimal? MinimumSavingsThreshold { get; set; }
    }

    public class CapacityOptimizationRequest
    {
        public Guid OrganizationId { get; set; }
        public DateRangeDto OptimizationPeriod { get; set; } = null!;
        public int ForecastPeriod { get; set; } = 30;
        public CapacityConstraintsDto? CapacityConstraints { get; set; }
        public string OptimizationObjective { get; set; } = "Utilization";
    }

    public class SmartRecommendationRequest
    {
        public Guid OrganizationId { get; set; }
        public string RecommendationType { get; set; } = "All";
        public string? Priority { get; set; }
        public int Limit { get; set; } = 10;
    }

    public class ShipmentCharacteristicsDto
    {
        public WeightDto Weight { get; set; } = null!;
        public DimensionsDto? Dimensions { get; set; }
        public int PackageCount { get; set; }
        public string? CommodityType { get; set; }
        public bool IsHazmat { get; set; }
        public bool RequiresSignature { get; set; }
        public bool IsResidential { get; set; }
    }

    public class OptimizationCriteriaDto
    {
        public string PrimaryObjective { get; set; } = "Cost";
        public Dictionary<string, decimal>? CustomWeights { get; set; }
        public List<string>? Constraints { get; set; }
        public decimal? MaxCostThreshold { get; set; }
        public int? MaxTransitTimeThreshold { get; set; }
    }

    public class RouteStopDto
    {
        public Guid Id { get; set; }
        public AddressDto Address { get; set; } = null!;
        public string StopType { get; set; } = null!; // Pickup, Delivery
        public TimeSpan? TimeWindow { get; set; }
        public int Priority { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    public class RouteConstraintsDto
    {
        public TimeSpan? MaxRouteTime { get; set; }
        public decimal? MaxRouteDistance { get; set; }
        public List<string>? AvoidAreas { get; set; }
        public Dictionary<string, object>? CustomConstraints { get; set; }
    }

    public class VehicleConstraintsDto
    {
        public decimal? MaxWeight { get; set; }
        public DimensionsDto? MaxDimensions { get; set; }
        public string? VehicleType { get; set; }
        public List<string>? Capabilities { get; set; }
    }

    public class CapacityConstraintsDto
    {
        public decimal? MaxCapacity { get; set; }
        public decimal? MinUtilization { get; set; }
        public List<string>? ServiceTypes { get; set; }
        public Dictionary<string, object>? CustomConstraints { get; set; }
    }

    // Response DTOs
    public class CarrierSelectionRecommendationDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public Guid RequestId { get; set; }
        public OptimizationCriteriaDto OptimizationCriteria { get; set; } = null!;
        public CarrierOptionDto RecommendedCarrier { get; set; } = null!;
        public List<CarrierOptionDto> AlternativeCarriers { get; set; } = new();
        public decimal OptimizationScore { get; set; }
        public decimal EstimatedSavings { get; set; }
        public decimal ConfidenceLevel { get; set; }
        public string Reasoning { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
    }

    public class RouteOptimizationResultDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public Guid RequestId { get; set; }
        public List<RouteStopDto> OriginalRoute { get; set; } = new();
        public List<RouteStopDto> OptimizedRoute { get; set; } = new();
        public decimal OriginalDistance { get; set; }
        public decimal OptimizedDistance { get; set; }
        public decimal DistanceSaved { get; set; }
        public decimal PercentImprovement { get; set; }
        public decimal OriginalCost { get; set; }
        public decimal OptimizedCost { get; set; }
        public decimal CostSaved { get; set; }
        public TimeSpan EstimatedTimeReduction { get; set; }
        public EnvironmentalImpactDto EnvironmentalImpact { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
    }

    public class CostOptimizationResultDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public Guid RequestId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateRangeDto OptimizationPeriod { get; set; } = null!;
        public CurrentSpendingAnalysisDto CurrentSpending { get; set; } = null!;
        public List<CostOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();
        public decimal PotentialSavings { get; set; }
        public string ImplementationComplexity { get; set; } = null!;
        public RiskAssessmentDto RiskAssessment { get; set; } = null!;
        public List<RecommendationDto> Recommendations { get; set; } = new();
        public ImplementationPlanDto ImplementationPlan { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
    }

    public class CapacityOptimizationResultDto
    {
        public bool Success { get; set; }
        public string? Message { get; set; }
        public Guid RequestId { get; set; }
        public Guid OrganizationId { get; set; }
        public DateRangeDto OptimizationPeriod { get; set; } = null!;
        public CapacityUtilizationAnalysisDto CurrentUtilization { get; set; } = null!;
        public CapacityForecastDto CapacityForecast { get; set; } = null!;
        public List<CapacityOptimizationOpportunityDto> OptimizationOpportunities { get; set; } = new();
        public List<RecommendationDto> Recommendations { get; set; } = new();
        public ExpectedImpactDto ExpectedImpact { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
    }

    public class CarrierRecommendationDto
    {
        public string RecommendationType { get; set; } = null!;
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public string ExpectedBenefit { get; set; } = null!;
        public decimal ConfidenceScore { get; set; }
        public string Priority { get; set; } = null!;
        public string Category { get; set; } = null!;
        public string ActionRequired { get; set; } = null!;
        public string EstimatedImpact { get; set; } = null!;
        public string ImplementationEffort { get; set; } = null!;
        public DateTime GeneratedAt { get; set; }
    }

    public class CarrierOptionDto
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public string ServiceCode { get; set; } = null!;
        public string ServiceName { get; set; } = null!;
        public decimal EstimatedCost { get; set; }
        public int EstimatedTransitDays { get; set; }
        public decimal OptimizationScore { get; set; }
        public bool IsRecommended { get; set; }
        public Dictionary<string, object>? Metadata { get; set; }
    }

    public class EnvironmentalImpactDto
    {
        public decimal CO2Reduced { get; set; }
        public decimal FuelSaved { get; set; }
    }

    // Additional supporting DTOs
    public class CurrentSpendingAnalysisDto
    {
        public decimal TotalSpend { get; set; }
        public Dictionary<string, decimal> SpendByCarrier { get; set; } = new();
        public Dictionary<string, decimal> SpendByService { get; set; } = new();
        public List<TrendDataDto> SpendTrends { get; set; } = new();
    }

    public class RiskAssessmentDto
    {
        public string OverallRisk { get; set; } = null!;
        public List<string> RiskFactors { get; set; } = new();
        public List<string> MitigationStrategies { get; set; } = new();
    }

    public class ImplementationPlanDto
    {
        public List<string> Phases { get; set; } = new();
        public Dictionary<string, TimeSpan> Timeline { get; set; } = new();
        public List<string> Prerequisites { get; set; } = new();
    }

    public class CapacityUtilizationAnalysisDto
    {
        public decimal CurrentUtilization { get; set; }
        public Dictionary<string, decimal> UtilizationByCarrier { get; set; } = new();
        public List<TrendDataDto> UtilizationTrends { get; set; } = new();
        public object? HistoricalData { get; set; }
    }

    public class CapacityForecastDto
    {
        public List<ForecastDataPointDto> Forecast { get; set; } = new();
        public decimal PredictedPeakCapacity { get; set; }
        public DateTime PeakCapacityDate { get; set; }
        public List<string> Recommendations { get; set; } = new();
    }

    public class CapacityOptimizationOpportunityDto
    {
        public string Type { get; set; } = null!;
        public string Description { get; set; } = null!;
        public decimal ExpectedImprovement { get; set; }
        public string Implementation { get; set; } = null!;
    }

    public class ExpectedImpactDto
    {
        public decimal UtilizationImprovement { get; set; }
        public decimal CostReduction { get; set; }
        public decimal CapacityIncrease { get; set; }
        public TimeSpan ImplementationTime { get; set; }
    }

    // ML Service DTOs
    public class CarrierPerformancePredictionRequest
    {
        public List<CarrierOptionDto> Carriers { get; set; } = new();
        public RouteDto Route { get; set; } = null!;
        public ShipmentCharacteristicsDto ShipmentCharacteristics { get; set; } = null!;
        public List<CarrierPerformanceDataDto> HistoricalData { get; set; } = new();
    }

    public class CarrierPerformancePredictionDto
    {
        public Guid CarrierId { get; set; }
        public decimal PredictedOnTimeRate { get; set; }
        public decimal PredictedQualityScore { get; set; }
        public decimal PredictedCost { get; set; }
        public decimal ConfidenceLevel { get; set; }
    }

    public class CapacityPredictionRequest
    {
        public Guid OrganizationId { get; set; }
        public int ForecastPeriod { get; set; }
        public object? HistoricalData { get; set; }
    }

    public class PersonalizedRecommendationRequest
    {
        public Guid OrganizationId { get; set; }
        public ShippingPatternsDto ShippingPatterns { get; set; } = null!;
        public UserPreferencesDto UserPreferences { get; set; } = null!;
        public string RecommendationType { get; set; } = null!;
    }

    public class PersonalizedRecommendationDto
    {
        public string Type { get; set; } = null!;
        public string Title { get; set; } = null!;
        public string Description { get; set; } = null!;
        public string ExpectedBenefit { get; set; } = null!;
        public decimal ConfidenceScore { get; set; }
        public string Priority { get; set; } = null!;
        public string Category { get; set; } = null!;
        public string ActionRequired { get; set; } = null!;
        public string EstimatedImpact { get; set; } = null!;
        public string ImplementationEffort { get; set; } = null!;
    }

    public class CarrierRiskAssessmentDto
    {
        public Guid CarrierId { get; set; }
        public decimal RiskScore { get; set; }
        public string RiskLevel { get; set; } = null!;
        public List<string> RiskFactors { get; set; } = new();
        public List<string> Recommendations { get; set; } = new();
    }

    public class RouteDto
    {
        public AddressDto Origin { get; set; } = null!;
        public AddressDto Destination { get; set; } = null!;
        public decimal Distance { get; set; }
        public string RouteType { get; set; } = null!;
    }

    public class CarrierPerformanceDataDto
    {
        public Guid CarrierId { get; set; }
        public DateTime Date { get; set; }
        public Dictionary<string, decimal> Metrics { get; set; } = new();
    }

    public class ShippingPatternsDto
    {
        public Dictionary<string, int> VolumeByRoute { get; set; } = new();
        public Dictionary<string, int> VolumeByService { get; set; } = new();
        public List<TrendDataDto> VolumeTrends { get; set; } = new();
    }

    public class UserPreferencesDto
    {
        public Dictionary<string, decimal> CriteriaWeights { get; set; } = new();
        public List<Guid> PreferredCarriers { get; set; } = new();
        public List<string> ServicePreferences { get; set; } = new();
    }
}
