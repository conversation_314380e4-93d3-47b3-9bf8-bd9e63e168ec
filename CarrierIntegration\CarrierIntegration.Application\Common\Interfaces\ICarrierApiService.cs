using CarrierIntegration.Application.DTOs;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Common.Interfaces
{
    public interface ICarrierApiService
    {
        Task<CarrierHealthCheckResult> HealthCheckAsync(Guid carrierId);
        Task<CarrierRateResponse> GetRatesAsync(CarrierRateRequest request);
        Task<CarrierBookingResponse> CreateBookingAsync(CarrierBookingRequest request);
        Task<CarrierBookingResponse> ModifyBookingAsync(string bookingReference, CarrierBookingRequest request);
        Task<CarrierBookingResponse> CancelBookingAsync(string bookingReference, string reason);
        Task<CarrierTrackingResponse> GetTrackingAsync(string trackingNumber);
        Task<CarrierLabelResponse> GenerateLabelAsync(CarrierLabelRequest request);
        Task<CarrierPickupResponse> SchedulePickupAsync(CarrierPickupRequest request);
        Task<CarrierPickupResponse> CancelPickupAsync(string pickupReference, string reason);
        Task<CarrierDocumentResponse> GetDocumentsAsync(string bookingReference);
        Task<CarrierManifestResponse> CreateManifestAsync(CarrierManifestRequest request);
        Task<CarrierTransitTimeResponse> GetTransitTimeAsync(CarrierTransitTimeRequest request);
        Task<CarrierServiceAvailabilityResponse> CheckServiceAvailabilityAsync(CarrierServiceAvailabilityRequest request);
        Task<CarrierAddressValidationResponse> ValidateAddressAsync(CarrierAddressValidationRequest request);
    }

    public class CarrierHealthCheckResult
    {
        public bool IsHealthy { get; set; }
        public string? Message { get; set; }
        public DateTime CheckedAt { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierRateRequest
    {
        public Guid CarrierId { get; set; }
        public Guid? CarrierAccountId { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public List<PackageDto> Packages { get; set; } = new();
        public DateTime ShipDate { get; set; }
        public List<string>? ServiceCodes { get; set; }
        public bool IncludeAccessorials { get; set; }
        public decimal? DeclaredValue { get; set; }
        public string? Currency { get; set; }
        public Dictionary<string, object>? Options { get; set; }
    }

    public class CarrierRateResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public List<CarrierRateDto> Rates { get; set; } = new();
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierBookingRequest
    {
        public Guid CarrierId { get; set; }
        public Guid? CarrierAccountId { get; set; }
        public string? ServiceCode { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public ContactDto OriginContact { get; set; } = null!;
        public ContactDto DestinationContact { get; set; } = null!;
        public List<PackageDto> Packages { get; set; } = new();
        public DateTime PickupDate { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public string? PickupInstructions { get; set; }
        public string? DeliveryInstructions { get; set; }
        public List<string>? SpecialServices { get; set; }
        public decimal? DeclaredValue { get; set; }
        public string? Currency { get; set; }
        public string? Reference { get; set; }
        public Dictionary<string, object>? Options { get; set; }
    }

    public class CarrierBookingResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? BookingReference { get; set; }
        public string? TrackingNumber { get; set; }
        public string? LabelUrl { get; set; }
        public DateTime? ConfirmedPickupDate { get; set; }
        public DateTime? EstimatedDeliveryDate { get; set; }
        public decimal? TotalCost { get; set; }
        public string? Currency { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierTrackingResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? TrackingNumber { get; set; }
        public string? Status { get; set; }
        public string? StatusDescription { get; set; }
        public DateTime? StatusDate { get; set; }
        public AddressDto? CurrentLocation { get; set; }
        public DateTime? EstimatedDeliveryDate { get; set; }
        public DateTime? ActualDeliveryDate { get; set; }
        public List<TrackingEventDto> Events { get; set; } = new();
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierLabelRequest
    {
        public string BookingReference { get; set; } = null!;
        public string? Format { get; set; } // PDF, ZPL, EPL, etc.
        public string? Size { get; set; } // 4x6, 8.5x11, etc.
        public Dictionary<string, object>? Options { get; set; }
    }

    public class CarrierLabelResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? LabelUrl { get; set; }
        public byte[]? LabelData { get; set; }
        public string? Format { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierPickupRequest
    {
        public Guid CarrierId { get; set; }
        public Guid? CarrierAccountId { get; set; }
        public AddressDto PickupAddress { get; set; } = null!;
        public ContactDto PickupContact { get; set; } = null!;
        public DateTime PickupDate { get; set; }
        public TimeSpan? ReadyTime { get; set; }
        public TimeSpan? CloseTime { get; set; }
        public List<string> TrackingNumbers { get; set; } = new();
        public string? Instructions { get; set; }
        public Dictionary<string, object>? Options { get; set; }
    }

    public class CarrierPickupResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? PickupReference { get; set; }
        public DateTime? ConfirmedPickupDate { get; set; }
        public TimeSpan? PickupTimeWindow { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierDocumentResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public List<DocumentDto> Documents { get; set; } = new();
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierManifestRequest
    {
        public Guid CarrierId { get; set; }
        public Guid? CarrierAccountId { get; set; }
        public List<string> BookingReferences { get; set; } = new();
        public DateTime ManifestDate { get; set; }
        public Dictionary<string, object>? Options { get; set; }
    }

    public class CarrierManifestResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public string? ManifestNumber { get; set; }
        public string? ManifestUrl { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierTransitTimeRequest
    {
        public Guid CarrierId { get; set; }
        public string? ServiceCode { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public DateTime ShipDate { get; set; }
        public Dictionary<string, object>? Options { get; set; }
    }

    public class CarrierTransitTimeResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public int? TransitDays { get; set; }
        public DateTime? EstimatedDeliveryDate { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierServiceAvailabilityRequest
    {
        public Guid CarrierId { get; set; }
        public string? ServiceCode { get; set; }
        public AddressDto OriginAddress { get; set; } = null!;
        public AddressDto DestinationAddress { get; set; } = null!;
        public DateTime ShipDate { get; set; }
        public Dictionary<string, object>? Options { get; set; }
    }

    public class CarrierServiceAvailabilityResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public bool IsAvailable { get; set; }
        public string? UnavailableReason { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class CarrierAddressValidationRequest
    {
        public Guid CarrierId { get; set; }
        public AddressDto Address { get; set; } = null!;
        public Dictionary<string, object>? Options { get; set; }
    }

    public class CarrierAddressValidationResponse
    {
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public bool IsValid { get; set; }
        public AddressDto? ValidatedAddress { get; set; }
        public List<string>? ValidationMessages { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }
}
