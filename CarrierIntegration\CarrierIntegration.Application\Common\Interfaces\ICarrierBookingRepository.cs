using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Common.Interfaces
{
    public interface ICarrierBookingRepository
    {
        Task<CarrierBooking?> GetByIdAsync(Guid id);
        Task<CarrierBooking?> GetByReferenceAsync(string bookingReference, Guid organizationId);
        Task<IReadOnlyList<CarrierBooking>> GetByCarrierIdAsync(Guid carrierId, Guid organizationId);
        Task<IReadOnlyList<CarrierBooking>> GetByOrderIdAsync(Guid orderId, Guid organizationId);
        Task<IReadOnlyList<CarrierBooking>> GetByShipmentIdAsync(Guid shipmentId, Guid organizationId);
        Task<IReadOnlyList<CarrierBooking>> GetByStatusAsync(BookingStatus status, Guid organizationId);
        Task<IReadOnlyList<CarrierBooking>> GetExpiredBookingsAsync(Guid organizationId);
        Task<IReadOnlyList<CarrierBooking>> GetBookingsForRetryAsync(Guid organizationId);
        Task<(IReadOnlyList<CarrierBooking> bookings, int totalCount)> GetPagedAsync(
            Guid organizationId,
            int pageNumber,
            int pageSize,
            string? searchTerm = null,
            Guid? carrierId = null,
            BookingStatus? status = null,
            DateTime? fromDate = null,
            DateTime? toDate = null);
        Task<CarrierBooking> AddAsync(CarrierBooking booking);
        Task UpdateAsync(CarrierBooking booking);
        Task DeleteAsync(CarrierBooking booking);
        Task<bool> ExistsAsync(Guid id);
        Task<bool> ReferenceExistsAsync(string bookingReference, Guid organizationId, Guid? excludeId = null);
    }
}
