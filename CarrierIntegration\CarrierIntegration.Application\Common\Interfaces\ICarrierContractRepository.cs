using CarrierIntegration.Domain.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Common.Interfaces
{
    public interface ICarrierContractRepository : IRepository<CarrierContract>
    {
        Task<List<CarrierContract>> GetByCarrierIdAsync(Guid carrierId);
        Task<List<CarrierContract>> GetByOrganizationIdAsync(Guid organizationId);
        Task<List<CarrierContract>> GetActiveContractsAsync(Guid organizationId);
        Task<List<CarrierContract>> GetExpiringContractsAsync(Guid organizationId, int daysAhead);
        Task<List<CarrierContract>> GetByStatusAsync(Guid organizationId, string status);
        Task<List<CarrierContract>> GetByContractTypeAsync(Guid organizationId, string contractType);
        Task<CarrierContract?> GetByContractNumberAsync(string contractNumber, Guid organizationId);
        Task<bool> ContractNumberExistsAsync(string contractNumber, Guid organizationId, Guid? excludeId = null);
    }

    public interface ICarrierRelationshipScoreRepository : IRepository<CarrierRelationshipScore>
    {
        Task<List<CarrierRelationshipScore>> GetByCarrierIdAsync(Guid carrierId);
        Task<List<CarrierRelationshipScore>> GetByOrganizationIdAsync(Guid organizationId);
        Task<CarrierRelationshipScore?> GetLatestScoreAsync(Guid carrierId, Guid organizationId);
        Task<List<CarrierRelationshipScore>> GetByPeriodAsync(Guid organizationId, DateTime startDate, DateTime endDate);
        Task<List<CarrierRelationshipScore>> GetTopPerformingCarriersAsync(Guid organizationId, int count = 10);
        Task<List<CarrierRelationshipScore>> GetByScoreRangeAsync(Guid organizationId, decimal minScore, decimal maxScore);
    }

    public interface ICarrierServiceRepository : IRepository<CarrierService>
    {
        Task<List<CarrierService>> GetByCarrierIdAsync(Guid carrierId);
        Task<List<CarrierService>> GetByOrganizationIdAsync(Guid organizationId);
        Task<List<CarrierService>> GetActiveServicesAsync(Guid organizationId);
        Task<CarrierService?> GetByServiceCodeAsync(string serviceCode, Guid carrierId);
        Task<List<CarrierService>> GetByServiceLevelAsync(string serviceLevel, Guid organizationId);
        Task<bool> ServiceCodeExistsAsync(string serviceCode, Guid carrierId, Guid? excludeId = null);
    }

    public interface ICarrierAccountRepository : IRepository<CarrierAccount>
    {
        Task<List<CarrierAccount>> GetByCarrierIdAsync(Guid carrierId);
        Task<List<CarrierAccount>> GetByOrganizationIdAsync(Guid organizationId);
        Task<List<CarrierAccount>> GetActiveAccountsAsync(Guid organizationId);
        Task<CarrierAccount?> GetByAccountNumberAsync(string accountNumber, Guid carrierId);
        Task<bool> AccountNumberExistsAsync(string accountNumber, Guid carrierId, Guid? excludeId = null);
    }

    public interface ICarrierPerformanceMetricRepository : IRepository<CarrierPerformanceMetric>
    {
        Task<List<CarrierPerformanceMetric>> GetByCarrierIdAsync(Guid carrierId);
        Task<List<CarrierPerformanceMetric>> GetByOrganizationIdAsync(Guid organizationId);
        Task<List<CarrierPerformanceMetric>> GetByPeriodAsync(Guid organizationId, DateTime startDate, DateTime endDate);
        Task<List<CarrierPerformanceMetric>> GetByOrganizationAndPeriodAsync(Guid organizationId, DateTime startDate, DateTime endDate, Guid? carrierId = null);
        Task<List<CarrierPerformanceMetric>> GetByMetricTypeAsync(Guid organizationId, string metricType);
        Task<CarrierPerformanceMetric?> GetLatestMetricAsync(Guid carrierId, string metricType);
    }

    public interface ICarrierComplianceRepository : IRepository<CarrierCompliance>
    {
        Task<List<CarrierCompliance>> GetByCarrierIdAsync(Guid carrierId);
        Task<List<CarrierCompliance>> GetByOrganizationIdAsync(Guid organizationId);
        Task<List<CarrierCompliance>> GetByComplianceTypeAsync(Guid organizationId, string complianceType);
        Task<List<CarrierCompliance>> GetExpiringComplianceAsync(Guid organizationId, int daysAhead);
        Task<List<CarrierCompliance>> GetByStatusAsync(Guid organizationId, string status);
        Task<bool> ComplianceExistsAsync(Guid carrierId, string complianceType, string complianceName, Guid? excludeId = null);
    }
}
