using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Common.Interfaces
{
    public interface IUnitOfWork : IDisposable
    {
        ICarrierRepository Carriers { get; }
        ICarrierServiceRepository CarrierServices { get; }
        ICarrierAccountRepository CarrierAccounts { get; }
        ICarrierBookingRepository CarrierBookings { get; }
        ICarrierPerformanceMetricRepository CarrierPerformanceMetrics { get; }
        ICarrierComplianceRepository CarrierCompliances { get; }
        ICarrierContractRepository CarrierContracts { get; }
        ICarrierRelationshipScoreRepository CarrierRelationshipScores { get; }

        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
        Task BeginTransactionAsync(CancellationToken cancellationToken = default);
        Task CommitTransactionAsync(CancellationToken cancellationToken = default);
        Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
    }
}
