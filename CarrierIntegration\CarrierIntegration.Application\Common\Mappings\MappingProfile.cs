using AutoMapper;
using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.ValueObjects;

namespace CarrierIntegration.Application.Common.Mappings
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            CreateMap<Carrier, CarrierDto>()
                .ForMember(dest => dest.PrimaryContact, opt => opt.MapFrom(src => src.PrimaryContact))
                .ForMember(dest => dest.HeadquartersAddress, opt => opt.MapFrom(src => src.HeadquartersAddress))
                .ForMember(dest => dest.Services, opt => opt.MapFrom(src => src.Services))
                .ForMember(dest => dest.Accounts, opt => opt.MapFrom(src => src.Accounts));

            CreateMap<CarrierService, CarrierServiceDto>()
                .ForMember(dest => dest.BaseRate, opt => opt.MapFrom(src => src.BaseRate))
                .ForMember(dest => dest.SupportedCountries, opt => opt.MapFrom(src => src.SupportedCountries))
                .ForMember(dest => dest.RestrictedCountries, opt => opt.MapFrom(src => src.RestrictedCountries));

            CreateMap<CarrierAccount, CarrierAccountDto>()
                .ForMember(dest => dest.BillingContact, opt => opt.MapFrom(src => src.BillingContact))
                .ForMember(dest => dest.BillingAddress, opt => opt.MapFrom(src => src.BillingAddress))
                .ForMember(dest => dest.TechnicalContact, opt => opt.MapFrom(src => src.TechnicalContact));

            CreateMap<CarrierBooking, CarrierBookingDto>()
                .ForMember(dest => dest.PickupAddress, opt => opt.MapFrom(src => src.PickupAddress))
                .ForMember(dest => dest.DeliveryAddress, opt => opt.MapFrom(src => src.DeliveryAddress))
                .ForMember(dest => dest.PickupContact, opt => opt.MapFrom(src => src.PickupContact))
                .ForMember(dest => dest.DeliveryContact, opt => opt.MapFrom(src => src.DeliveryContact))
                .ForMember(dest => dest.TotalWeight, opt => opt.MapFrom(src => src.TotalWeight))
                .ForMember(dest => dest.TotalDimensions, opt => opt.MapFrom(src => src.TotalDimensions))
                .ForMember(dest => dest.DeclaredValue, opt => opt.MapFrom(src => src.DeclaredValue))
                .ForMember(dest => dest.CODAmount, opt => opt.MapFrom(src => src.CODAmount))
                .ForMember(dest => dest.EstimatedCost, opt => opt.MapFrom(src => src.EstimatedCost))
                .ForMember(dest => dest.ActualCost, opt => opt.MapFrom(src => src.ActualCost))
                .ForMember(dest => dest.CarrierName, opt => opt.Ignore()); // Will be populated separately

            // Value Object mappings
            CreateMap<ContactInfo, ContactDto>()
                .ReverseMap();

            CreateMap<Address, AddressDto>()
                .ReverseMap();

            CreateMap<Money, MoneyDto>()
                .ReverseMap();

            CreateMap<Weight, WeightDto>()
                .ReverseMap();

            CreateMap<Dimensions, DimensionsDto>()
                .ReverseMap();

            // DTO to Value Object mappings for commands
            CreateMap<ContactDto, ContactInfo>()
                .ConstructUsing(src => new ContactInfo(
                    src.Name,
                    src.Email,
                    src.Phone,
                    src.Title,
                    src.Department));

            CreateMap<AddressDto, Address>()
                .ConstructUsing(src => new Address(
                    src.Street1,
                    src.City,
                    src.State,
                    src.PostalCode,
                    src.Country,
                    src.Street2,
                    src.Latitude,
                    src.Longitude));

            CreateMap<MoneyDto, Money>()
                .ConstructUsing(src => new Money(src.Amount, src.Currency));

            CreateMap<WeightDto, Weight>()
                .ConstructUsing(src => new Weight(src.Value, src.Unit));

            CreateMap<DimensionsDto, Dimensions>()
                .ConstructUsing(src => new Dimensions(src.Length, src.Width, src.Height, src.Unit));
        }
    }
}
