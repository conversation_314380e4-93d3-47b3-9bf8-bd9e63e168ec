using CarrierIntegration.Domain.Enums;
using System;
using System.Collections.Generic;

namespace CarrierIntegration.Application.DTOs
{
    public class CarrierDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; } = null!;
        public string Code { get; set; } = null!;
        public CarrierType Type { get; set; }
        public CarrierStatus Status { get; set; }
        public string? Description { get; set; }
        public string? Website { get; set; }
        public ContactDto PrimaryContact { get; set; } = null!;
        public AddressDto HeadquartersAddress { get; set; } = null!;
        public string? ApiEndpoint { get; set; }
        public string? ApiVersion { get; set; }
        public bool IsActive { get; set; }
        public DateTime? LastHealthCheck { get; set; }
        public bool HealthCheckPassed { get; set; }
        public string? HealthCheckMessage { get; set; }
        public int Priority { get; set; }
        public decimal? MaxWeight { get; set; }
        public string? MaxWeightUnit { get; set; }
        public decimal? MaxDimensions { get; set; }
        public string? MaxDimensionsUnit { get; set; }
        public bool SupportsTracking { get; set; }
        public bool SupportsRating { get; set; }
        public bool SupportsLabeling { get; set; }
        public bool SupportsPickup { get; set; }
        public bool SupportsDeliveryConfirmation { get; set; }
        public bool SupportsInternational { get; set; }
        public bool RequiresAccount { get; set; }
        public string? TimeZone { get; set; }
        public string? OperatingHours { get; set; }
        public DateTime? ContractStartDate { get; set; }
        public DateTime? ContractEndDate { get; set; }
        public string? ContractTerms { get; set; }
        public decimal? BaseRate { get; set; }
        public Currency? BaseCurrency { get; set; }
        public string? InsuranceProvider { get; set; }
        public DateTime? InsuranceExpiryDate { get; set; }
        public string? LicenseNumber { get; set; }
        public DateTime? LicenseExpiryDate { get; set; }
        public string? ScacCode { get; set; }
        public List<CarrierServiceDto> Services { get; set; } = new();
        public List<CarrierAccountDto> Accounts { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string CreatedBy { get; set; } = null!;
        public string? UpdatedBy { get; set; }
    }

    public class CarrierServiceDto
    {
        public Guid Id { get; set; }
        public Guid CarrierId { get; set; }
        public string ServiceCode { get; set; } = null!;
        public string ServiceName { get; set; } = null!;
        public string? Description { get; set; }
        public ServiceLevel ServiceLevel { get; set; }
        public bool IsActive { get; set; }
        public int TransitDays { get; set; }
        public int? TransitDaysMax { get; set; }
        public bool IsGuaranteed { get; set; }
        public TimeSpan? CutoffTime { get; set; }
        public bool RequiresSignature { get; set; }
        public bool SupportsInsurance { get; set; }
        public bool SupportsCOD { get; set; }
        public bool SupportsHazmat { get; set; }
        public bool SupportsInternational { get; set; }
        public bool SupportsResidential { get; set; }
        public bool SupportsCommercial { get; set; }
        public bool SupportsPickup { get; set; }
        public bool SupportsDropoff { get; set; }
        public bool SupportsSaturdayDelivery { get; set; }
        public bool SupportsSundayDelivery { get; set; }
        public bool SupportsHolidayDelivery { get; set; }
        public decimal? MinWeight { get; set; }
        public string? MinWeightUnit { get; set; }
        public decimal? MaxWeight { get; set; }
        public string? MaxWeightUnit { get; set; }
        public decimal? MaxLength { get; set; }
        public decimal? MaxWidth { get; set; }
        public decimal? MaxHeight { get; set; }
        public string? MaxDimensionsUnit { get; set; }
        public decimal? MaxDeclaredValue { get; set; }
        public Currency? MaxDeclaredValueCurrency { get; set; }
        public string? ServiceArea { get; set; }
        public string? RestrictedAreas { get; set; }
        public MoneyDto? BaseRate { get; set; }
        public string? RateStructure { get; set; }
        public int Priority { get; set; }
        public string? ApiServiceCode { get; set; }
        public string? TrackingUrlTemplate { get; set; }
        public string? LabelFormat { get; set; }
        public bool RequiresAccount { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public List<string> SupportedCountries { get; set; } = new();
        public List<string> RestrictedCountries { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CarrierAccountDto
    {
        public Guid Id { get; set; }
        public Guid CarrierId { get; set; }
        public string AccountNumber { get; set; } = null!;
        public string AccountName { get; set; } = null!;
        public string? Description { get; set; }
        public bool IsActive { get; set; }
        public bool IsDefault { get; set; }
        public string? Username { get; set; }
        public string? ApiKey { get; set; }
        public DateTime? TokenExpiryDate { get; set; }
        public string? AuthenticationMethod { get; set; }
        public ContactDto BillingContact { get; set; } = null!;
        public AddressDto BillingAddress { get; set; } = null!;
        public ContactDto? TechnicalContact { get; set; }
        public string? CustomerNumber { get; set; }
        public string? MeterNumber { get; set; }
        public string? Environment { get; set; }
        public string? BaseUrl { get; set; }
        public string? TestBaseUrl { get; set; }
        public bool IsTestMode { get; set; }
        public decimal? CreditLimit { get; set; }
        public Currency? CreditCurrency { get; set; }
        public decimal? CurrentBalance { get; set; }
        public decimal? AvailableCredit { get; set; }
        public string? PaymentTerms { get; set; }
        public string? BillingCycle { get; set; }
        public DateTime? LastBillingDate { get; set; }
        public DateTime? NextBillingDate { get; set; }
        public bool AutoPay { get; set; }
        public string? PaymentMethod { get; set; }
        public string? ContractNumber { get; set; }
        public DateTime? ContractStartDate { get; set; }
        public DateTime? ContractEndDate { get; set; }
        public string? ServiceLevel { get; set; }
        public string? DiscountCode { get; set; }
        public decimal? DiscountPercentage { get; set; }
        public int? MaxShipmentsPerDay { get; set; }
        public int? MaxShipmentsPerMonth { get; set; }
        public decimal? MaxShipmentValue { get; set; }
        public Currency? MaxShipmentValueCurrency { get; set; }
        public string? TimeZone { get; set; }
        public string? PreferredLanguage { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public DateTime? LastApiCall { get; set; }
        public int ApiCallsToday { get; set; }
        public int ApiCallsThisMonth { get; set; }
        public int? MaxApiCallsPerDay { get; set; }
        public int? MaxApiCallsPerMonth { get; set; }
        public string? Notes { get; set; }
        public string? Tags { get; set; }
        public DateTime? ActivationDate { get; set; }
        public DateTime? DeactivationDate { get; set; }
        public string? DeactivationReason { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }

    public class CarrierBookingDto
    {
        public Guid Id { get; set; }
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public Guid? CarrierAccountId { get; set; }
        public Guid? CarrierServiceId { get; set; }
        public Guid? OrderId { get; set; }
        public Guid? ShipmentId { get; set; }
        public string BookingReference { get; set; } = null!;
        public string? CarrierBookingReference { get; set; }
        public BookingStatus Status { get; set; }
        public string? StatusMessage { get; set; }
        public DateTime BookingDate { get; set; }
        public DateTime? ConfirmationDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public DateTime RequestedPickupDate { get; set; }
        public DateTime? RequestedDeliveryDate { get; set; }
        public DateTime? ConfirmedPickupDate { get; set; }
        public DateTime? ConfirmedDeliveryDate { get; set; }
        public TimeSpan? PickupTimeWindow { get; set; }
        public TimeSpan? DeliveryTimeWindow { get; set; }
        public AddressDto PickupAddress { get; set; } = null!;
        public AddressDto DeliveryAddress { get; set; } = null!;
        public ContactDto PickupContact { get; set; } = null!;
        public ContactDto DeliveryContact { get; set; } = null!;
        public string? PickupInstructions { get; set; }
        public string? DeliveryInstructions { get; set; }
        public WeightDto TotalWeight { get; set; } = null!;
        public DimensionsDto? TotalDimensions { get; set; }
        public int PackageCount { get; set; }
        public string? PackageDescription { get; set; }
        public MoneyDto? DeclaredValue { get; set; }
        public string? CommodityCode { get; set; }
        public string? HazmatClass { get; set; }
        public bool RequiresSignature { get; set; }
        public bool RequiresInsurance { get; set; }
        public bool IsCOD { get; set; }
        public MoneyDto? CODAmount { get; set; }
        public string? CODPaymentMethod { get; set; }
        public bool IsResidentialPickup { get; set; }
        public bool IsResidentialDelivery { get; set; }
        public bool RequiresAppointment { get; set; }
        public bool RequiresLiftgate { get; set; }
        public bool RequiresInsideDelivery { get; set; }
        public bool RequiresWhiteGlove { get; set; }
        public string? SpecialServices { get; set; }
        public string? AccessorialServices { get; set; }
        public MoneyDto? EstimatedCost { get; set; }
        public MoneyDto? ActualCost { get; set; }
        public string? CostBreakdown { get; set; }
        public string? CarrierServiceCode { get; set; }
        public string? CarrierServiceName { get; set; }
        public int? EstimatedTransitDays { get; set; }
        public string? TrackingNumber { get; set; }
        public string? LabelUrl { get; set; }
        public string? DocumentsUrl { get; set; }
        public string? ManifestNumber { get; set; }
        public string? BillOfLadingNumber { get; set; }
        public string? ProNumber { get; set; }
        public string? EquipmentType { get; set; }
        public string? EquipmentNumber { get; set; }
        public string? DriverName { get; set; }
        public string? DriverPhone { get; set; }
        public string? VehicleInfo { get; set; }
        public string? CarrierNotes { get; set; }
        public string? InternalNotes { get; set; }
        public string? CustomerReference { get; set; }
        public string? ShipperReference { get; set; }
        public string? PoNumber { get; set; }
        public string? InvoiceNumber { get; set; }
        public DateTime? CancelledDate { get; set; }
        public string? CancellationReason { get; set; }
        public string? CancelledBy { get; set; }
        public DateTime? CompletedDate { get; set; }
        public string? CompletionNotes { get; set; }
        public DateTime? LastApiCall { get; set; }
        public int ApiCallCount { get; set; }
        public string? LastError { get; set; }
        public DateTime? LastErrorDate { get; set; }
        public int RetryCount { get; set; }
        public DateTime? NextRetryDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string CreatedBy { get; set; } = null!;
        public string? UpdatedBy { get; set; }
    }

    // CarrierRateDto is defined in CarrierApiDtos.cs to avoid duplicates

    public class AccessorialChargeDto
    {
        public string Code { get; set; } = null!;
        public string Name { get; set; } = null!;
        public MoneyDto Amount { get; set; } = null!;
        public string? Description { get; set; }
    }
}
