using CarrierIntegration.Domain.Enums;
using System;
using System.Collections.Generic;

namespace CarrierIntegration.Application.DTOs
{
    public class AddressDto
    {
        public string Street1 { get; set; } = null!;
        public string? Street2 { get; set; }
        public string City { get; set; } = null!;
        public string State { get; set; } = null!;
        public string PostalCode { get; set; } = null!;
        public string Country { get; set; } = null!;
        public decimal? Latitude { get; set; }
        public decimal? Longitude { get; set; }
    }

    public class ContactDto
    {
        public string Name { get; set; } = null!;
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Title { get; set; }
        public string? Department { get; set; }
    }

    public class MoneyDto
    {
        public decimal Amount { get; set; }
        public Currency Currency { get; set; }
    }

    public class WeightDto
    {
        public decimal Value { get; set; }
        public string Unit { get; set; } = null!;
    }

    public class DimensionsDto
    {
        public decimal Length { get; set; }
        public decimal Width { get; set; }
        public decimal Height { get; set; }
        public string Unit { get; set; } = null!;
    }

    public class PackageDto
    {
        public int SequenceNumber { get; set; }
        public WeightDto Weight { get; set; } = null!;
        public DimensionsDto? Dimensions { get; set; }
        public string? Description { get; set; }
        public MoneyDto? DeclaredValue { get; set; }
        public string? PackageType { get; set; }
        public string? Reference { get; set; }
        public bool IsHazmat { get; set; }
        public string? HazmatClass { get; set; }
        public string? HazmatId { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class TrackingEventDto
    {
        public DateTime EventDate { get; set; }
        public string EventCode { get; set; } = null!;
        public string EventDescription { get; set; } = null!;
        public AddressDto? Location { get; set; }
        public string? LocationDescription { get; set; }
        public string? StatusCode { get; set; }
        public string? StatusDescription { get; set; }
        public bool IsDelivered { get; set; }
        public bool IsException { get; set; }
        public string? ExceptionReason { get; set; }
        public string? SignedBy { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class DocumentDto
    {
        public string Type { get; set; } = null!;
        public string Name { get; set; } = null!;
        public string? Description { get; set; }
        public string? Url { get; set; }
        public byte[]? Data { get; set; }
        public string? Format { get; set; }
        public long? Size { get; set; }
        public DateTime CreatedDate { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class PagedResultDto<T>
    {
        public List<T> Items { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }

    public class ApiResponseDto<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string? ErrorMessage { get; set; }
        public List<string>? ValidationErrors { get; set; }
        public Dictionary<string, object>? AdditionalData { get; set; }

        public static ApiResponseDto<T> SuccessResult(T data)
        {
            return new ApiResponseDto<T>
            {
                Success = true,
                Data = data
            };
        }

        public static ApiResponseDto<T> ErrorResult(string errorMessage)
        {
            return new ApiResponseDto<T>
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }

        public static ApiResponseDto<T> ValidationErrorResult(List<string> validationErrors)
        {
            return new ApiResponseDto<T>
            {
                Success = false,
                ValidationErrors = validationErrors
            };
        }
    }

    public class CarrierSelectionCriteriaDto
    {
        public List<Guid>? PreferredCarrierIds { get; set; }
        public List<Guid>? ExcludedCarrierIds { get; set; }
        public List<CarrierType>? AllowedCarrierTypes { get; set; }
        public List<CarrierType>? ExcludedCarrierTypes { get; set; }
        public bool RequireTracking { get; set; }
        public bool RequireInsurance { get; set; }
        public bool RequireSignature { get; set; }
        public bool RequirePickup { get; set; }
        public bool RequireInternational { get; set; }
        public int? MaxTransitDays { get; set; }
        public MoneyDto? MaxCost { get; set; }
        public WeightDto? MaxWeight { get; set; }
        public DimensionsDto? MaxDimensions { get; set; }
        public string? ServiceLevel { get; set; }
        public bool IsResidential { get; set; }
        public bool RequiresSpecialHandling { get; set; }
        public List<string>? RequiredServices { get; set; }
        public Dictionary<string, object>? AdditionalCriteria { get; set; }
    }

    public class CarrierPerformanceDto
    {
        public Guid CarrierId { get; set; }
        public string CarrierName { get; set; } = null!;
        public decimal OnTimeDeliveryRate { get; set; }
        public decimal AverageTransitTime { get; set; }
        public decimal ExceptionRate { get; set; }
        public decimal DamageClaimRate { get; set; }
        public decimal CostPerformanceIndex { get; set; }
        public decimal CustomerSatisfactionScore { get; set; }
        public int TotalShipments { get; set; }
        public DateTime PeriodStart { get; set; }
        public DateTime PeriodEnd { get; set; }
        public Dictionary<string, object>? AdditionalMetrics { get; set; }
    }

    // CarrierComparisonDto is defined in AnalyticsDtos.cs to avoid duplicates
}
