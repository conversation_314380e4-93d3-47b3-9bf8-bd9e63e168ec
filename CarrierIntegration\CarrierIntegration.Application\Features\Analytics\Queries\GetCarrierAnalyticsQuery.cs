using AutoMapper;
using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.Analytics.Queries
{
    public class GetCarrierAnalyticsQuery : IRequest<ApiResponseDto<CarrierAnalyticsDto>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public string AnalyticsType { get; set; } = "Performance"; // Performance, Cost, Volume, Quality
        public string Granularity { get; set; } = "Daily"; // Hourly, Daily, Weekly, Monthly
        public List<string>? Metrics { get; set; }
        public bool IncludePredictions { get; set; } = false;
        public bool IncludeComparisons { get; set; } = true;
    }

    public class GetCarrierAnalyticsQueryHandler : IRequestHandler<GetCarrierAnalyticsQuery, ApiResponseDto<CarrierAnalyticsDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICarrierAnalyticsService _analyticsService;
        private readonly ICachingService _cachingService;
        private readonly IMapper _mapper;
        private readonly ILogger<GetCarrierAnalyticsQueryHandler> _logger;

        public GetCarrierAnalyticsQueryHandler(
            IUnitOfWork unitOfWork,
            ICarrierAnalyticsService analyticsService,
            ICachingService cachingService,
            IMapper mapper,
            ILogger<GetCarrierAnalyticsQueryHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _analyticsService = analyticsService;
            _cachingService = cachingService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ApiResponseDto<CarrierAnalyticsDto>> Handle(GetCarrierAnalyticsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Generating carrier analytics for organization {OrganizationId} from {StartDate} to {EndDate}",
                    request.OrganizationId, request.StartDate, request.EndDate);

                // Check cache first
                var cacheKey = _cachingService.GenerateKey("analytics",
                    request.OrganizationId, request.CarrierId, request.StartDate.ToString("yyyyMMdd"),
                    request.EndDate.ToString("yyyyMMdd"), request.AnalyticsType, request.Granularity);

                var cachedResult = await _cachingService.GetAsync<CarrierAnalyticsDto>(cacheKey);
                if (cachedResult != null)
                {
                    _logger.LogInformation("Returning cached analytics data");
                    return ApiResponseDto<CarrierAnalyticsDto>.SuccessResult(cachedResult);
                }

                // Generate analytics
                var analytics = await _analyticsService.GenerateAnalyticsAsync(request);

                // Add predictive analytics if requested
                if (request.IncludePredictions)
                {
                    analytics.Predictions = await _analyticsService.GeneratePredictionsAsync(request);
                }

                // Add comparisons if requested
                if (request.IncludeComparisons)
                {
                    analytics.Comparisons = await _analyticsService.GenerateComparisonsAsync(request);
                }

                // Cache the result
                await _cachingService.SetAsync(cacheKey, analytics, TimeSpan.FromHours(1));

                _logger.LogInformation("Successfully generated carrier analytics with {MetricCount} metrics",
                    analytics.Metrics?.Count ?? 0);

                return ApiResponseDto<CarrierAnalyticsDto>.SuccessResult(analytics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating carrier analytics for organization {OrganizationId}",
                    request.OrganizationId);
                return ApiResponseDto<CarrierAnalyticsDto>.ErrorResult($"Failed to generate analytics: {ex.Message}");
            }
        }
    }

    // GetCarrierDashboardQuery and GetPredictiveAnalyticsQuery are defined in separate files
}
