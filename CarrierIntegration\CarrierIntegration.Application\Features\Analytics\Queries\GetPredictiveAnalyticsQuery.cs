using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.Analytics.Queries
{
    public class GetPredictiveAnalyticsQuery : IRequest<ApiResponseDto<PredictiveAnalyticsDto>>
    {
        public Guid OrganizationId { get; set; }
        public Guid? CarrierId { get; set; }
        public string PredictionType { get; set; } = "Performance";
        public int ForecastDays { get; set; } = 30;
        public decimal ConfidenceLevel { get; set; } = 0.95m;
        public bool IncludeScenarios { get; set; } = true;
        public string? ModelVersion { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class GetPredictiveAnalyticsQueryHandler : IRequestHandler<GetPredictiveAnalyticsQuery, ApiResponseDto<PredictiveAnalyticsDto>>
    {
        private readonly IPredictiveAnalyticsService _predictiveService;
        private readonly ICachingService _cachingService;
        private readonly ILogger<GetPredictiveAnalyticsQueryHandler> _logger;

        public GetPredictiveAnalyticsQueryHandler(
            IPredictiveAnalyticsService predictiveService,
            ICachingService cachingService,
            ILogger<GetPredictiveAnalyticsQueryHandler> logger)
        {
            _predictiveService = predictiveService;
            _cachingService = cachingService;
            _logger = logger;
        }

        public async Task<ApiResponseDto<PredictiveAnalyticsDto>> Handle(GetPredictiveAnalyticsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Generating {PredictionType} predictions for organization {OrganizationId}",
                    request.PredictionType, request.OrganizationId);

                var predictions = await _predictiveService.GeneratePredictionsAsync(request);

                _logger.LogInformation("Successfully generated {PredictionType} predictions with {DataPointCount} data points",
                    request.PredictionType, predictions.Forecasts?.Count ?? 0);

                return ApiResponseDto<PredictiveAnalyticsDto>.SuccessResult(predictions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating predictions for organization {OrganizationId}", request.OrganizationId);
                return ApiResponseDto<PredictiveAnalyticsDto>.ErrorResult($"Failed to generate predictions: {ex.Message}");
            }
        }
    }
}
