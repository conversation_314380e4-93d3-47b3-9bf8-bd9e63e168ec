using AutoMapper;
using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.CarrierBookings.Commands
{
    public class CancelCarrierBookingCommand : IRequest<ApiResponseDto<bool>>
    {
        public Guid Id { get; set; }
        public string Reason { get; set; } = null!;
        public bool NotifyCarrier { get; set; } = true;
        public string? AdditionalNotes { get; set; }
        public string UpdatedBy { get; set; } = "System";
    }

    public class CancelCarrierBookingCommandValidator : AbstractValidator<CancelCarrierBookingCommand>
    {
        private readonly IUnitOfWork _unitOfWork;

        public CancelCarrierBookingCommandValidator(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

            RuleFor(x => x.Id)
                .NotEmpty()
                .WithMessage("Booking ID is required")
                .MustAsync(BeValidBookingForCancellation)
                .WithMessage("Booking does not exist or cannot be cancelled");

            RuleFor(x => x.Reason)
                .NotEmpty()
                .WithMessage("Cancellation reason is required")
                .MaximumLength(500)
                .WithMessage("Cancellation reason cannot exceed 500 characters");

            RuleFor(x => x.AdditionalNotes)
                .MaximumLength(1000)
                .WithMessage("Additional notes cannot exceed 1000 characters");
        }

        private async Task<bool> BeValidBookingForCancellation(Guid bookingId, CancellationToken cancellationToken)
        {
            var booking = await _unitOfWork.CarrierBookings.GetByIdAsync(bookingId);
            return booking != null && booking.CanBeCancelled();
        }
    }

    public class CancelCarrierBookingCommandHandler : IRequestHandler<CancelCarrierBookingCommand, ApiResponseDto<bool>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ICarrierApiService _carrierApiService;
        private readonly ILogger<CancelCarrierBookingCommandHandler> _logger;

        public CancelCarrierBookingCommandHandler(
            IUnitOfWork unitOfWork,
            ICarrierApiService carrierApiService,
            ILogger<CancelCarrierBookingCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _carrierApiService = carrierApiService;
            _logger = logger;
        }

        public async Task<ApiResponseDto<bool>> Handle(CancelCarrierBookingCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Cancelling carrier booking {BookingId} with reason: {Reason}",
                    request.Id, request.Reason);

                var booking = await _unitOfWork.CarrierBookings.GetByIdAsync(request.Id);
                if (booking == null)
                {
                    return ApiResponseDto<bool>.ErrorResult("Booking not found");
                }

                if (!booking.CanBeCancelled())
                {
                    return ApiResponseDto<bool>.ErrorResult($"Cannot cancel booking with status {booking.Status}");
                }

                // Notify carrier if requested and booking has carrier reference
                if (request.NotifyCarrier && !string.IsNullOrWhiteSpace(booking.CarrierBookingReference))
                {
                    try
                    {
                        _logger.LogInformation("Notifying carrier about booking cancellation for {BookingReference}",
                            booking.CarrierBookingReference);

                        var carrierResponse = await _carrierApiService.CancelBookingAsync(
                            booking.CarrierBookingReference,
                            request.Reason);

                        if (!carrierResponse.Success)
                        {
                            _logger.LogWarning("Failed to notify carrier about cancellation: {Error}",
                                carrierResponse.ErrorMessage);
                            
                            // Continue with cancellation even if carrier notification fails
                        }
                        else
                        {
                            _logger.LogInformation("Successfully notified carrier about booking cancellation");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error notifying carrier about booking cancellation");
                        // Continue with cancellation even if carrier notification fails
                    }
                }

                // Update notes if additional notes provided
                if (!string.IsNullOrWhiteSpace(request.AdditionalNotes))
                {
                    var updatedInternalNotes = string.IsNullOrWhiteSpace(booking.InternalNotes)
                        ? $"Cancellation Notes: {request.AdditionalNotes}"
                        : $"{booking.InternalNotes}\n\nCancellation Notes: {request.AdditionalNotes}";

                    booking.UpdateNotes(
                        booking.CarrierNotes,
                        updatedInternalNotes,
                        request.UpdatedBy);
                }

                // Cancel the booking
                booking.Cancel(request.Reason, request.UpdatedBy);

                // Save to database
                await _unitOfWork.CarrierBookings.UpdateAsync(booking);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully cancelled carrier booking {BookingId} with reference {BookingReference}",
                    booking.Id, booking.BookingReference);

                return ApiResponseDto<bool>.SuccessResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling carrier booking {BookingId}", request.Id);
                return ApiResponseDto<bool>.ErrorResult($"Failed to cancel carrier booking: {ex.Message}");
            }
        }
    }
}
