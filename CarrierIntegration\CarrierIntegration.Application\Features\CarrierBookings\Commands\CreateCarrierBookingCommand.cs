using AutoMapper;
using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.ValueObjects;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Application.Features.CarrierBookings.Commands
{
    public class CreateCarrierBookingCommand : IRequest<ApiResponseDto<CarrierBookingDto>>
    {
        public Guid OrganizationId { get; set; }
        public Guid CarrierId { get; set; }
        public Guid? CarrierAccountId { get; set; }
        public Guid? CarrierServiceId { get; set; }
        public Guid? OrderId { get; set; }
        public Guid? ShipmentId { get; set; }
        public string BookingReference { get; set; } = null!;
        public DateTime RequestedPickupDate { get; set; }
        public DateTime? RequestedDeliveryDate { get; set; }
        public TimeSpan? PickupTimeWindow { get; set; }
        public TimeSpan? DeliveryTimeWindow { get; set; }
        public AddressDto PickupAddress { get; set; } = null!;
        public AddressDto DeliveryAddress { get; set; } = null!;
        public ContactDto PickupContact { get; set; } = null!;
        public ContactDto DeliveryContact { get; set; } = null!;
        public string? PickupInstructions { get; set; }
        public string? DeliveryInstructions { get; set; }
        public WeightDto TotalWeight { get; set; } = null!;
        public DimensionsDto? TotalDimensions { get; set; }
        public int PackageCount { get; set; }
        public string? PackageDescription { get; set; }
        public MoneyDto? DeclaredValue { get; set; }
        public string? CommodityCode { get; set; }
        public string? HazmatClass { get; set; }
        public bool RequiresSignature { get; set; }
        public bool RequiresInsurance { get; set; }
        public bool IsCOD { get; set; }
        public MoneyDto? CODAmount { get; set; }
        public string? CODPaymentMethod { get; set; }
        public bool IsResidentialPickup { get; set; }
        public bool IsResidentialDelivery { get; set; }
        public bool RequiresAppointment { get; set; }
        public bool RequiresLiftgate { get; set; }
        public bool RequiresInsideDelivery { get; set; }
        public bool RequiresWhiteGlove { get; set; }
        public string? SpecialServices { get; set; }
        public string? AccessorialServices { get; set; }
        public string? CustomerReference { get; set; }
        public string? ShipperReference { get; set; }
        public string? PoNumber { get; set; }
        public string CreatedBy { get; set; } = "System";
    }

    public class CreateCarrierBookingCommandValidator : AbstractValidator<CreateCarrierBookingCommand>
    {
        private readonly IUnitOfWork _unitOfWork;

        public CreateCarrierBookingCommandValidator(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;

            RuleFor(x => x.OrganizationId)
                .NotEmpty()
                .WithMessage("Organization ID is required");

            RuleFor(x => x.CarrierId)
                .NotEmpty()
                .WithMessage("Carrier ID is required")
                .MustAsync(BeValidCarrier)
                .WithMessage("Carrier does not exist or is not active");

            RuleFor(x => x.BookingReference)
                .NotEmpty()
                .WithMessage("Booking reference is required")
                .MaximumLength(100)
                .WithMessage("Booking reference cannot exceed 100 characters")
                .MustAsync(BeUniqueBookingReference)
                .WithMessage("Booking reference already exists");

            RuleFor(x => x.RequestedPickupDate)
                .GreaterThanOrEqualTo(DateTime.Today)
                .WithMessage("Requested pickup date cannot be in the past");

            RuleFor(x => x.RequestedDeliveryDate)
                .GreaterThan(x => x.RequestedPickupDate)
                .When(x => x.RequestedDeliveryDate.HasValue)
                .WithMessage("Requested delivery date must be after pickup date");

            RuleFor(x => x.PickupAddress)
                .NotNull()
                .WithMessage("Pickup address is required");

            RuleFor(x => x.DeliveryAddress)
                .NotNull()
                .WithMessage("Delivery address is required");

            RuleFor(x => x.PickupContact)
                .NotNull()
                .WithMessage("Pickup contact is required");

            RuleFor(x => x.DeliveryContact)
                .NotNull()
                .WithMessage("Delivery contact is required");

            RuleFor(x => x.TotalWeight)
                .NotNull()
                .WithMessage("Total weight is required");

            RuleFor(x => x.TotalWeight.Value)
                .GreaterThan(0)
                .When(x => x.TotalWeight != null)
                .WithMessage("Total weight must be greater than zero");

            RuleFor(x => x.PackageCount)
                .GreaterThan(0)
                .WithMessage("Package count must be greater than zero");

            RuleFor(x => x.CODAmount)
                .NotNull()
                .When(x => x.IsCOD)
                .WithMessage("COD amount is required when COD is enabled");

            RuleFor(x => x.CODAmount!.Amount)
                .GreaterThan(0)
                .When(x => x.IsCOD && x.CODAmount != null)
                .WithMessage("COD amount must be greater than zero");
        }

        private async Task<bool> BeValidCarrier(CreateCarrierBookingCommand command, Guid carrierId, CancellationToken cancellationToken)
        {
            var carrier = await _unitOfWork.Carriers.GetByIdAsync(carrierId);
            return carrier != null && carrier.IsActive && carrier.OrganizationId == command.OrganizationId;
        }

        private async Task<bool> BeUniqueBookingReference(CreateCarrierBookingCommand command, string bookingReference, CancellationToken cancellationToken)
        {
            return !await _unitOfWork.CarrierBookings.ReferenceExistsAsync(bookingReference, command.OrganizationId);
        }
    }

    public class CreateCarrierBookingCommandHandler : IRequestHandler<CreateCarrierBookingCommand, ApiResponseDto<CarrierBookingDto>>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly ILogger<CreateCarrierBookingCommandHandler> _logger;

        public CreateCarrierBookingCommandHandler(
            IUnitOfWork unitOfWork,
            IMapper mapper,
            ILogger<CreateCarrierBookingCommandHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ApiResponseDto<CarrierBookingDto>> Handle(CreateCarrierBookingCommand request, CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("Creating carrier booking {BookingReference} for carrier {CarrierId}",
                    request.BookingReference, request.CarrierId);

                // Create value objects
                var pickupAddress = _mapper.Map<Address>(request.PickupAddress);
                var deliveryAddress = _mapper.Map<Address>(request.DeliveryAddress);
                var pickupContact = _mapper.Map<ContactInfo>(request.PickupContact);
                var deliveryContact = _mapper.Map<ContactInfo>(request.DeliveryContact);
                var totalWeight = _mapper.Map<Weight>(request.TotalWeight);

                // Create booking entity
                var booking = new CarrierBooking(
                    request.CarrierId,
                    request.OrganizationId,
                    request.BookingReference,
                    request.RequestedPickupDate,
                    pickupAddress,
                    deliveryAddress,
                    pickupContact,
                    deliveryContact,
                    totalWeight,
                    request.PackageCount,
                    request.CreatedBy);

                // Update optional properties
                booking.UpdateCarrierInfo(
                    request.CarrierAccountId,
                    request.CarrierServiceId,
                    null, // CarrierServiceCode will be set later
                    null, // CarrierServiceName will be set later
                    request.CreatedBy);

                booking.UpdateOrderInfo(
                    request.OrderId,
                    request.ShipmentId,
                    request.CustomerReference,
                    request.ShipperReference,
                    request.PoNumber,
                    request.CreatedBy);

                booking.UpdateSchedule(
                    request.RequestedPickupDate,
                    request.RequestedDeliveryDate,
                    request.PickupTimeWindow,
                    request.DeliveryTimeWindow,
                    request.CreatedBy);

                booking.UpdateAddresses(
                    pickupAddress,
                    deliveryAddress,
                    pickupContact,
                    deliveryContact,
                    request.PickupInstructions,
                    request.DeliveryInstructions,
                    request.CreatedBy);

                var totalDimensions = request.TotalDimensions != null ? _mapper.Map<Dimensions>(request.TotalDimensions) : null;
                var declaredValue = request.DeclaredValue != null ? _mapper.Map<Money>(request.DeclaredValue) : null;

                booking.UpdatePackageInfo(
                    totalWeight,
                    totalDimensions,
                    request.PackageCount,
                    request.PackageDescription,
                    declaredValue,
                    request.CommodityCode,
                    request.HazmatClass,
                    request.CreatedBy);

                var codAmount = request.CODAmount != null ? _mapper.Map<Money>(request.CODAmount) : null;

                booking.UpdateServiceRequirements(
                    request.RequiresSignature,
                    request.RequiresInsurance,
                    request.IsCOD,
                    codAmount,
                    request.CODPaymentMethod,
                    request.IsResidentialPickup,
                    request.IsResidentialDelivery,
                    request.RequiresAppointment,
                    request.RequiresLiftgate,
                    request.RequiresInsideDelivery,
                    request.RequiresWhiteGlove,
                    request.SpecialServices,
                    request.AccessorialServices,
                    request.CreatedBy);

                // Save to database
                await _unitOfWork.CarrierBookings.AddAsync(booking);
                await _unitOfWork.SaveChangesAsync(cancellationToken);

                _logger.LogInformation("Successfully created carrier booking {BookingId} with reference {BookingReference}",
                    booking.Id, booking.BookingReference);

                // Map to DTO and return
                var bookingDto = _mapper.Map<CarrierBookingDto>(booking);
                return ApiResponseDto<CarrierBookingDto>.SuccessResult(bookingDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating carrier booking {BookingReference} for carrier {CarrierId}",
                    request.BookingReference, request.CarrierId);

                return ApiResponseDto<CarrierBookingDto>.ErrorResult($"Failed to create carrier booking: {ex.Message}");
            }
        }
    }
}
