using CarrierIntegration.Domain.Common;
using CarrierIntegration.Domain.Enums;
using CarrierIntegration.Domain.Events;
using CarrierIntegration.Domain.ValueObjects;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CarrierIntegration.Domain.Entities
{
    public class Carrier : BaseEntity
    {
        public string Name { get; private set; }
        public string Code { get; private set; }
        public CarrierType Type { get; private set; }
        public CarrierStatus Status { get; private set; }
        public string? Description { get; private set; }
        public string? Website { get; private set; }
        public ContactInfo PrimaryContact { get; private set; }
        public Address HeadquartersAddress { get; private set; }
        public string? ApiEndpoint { get; private set; }
        public string? ApiVersion { get; private set; }
        public bool IsActive { get; private set; }
        public DateTime? LastHealthCheck { get; private set; }
        public bool HealthCheckPassed { get; private set; }
        public string? HealthCheckMessage { get; private set; }
        public int Priority { get; private set; }
        public decimal? MaxWeight { get; private set; }
        public string? MaxWeightUnit { get; private set; }
        public decimal? MaxDimensions { get; private set; }
        public string? MaxDimensionsUnit { get; private set; }
        public bool SupportsTracking { get; private set; }
        public bool SupportsRating { get; private set; }
        public bool SupportsLabeling { get; private set; }
        public bool SupportsPickup { get; private set; }
        public bool SupportsDeliveryConfirmation { get; private set; }
        public bool SupportsInternational { get; private set; }
        public bool RequiresAccount { get; private set; }
        public string? TimeZone { get; private set; }
        public string? OperatingHours { get; private set; }
        public string? SupportContact { get; private set; }
        public string? BillingContact { get; private set; }
        public DateTime? ContractStartDate { get; private set; }
        public DateTime? ContractEndDate { get; private set; }
        public string? ContractTerms { get; private set; }
        public decimal? BaseRate { get; private set; }
        public Currency? BaseCurrency { get; private set; }
        public string? InsuranceProvider { get; private set; }
        public string? InsurancePolicyNumber { get; private set; }
        public DateTime? InsuranceExpiryDate { get; private set; }
        public string? LicenseNumber { get; private set; }
        public DateTime? LicenseExpiryDate { get; private set; }
        public string? DotNumber { get; private set; }
        public string? McNumber { get; private set; }
        public string? ScacCode { get; private set; }
        public string? IataCode { get; private set; }
        public string? CustomsCode { get; private set; }

        private readonly List<CarrierService> _services = new();
        public IReadOnlyCollection<CarrierService> Services => _services.AsReadOnly();

        private readonly List<CarrierAccount> _accounts = new();
        public IReadOnlyCollection<CarrierAccount> Accounts => _accounts.AsReadOnly();

        private readonly List<CarrierCompliance> _complianceRecords = new();
        public IReadOnlyCollection<CarrierCompliance> ComplianceRecords => _complianceRecords.AsReadOnly();

        private readonly List<CarrierPerformanceMetric> _performanceMetrics = new();
        public IReadOnlyCollection<CarrierPerformanceMetric> PerformanceMetrics => _performanceMetrics.AsReadOnly();

        private Carrier() { } // For EF Core

        public Carrier(
            Guid organizationId,
            string name,
            string code,
            CarrierType type,
            ContactInfo primaryContact,
            Address headquartersAddress,
            string createdBy = "System") : base(organizationId, createdBy)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Carrier name is required");
            if (string.IsNullOrWhiteSpace(code))
                throw new DomainException("Carrier code is required");

            Name = name.Trim();
            Code = code.Trim().ToUpperInvariant();
            Type = type;
            Status = CarrierStatus.Onboarding;
            PrimaryContact = primaryContact ?? throw new DomainException("Primary contact is required");
            HeadquartersAddress = headquartersAddress ?? throw new DomainException("Headquarters address is required");
            IsActive = false;
            Priority = 100; // Default priority
            SupportsTracking = false;
            SupportsRating = false;
            SupportsLabeling = false;
            SupportsPickup = false;
            SupportsDeliveryConfirmation = false;
            SupportsInternational = false;
            RequiresAccount = true;
            HealthCheckPassed = false;

            AddDomainEvent(new CarrierCreatedEvent(Id, organizationId, name, code, type));
        }

        public void UpdateBasicInfo(
            string name,
            string? description,
            string? website,
            ContactInfo primaryContact,
            Address headquartersAddress,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Carrier name is required");

            var oldName = Name;
            Name = name.Trim();
            Description = description?.Trim();
            Website = website?.Trim();
            PrimaryContact = primaryContact ?? throw new DomainException("Primary contact is required");
            HeadquartersAddress = headquartersAddress ?? throw new DomainException("Headquarters address is required");

            Update(updatedBy);

            if (oldName != Name)
            {
                AddDomainEvent(new CarrierUpdatedEvent(Id, OrganizationId, Name));
            }
        }

        public void UpdateApiConfiguration(
            string? apiEndpoint,
            string? apiVersion,
            string? timeZone,
            string? operatingHours,
            string updatedBy)
        {
            ApiEndpoint = apiEndpoint?.Trim();
            ApiVersion = apiVersion?.Trim();
            TimeZone = timeZone?.Trim();
            OperatingHours = operatingHours?.Trim();

            Update(updatedBy);
        }

        public void UpdateCapabilities(
            bool supportsTracking,
            bool supportsRating,
            bool supportsLabeling,
            bool supportsPickup,
            bool supportsDeliveryConfirmation,
            bool supportsInternational,
            bool requiresAccount,
            string updatedBy)
        {
            SupportsTracking = supportsTracking;
            SupportsRating = supportsRating;
            SupportsLabeling = supportsLabeling;
            SupportsPickup = supportsPickup;
            SupportsDeliveryConfirmation = supportsDeliveryConfirmation;
            SupportsInternational = supportsInternational;
            RequiresAccount = requiresAccount;

            Update(updatedBy);
        }

        public void UpdateLimitations(
            decimal? maxWeight,
            string? maxWeightUnit,
            decimal? maxDimensions,
            string? maxDimensionsUnit,
            string updatedBy)
        {
            MaxWeight = maxWeight;
            MaxWeightUnit = maxWeightUnit?.Trim().ToUpperInvariant();
            MaxDimensions = maxDimensions;
            MaxDimensionsUnit = maxDimensionsUnit?.Trim().ToUpperInvariant();

            Update(updatedBy);
        }

        public void UpdateContractInfo(
            DateTime? contractStartDate,
            DateTime? contractEndDate,
            string? contractTerms,
            decimal? baseRate,
            Currency? baseCurrency,
            string updatedBy)
        {
            ContractStartDate = contractStartDate;
            ContractEndDate = contractEndDate;
            ContractTerms = contractTerms?.Trim();
            BaseRate = baseRate;
            BaseCurrency = baseCurrency;

            Update(updatedBy);
        }

        public void UpdateComplianceInfo(
            string? insuranceProvider,
            string? insurancePolicyNumber,
            DateTime? insuranceExpiryDate,
            string? licenseNumber,
            DateTime? licenseExpiryDate,
            string? dotNumber,
            string? mcNumber,
            string? scacCode,
            string? iataCode,
            string? customsCode,
            string updatedBy)
        {
            InsuranceProvider = insuranceProvider?.Trim();
            InsurancePolicyNumber = insurancePolicyNumber?.Trim();
            InsuranceExpiryDate = insuranceExpiryDate;
            LicenseNumber = licenseNumber?.Trim();
            LicenseExpiryDate = licenseExpiryDate;
            DotNumber = dotNumber?.Trim();
            McNumber = mcNumber?.Trim();
            ScacCode = scacCode?.Trim().ToUpperInvariant();
            IataCode = iataCode?.Trim().ToUpperInvariant();
            CustomsCode = customsCode?.Trim().ToUpperInvariant();

            Update(updatedBy);
        }

        public void Activate(string updatedBy)
        {
            if (Status == CarrierStatus.Terminated)
                throw new DomainException("Cannot activate a terminated carrier");

            Status = CarrierStatus.Active;
            IsActive = true;
            Update(updatedBy);

            AddDomainEvent(new CarrierActivatedEvent(Id, OrganizationId, Name));
        }

        public void Deactivate(string reason, string updatedBy)
        {
            Status = CarrierStatus.Inactive;
            IsActive = false;
            Update(updatedBy);

            AddDomainEvent(new CarrierDeactivatedEvent(Id, OrganizationId, Name, reason));
        }

        public void Suspend(string reason, string updatedBy)
        {
            Status = CarrierStatus.Suspended;
            IsActive = false;
            Update(updatedBy);

            AddDomainEvent(new CarrierSuspendedEvent(Id, OrganizationId, Name, reason));
        }

        public void Terminate(string reason, string updatedBy)
        {
            Status = CarrierStatus.Terminated;
            IsActive = false;
            Update(updatedBy);

            AddDomainEvent(new CarrierTerminatedEvent(Id, OrganizationId, Name, reason));
        }

        public void UpdateHealthCheck(bool passed, string? message, string updatedBy)
        {
            LastHealthCheck = DateTime.UtcNow;
            HealthCheckPassed = passed;
            HealthCheckMessage = message?.Trim();
            Update(updatedBy);

            if (!passed)
            {
                AddDomainEvent(new CarrierHealthCheckFailedEvent(Id, OrganizationId, Name, message ?? "Health check failed"));
            }
        }

        public void SetPriority(int priority, string updatedBy)
        {
            if (priority < 1 || priority > 1000)
                throw new DomainException("Priority must be between 1 and 1000");

            Priority = priority;
            Update(updatedBy);
        }

        public void AddService(CarrierService service)
        {
            if (service == null)
                throw new DomainException("Service cannot be null");

            if (_services.Any(s => s.ServiceCode == service.ServiceCode))
                throw new DomainException($"Service with code '{service.ServiceCode}' already exists");

            _services.Add(service);
        }

        public void RemoveService(string serviceCode)
        {
            var service = _services.FirstOrDefault(s => s.ServiceCode == serviceCode);
            if (service != null)
            {
                _services.Remove(service);
            }
        }

        public void AddAccount(CarrierAccount account)
        {
            if (account == null)
                throw new DomainException("Account cannot be null");

            if (_accounts.Any(a => a.AccountNumber == account.AccountNumber))
                throw new DomainException($"Account with number '{account.AccountNumber}' already exists");

            _accounts.Add(account);
        }

        public void RemoveAccount(string accountNumber)
        {
            var account = _accounts.FirstOrDefault(a => a.AccountNumber == accountNumber);
            if (account != null)
            {
                _accounts.Remove(account);
            }
        }

        public bool CanHandleWeight(Weight weight)
        {
            if (MaxWeight == null || string.IsNullOrWhiteSpace(MaxWeightUnit))
                return true;

            var maxWeight = new Weight(MaxWeight.Value, MaxWeightUnit);
            return !weight.IsGreaterThan(maxWeight);
        }

        public bool CanHandleDimensions(Dimensions dimensions)
        {
            if (MaxDimensions == null || string.IsNullOrWhiteSpace(MaxDimensionsUnit))
                return true;

            var maxDimensions = new Dimensions(MaxDimensions.Value, MaxDimensions.Value, MaxDimensions.Value, MaxDimensionsUnit);
            return dimensions.GetLongestSide() <= maxDimensions.GetLongestSide();
        }

        public bool IsOperational()
        {
            return IsActive && Status == CarrierStatus.Active && HealthCheckPassed;
        }

        public bool IsContractValid()
        {
            if (ContractStartDate == null || ContractEndDate == null)
                return true; // No contract restrictions

            var now = DateTime.UtcNow;
            return now >= ContractStartDate && now <= ContractEndDate;
        }

        public bool IsComplianceValid()
        {
            var now = DateTime.UtcNow;

            if (InsuranceExpiryDate.HasValue && InsuranceExpiryDate.Value < now)
                return false;

            if (LicenseExpiryDate.HasValue && LicenseExpiryDate.Value < now)
                return false;

            return true;
        }
    }
}
