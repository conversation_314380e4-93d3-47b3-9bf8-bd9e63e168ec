using CarrierIntegration.Domain.Common;
using CarrierIntegration.Domain.Enums;
using CarrierIntegration.Domain.Events;
using CarrierIntegration.Domain.ValueObjects;
using System;

namespace CarrierIntegration.Domain.Entities
{
    public class CarrierBooking : BaseEntity
    {
        public Guid CarrierId { get; private set; }
        public Guid? CarrierAccountId { get; private set; }
        public Guid? CarrierServiceId { get; private set; }
        public Guid? OrderId { get; private set; }
        public Guid? ShipmentId { get; private set; }
        public string BookingReference { get; private set; }
        public string? CarrierBookingReference { get; private set; }
        public BookingStatus Status { get; private set; }
        public string? StatusMessage { get; private set; }
        public DateTime BookingDate { get; private set; }
        public DateTime? ConfirmationDate { get; private set; }
        public DateTime? ExpirationDate { get; private set; }
        public DateTime RequestedPickupDate { get; private set; }
        public DateTime? RequestedDeliveryDate { get; private set; }
        public DateTime? ConfirmedPickupDate { get; private set; }
        public DateTime? ConfirmedDeliveryDate { get; private set; }
        public TimeSpan? PickupTimeWindow { get; private set; }
        public TimeSpan? DeliveryTimeWindow { get; private set; }
        public Address PickupAddress { get; private set; }
        public Address DeliveryAddress { get; private set; }
        public ContactInfo PickupContact { get; private set; }
        public ContactInfo DeliveryContact { get; private set; }
        public string? PickupInstructions { get; private set; }
        public string? DeliveryInstructions { get; private set; }
        public Weight TotalWeight { get; private set; }
        public Dimensions? TotalDimensions { get; private set; }
        public int PackageCount { get; private set; }
        public string? PackageDescription { get; private set; }
        public Money? DeclaredValue { get; private set; }
        public string? CommodityCode { get; private set; }
        public string? HazmatClass { get; private set; }
        public bool RequiresSignature { get; private set; }
        public bool RequiresInsurance { get; private set; }
        public bool IsCOD { get; private set; }
        public Money? CODAmount { get; private set; }
        public string? CODPaymentMethod { get; private set; }
        public bool IsResidentialPickup { get; private set; }
        public bool IsResidentialDelivery { get; private set; }
        public bool RequiresAppointment { get; private set; }
        public bool RequiresLiftgate { get; private set; }
        public bool RequiresInsideDelivery { get; private set; }
        public bool RequiresWhiteGlove { get; private set; }
        public string? SpecialServices { get; private set; }
        public string? AccessorialServices { get; private set; }
        public Money? EstimatedCost { get; private set; }
        public Money? ActualCost { get; private set; }
        public string? CostBreakdown { get; private set; }
        public string? CarrierServiceCode { get; private set; }
        public string? CarrierServiceName { get; private set; }
        public int? EstimatedTransitDays { get; private set; }
        public string? TrackingNumber { get; private set; }
        public string? LabelUrl { get; private set; }
        public string? DocumentsUrl { get; private set; }
        public string? ManifestNumber { get; private set; }
        public string? BillOfLadingNumber { get; private set; }
        public string? ProNumber { get; private set; }
        public string? EquipmentType { get; private set; }
        public string? EquipmentNumber { get; private set; }
        public string? DriverName { get; private set; }
        public string? DriverPhone { get; private set; }
        public string? VehicleInfo { get; private set; }
        public string? CarrierNotes { get; private set; }
        public string? InternalNotes { get; private set; }
        public string? CustomerReference { get; private set; }
        public string? ShipperReference { get; private set; }
        public string? PoNumber { get; private set; }
        public string? InvoiceNumber { get; private set; }
        public DateTime? CancelledDate { get; private set; }
        public string? CancellationReason { get; private set; }
        public string? CancelledBy { get; private set; }
        public DateTime? CompletedDate { get; private set; }
        public string? CompletionNotes { get; private set; }
        public string? ApiRequestData { get; private set; }
        public string? ApiResponseData { get; private set; }
        public DateTime? LastApiCall { get; private set; }
        public int ApiCallCount { get; private set; }
        public string? LastError { get; private set; }
        public DateTime? LastErrorDate { get; private set; }
        public int RetryCount { get; private set; }
        public DateTime? NextRetryDate { get; private set; }

        private CarrierBooking() { } // For EF Core

        public CarrierBooking(
            Guid carrierId,
            Guid organizationId,
            string bookingReference,
            DateTime requestedPickupDate,
            Address pickupAddress,
            Address deliveryAddress,
            ContactInfo pickupContact,
            ContactInfo deliveryContact,
            Weight totalWeight,
            int packageCount,
            string createdBy = "System") : base(organizationId, createdBy)
        {
            if (carrierId == Guid.Empty)
                throw new DomainException("Carrier ID is required");
            if (string.IsNullOrWhiteSpace(bookingReference))
                throw new DomainException("Booking reference is required");
            if (requestedPickupDate < DateTime.UtcNow.Date)
                throw new DomainException("Requested pickup date cannot be in the past");
            if (packageCount <= 0)
                throw new DomainException("Package count must be greater than zero");

            CarrierId = carrierId;
            BookingReference = bookingReference.Trim();
            Status = BookingStatus.Requested;
            BookingDate = DateTime.UtcNow;
            RequestedPickupDate = requestedPickupDate;
            PickupAddress = pickupAddress ?? throw new DomainException("Pickup address is required");
            DeliveryAddress = deliveryAddress ?? throw new DomainException("Delivery address is required");
            PickupContact = pickupContact ?? throw new DomainException("Pickup contact is required");
            DeliveryContact = deliveryContact ?? throw new DomainException("Delivery contact is required");
            TotalWeight = totalWeight ?? throw new DomainException("Total weight is required");
            PackageCount = packageCount;
            RequiresSignature = false;
            RequiresInsurance = false;
            IsCOD = false;
            IsResidentialPickup = false;
            IsResidentialDelivery = false;
            RequiresAppointment = false;
            RequiresLiftgate = false;
            RequiresInsideDelivery = false;
            RequiresWhiteGlove = false;
            ApiCallCount = 0;
            RetryCount = 0;

            AddDomainEvent(new CarrierBookingCreatedEvent(Id, OrganizationId, CarrierId, BookingReference));
        }

        public void UpdateCarrierInfo(
            Guid? carrierAccountId,
            Guid? carrierServiceId,
            string? carrierServiceCode,
            string? carrierServiceName,
            string updatedBy)
        {
            CarrierAccountId = carrierAccountId;
            CarrierServiceId = carrierServiceId;
            CarrierServiceCode = carrierServiceCode?.Trim();
            CarrierServiceName = carrierServiceName?.Trim();

            Update(updatedBy);
        }

        public void UpdateOrderInfo(
            Guid? orderId,
            Guid? shipmentId,
            string? customerReference,
            string? shipperReference,
            string? poNumber,
            string updatedBy)
        {
            OrderId = orderId;
            ShipmentId = shipmentId;
            CustomerReference = customerReference?.Trim();
            ShipperReference = shipperReference?.Trim();
            PoNumber = poNumber?.Trim();

            Update(updatedBy);
        }

        public void UpdateSchedule(
            DateTime requestedPickupDate,
            DateTime? requestedDeliveryDate,
            TimeSpan? pickupTimeWindow,
            TimeSpan? deliveryTimeWindow,
            string updatedBy)
        {
            if (requestedPickupDate < DateTime.UtcNow.Date)
                throw new DomainException("Requested pickup date cannot be in the past");
            if (requestedDeliveryDate.HasValue && requestedDeliveryDate.Value <= requestedPickupDate)
                throw new DomainException("Requested delivery date must be after pickup date");

            RequestedPickupDate = requestedPickupDate;
            RequestedDeliveryDate = requestedDeliveryDate;
            PickupTimeWindow = pickupTimeWindow;
            DeliveryTimeWindow = deliveryTimeWindow;

            Update(updatedBy);
        }

        public void UpdateAddresses(
            Address pickupAddress,
            Address deliveryAddress,
            ContactInfo pickupContact,
            ContactInfo deliveryContact,
            string? pickupInstructions,
            string? deliveryInstructions,
            string updatedBy)
        {
            PickupAddress = pickupAddress ?? throw new DomainException("Pickup address is required");
            DeliveryAddress = deliveryAddress ?? throw new DomainException("Delivery address is required");
            PickupContact = pickupContact ?? throw new DomainException("Pickup contact is required");
            DeliveryContact = deliveryContact ?? throw new DomainException("Delivery contact is required");
            PickupInstructions = pickupInstructions?.Trim();
            DeliveryInstructions = deliveryInstructions?.Trim();

            Update(updatedBy);
        }

        public void UpdatePackageInfo(
            Weight totalWeight,
            Dimensions? totalDimensions,
            int packageCount,
            string? packageDescription,
            Money? declaredValue,
            string? commodityCode,
            string? hazmatClass,
            string updatedBy)
        {
            if (packageCount <= 0)
                throw new DomainException("Package count must be greater than zero");

            TotalWeight = totalWeight ?? throw new DomainException("Total weight is required");
            TotalDimensions = totalDimensions;
            PackageCount = packageCount;
            PackageDescription = packageDescription?.Trim();
            DeclaredValue = declaredValue;
            CommodityCode = commodityCode?.Trim();
            HazmatClass = hazmatClass?.Trim();

            Update(updatedBy);
        }

        public void UpdateServiceRequirements(
            bool requiresSignature,
            bool requiresInsurance,
            bool isCOD,
            Money? codAmount,
            string? codPaymentMethod,
            bool isResidentialPickup,
            bool isResidentialDelivery,
            bool requiresAppointment,
            bool requiresLiftgate,
            bool requiresInsideDelivery,
            bool requiresWhiteGlove,
            string? specialServices,
            string? accessorialServices,
            string updatedBy)
        {
            if (isCOD && codAmount == null)
                throw new DomainException("COD amount is required when COD is enabled");

            RequiresSignature = requiresSignature;
            RequiresInsurance = requiresInsurance;
            IsCOD = isCOD;
            CODAmount = codAmount;
            CODPaymentMethod = codPaymentMethod?.Trim();
            IsResidentialPickup = isResidentialPickup;
            IsResidentialDelivery = isResidentialDelivery;
            RequiresAppointment = requiresAppointment;
            RequiresLiftgate = requiresLiftgate;
            RequiresInsideDelivery = requiresInsideDelivery;
            RequiresWhiteGlove = requiresWhiteGlove;
            SpecialServices = specialServices?.Trim();
            AccessorialServices = accessorialServices?.Trim();

            Update(updatedBy);
        }

        public void UpdateCostInfo(
            Money? estimatedCost,
            Money? actualCost,
            string? costBreakdown,
            int? estimatedTransitDays,
            string updatedBy)
        {
            if (estimatedTransitDays.HasValue && estimatedTransitDays.Value < 0)
                throw new DomainException("Estimated transit days cannot be negative");

            EstimatedCost = estimatedCost;
            ActualCost = actualCost;
            CostBreakdown = costBreakdown?.Trim();
            EstimatedTransitDays = estimatedTransitDays;

            Update(updatedBy);
        }

        public void UpdateCarrierResponse(
            string? carrierBookingReference,
            string? trackingNumber,
            string? labelUrl,
            string? documentsUrl,
            string? manifestNumber,
            string? billOfLadingNumber,
            string? proNumber,
            string updatedBy)
        {
            CarrierBookingReference = carrierBookingReference?.Trim();
            TrackingNumber = trackingNumber?.Trim();
            LabelUrl = labelUrl?.Trim();
            DocumentsUrl = documentsUrl?.Trim();
            ManifestNumber = manifestNumber?.Trim();
            BillOfLadingNumber = billOfLadingNumber?.Trim();
            ProNumber = proNumber?.Trim();

            Update(updatedBy);
        }

        public void UpdateEquipmentInfo(
            string? equipmentType,
            string? equipmentNumber,
            string? driverName,
            string? driverPhone,
            string? vehicleInfo,
            string updatedBy)
        {
            EquipmentType = equipmentType?.Trim();
            EquipmentNumber = equipmentNumber?.Trim();
            DriverName = driverName?.Trim();
            DriverPhone = driverPhone?.Trim();
            VehicleInfo = vehicleInfo?.Trim();

            Update(updatedBy);
        }

        public void UpdateNotes(
            string? carrierNotes,
            string? internalNotes,
            string updatedBy)
        {
            CarrierNotes = carrierNotes?.Trim();
            InternalNotes = internalNotes?.Trim();

            Update(updatedBy);
        }

        public void Confirm(
            DateTime? confirmedPickupDate,
            DateTime? confirmedDeliveryDate,
            string? carrierBookingReference,
            string updatedBy)
        {
            if (Status != BookingStatus.Requested)
                throw new DomainException($"Cannot confirm booking with status {Status}");

            Status = BookingStatus.Confirmed;
            ConfirmationDate = DateTime.UtcNow;
            ConfirmedPickupDate = confirmedPickupDate;
            ConfirmedDeliveryDate = confirmedDeliveryDate;
            CarrierBookingReference = carrierBookingReference?.Trim();

            Update(updatedBy);

            AddDomainEvent(new CarrierBookingConfirmedEvent(Id, OrganizationId, CarrierId, BookingReference));
        }

        public void Modify(string reason, string updatedBy)
        {
            if (Status == BookingStatus.Completed || Status == BookingStatus.Cancelled)
                throw new DomainException($"Cannot modify booking with status {Status}");

            Status = BookingStatus.Modified;
            StatusMessage = reason?.Trim();

            Update(updatedBy);

            AddDomainEvent(new CarrierBookingModifiedEvent(Id, OrganizationId, CarrierId, BookingReference, reason));
        }

        public void Cancel(string reason, string updatedBy)
        {
            if (Status == BookingStatus.Completed)
                throw new DomainException("Cannot cancel a completed booking");

            Status = BookingStatus.Cancelled;
            CancelledDate = DateTime.UtcNow;
            CancellationReason = reason?.Trim();
            CancelledBy = updatedBy;

            Update(updatedBy);

            AddDomainEvent(new CarrierBookingCancelledEvent(Id, OrganizationId, CarrierId, BookingReference, reason));
        }

        public void Complete(string? completionNotes, string updatedBy)
        {
            if (Status != BookingStatus.Confirmed)
                throw new DomainException($"Cannot complete booking with status {Status}");

            Status = BookingStatus.Completed;
            CompletedDate = DateTime.UtcNow;
            CompletionNotes = completionNotes?.Trim();

            Update(updatedBy);

            AddDomainEvent(new CarrierBookingCompletedEvent(Id, OrganizationId, CarrierId, BookingReference));
        }

        public void Fail(string reason, string updatedBy)
        {
            Status = BookingStatus.Failed;
            StatusMessage = reason?.Trim();
            LastError = reason?.Trim();
            LastErrorDate = DateTime.UtcNow;

            Update(updatedBy);

            AddDomainEvent(new CarrierBookingFailedEvent(Id, OrganizationId, CarrierId, BookingReference, reason));
        }

        public void SetOnHold(string reason, string updatedBy)
        {
            Status = BookingStatus.OnHold;
            StatusMessage = reason?.Trim();

            Update(updatedBy);
        }

        public void SetExpiration(DateTime expirationDate, string updatedBy)
        {
            if (expirationDate <= DateTime.UtcNow)
                throw new DomainException("Expiration date must be in the future");

            ExpirationDate = expirationDate;

            Update(updatedBy);
        }

        public void RecordApiCall(string? requestData, string? responseData, string updatedBy)
        {
            ApiRequestData = requestData?.Trim();
            ApiResponseData = responseData?.Trim();
            LastApiCall = DateTime.UtcNow;
            ApiCallCount++;

            Update(updatedBy);
        }

        public void RecordError(string error, string updatedBy)
        {
            LastError = error?.Trim();
            LastErrorDate = DateTime.UtcNow;

            Update(updatedBy);
        }

        public void ScheduleRetry(DateTime nextRetryDate, string updatedBy)
        {
            RetryCount++;
            NextRetryDate = nextRetryDate;

            Update(updatedBy);
        }

        public void ResetRetry(string updatedBy)
        {
            RetryCount = 0;
            NextRetryDate = null;
            LastError = null;
            LastErrorDate = null;

            Update(updatedBy);
        }

        public bool IsExpired()
        {
            return ExpirationDate.HasValue && DateTime.UtcNow > ExpirationDate.Value;
        }

        public bool CanBeModified()
        {
            return Status == BookingStatus.Requested || Status == BookingStatus.OnHold;
        }

        public bool CanBeCancelled()
        {
            return Status != BookingStatus.Completed && Status != BookingStatus.Cancelled;
        }

        public bool IsReadyForRetry()
        {
            return NextRetryDate.HasValue && DateTime.UtcNow >= NextRetryDate.Value;
        }

        public bool HasTrackingInfo()
        {
            return !string.IsNullOrWhiteSpace(TrackingNumber);
        }

        public bool HasLabelInfo()
        {
            return !string.IsNullOrWhiteSpace(LabelUrl);
        }

        public bool RequiresSpecialHandling()
        {
            return RequiresLiftgate || RequiresInsideDelivery || RequiresWhiteGlove || 
                   !string.IsNullOrWhiteSpace(HazmatClass) || 
                   !string.IsNullOrWhiteSpace(SpecialServices);
        }
    }
}
