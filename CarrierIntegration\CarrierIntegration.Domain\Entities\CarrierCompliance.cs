using CarrierIntegration.Domain.Common;
using System;

namespace CarrierIntegration.Domain.Entities
{
    public class CarrierCompliance : BaseEntity
    {
        public Guid CarrierId { get; private set; }
        public string ComplianceType { get; private set; } // Insurance, License, Certification, etc.
        public string ComplianceName { get; private set; }
        public string? ComplianceNumber { get; private set; }
        public string IssuingAuthority { get; private set; }
        public DateTime IssueDate { get; private set; }
        public DateTime ExpirationDate { get; private set; }
        public string Status { get; private set; } // Valid, Expired, Suspended, Revoked
        public string? DocumentUrl { get; private set; }
        public string? DocumentPath { get; private set; }
        public string? Notes { get; private set; }
        public bool IsRequired { get; private set; }
        public bool IsActive { get; private set; }
        public DateTime? LastVerificationDate { get; private set; }
        public string? LastVerificationBy { get; private set; }
        public string? VerificationMethod { get; private set; }
        public DateTime? NextVerificationDate { get; private set; }
        public string? RenewalProcess { get; private set; }
        public string? ContactPerson { get; private set; }
        public string? ContactEmail { get; private set; }
        public string? ContactPhone { get; private set; }
        public decimal? CoverageAmount { get; private set; }
        public string? CoverageCurrency { get; private set; }
        public string? PolicyDetails { get; private set; }
        public string? Restrictions { get; private set; }
        public string? GeographicScope { get; private set; }
        public string? ServiceScope { get; private set; }

        private CarrierCompliance() { } // For EF Core

        public CarrierCompliance(
            Guid carrierId,
            Guid organizationId,
            string complianceType,
            string complianceName,
            string issuingAuthority,
            DateTime issueDate,
            DateTime expirationDate,
            bool isRequired = true,
            string createdBy = "System") : base(organizationId, createdBy)
        {
            if (carrierId == Guid.Empty)
                throw new DomainException("Carrier ID is required");
            if (string.IsNullOrWhiteSpace(complianceType))
                throw new DomainException("Compliance type is required");
            if (string.IsNullOrWhiteSpace(complianceName))
                throw new DomainException("Compliance name is required");
            if (string.IsNullOrWhiteSpace(issuingAuthority))
                throw new DomainException("Issuing authority is required");
            if (issueDate >= expirationDate)
                throw new DomainException("Issue date must be before expiration date");

            CarrierId = carrierId;
            ComplianceType = complianceType.Trim();
            ComplianceName = complianceName.Trim();
            IssuingAuthority = issuingAuthority.Trim();
            IssueDate = issueDate;
            ExpirationDate = expirationDate;
            IsRequired = isRequired;
            IsActive = true;
            Status = "Valid";
        }

        public void UpdateBasicInfo(
            string complianceName,
            string? complianceNumber,
            string issuingAuthority,
            DateTime issueDate,
            DateTime expirationDate,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(complianceName))
                throw new DomainException("Compliance name is required");
            if (string.IsNullOrWhiteSpace(issuingAuthority))
                throw new DomainException("Issuing authority is required");
            if (issueDate >= expirationDate)
                throw new DomainException("Issue date must be before expiration date");

            ComplianceName = complianceName.Trim();
            ComplianceNumber = complianceNumber?.Trim();
            IssuingAuthority = issuingAuthority.Trim();
            IssueDate = issueDate;
            ExpirationDate = expirationDate;

            Update(updatedBy);
        }

        public void UpdateDocumentation(
            string? documentUrl,
            string? documentPath,
            string? notes,
            string updatedBy)
        {
            DocumentUrl = documentUrl?.Trim();
            DocumentPath = documentPath?.Trim();
            Notes = notes?.Trim();

            Update(updatedBy);
        }

        public void UpdateContactInfo(
            string? contactPerson,
            string? contactEmail,
            string? contactPhone,
            string updatedBy)
        {
            ContactPerson = contactPerson?.Trim();
            ContactEmail = contactEmail?.Trim();
            ContactPhone = contactPhone?.Trim();

            Update(updatedBy);
        }

        public void UpdateCoverage(
            decimal? coverageAmount,
            string? coverageCurrency,
            string? policyDetails,
            string updatedBy)
        {
            if (coverageAmount.HasValue && coverageAmount.Value < 0)
                throw new DomainException("Coverage amount cannot be negative");

            CoverageAmount = coverageAmount;
            CoverageCurrency = coverageCurrency?.Trim();
            PolicyDetails = policyDetails?.Trim();

            Update(updatedBy);
        }

        public void UpdateScope(
            string? restrictions,
            string? geographicScope,
            string? serviceScope,
            string updatedBy)
        {
            Restrictions = restrictions?.Trim();
            GeographicScope = geographicScope?.Trim();
            ServiceScope = serviceScope?.Trim();

            Update(updatedBy);
        }

        public void UpdateVerification(
            DateTime verificationDate,
            string verificationBy,
            string? verificationMethod,
            DateTime? nextVerificationDate,
            string updatedBy)
        {
            if (string.IsNullOrWhiteSpace(verificationBy))
                throw new DomainException("Verification by is required");

            LastVerificationDate = verificationDate;
            LastVerificationBy = verificationBy.Trim();
            VerificationMethod = verificationMethod?.Trim();
            NextVerificationDate = nextVerificationDate;

            Update(updatedBy);
        }

        public void UpdateRenewalProcess(
            string? renewalProcess,
            string updatedBy)
        {
            RenewalProcess = renewalProcess?.Trim();
            Update(updatedBy);
        }

        public void SetStatus(string status, string updatedBy)
        {
            var validStatuses = new[] { "Valid", "Expired", "Suspended", "Revoked", "Pending" };
            if (!Array.Exists(validStatuses, s => s.Equals(status, StringComparison.OrdinalIgnoreCase)))
                throw new DomainException($"Invalid status. Valid statuses are: {string.Join(", ", validStatuses)}");

            Status = status;
            Update(updatedBy);
        }

        public void Activate(string updatedBy)
        {
            IsActive = true;
            Update(updatedBy);
        }

        public void Deactivate(string updatedBy)
        {
            IsActive = false;
            Update(updatedBy);
        }

        public void SetRequired(bool isRequired, string updatedBy)
        {
            IsRequired = isRequired;
            Update(updatedBy);
        }

        public bool IsExpired()
        {
            return DateTime.UtcNow > ExpirationDate;
        }

        public bool IsExpiringSoon(int daysThreshold = 30)
        {
            return DateTime.UtcNow.AddDays(daysThreshold) >= ExpirationDate;
        }

        public bool IsValid()
        {
            return IsActive && 
                   Status.Equals("Valid", StringComparison.OrdinalIgnoreCase) && 
                   !IsExpired();
        }

        public int GetDaysUntilExpiration()
        {
            var daysUntil = (ExpirationDate - DateTime.UtcNow).Days;
            return Math.Max(0, daysUntil);
        }

        public bool RequiresVerification()
        {
            if (!NextVerificationDate.HasValue)
                return false;

            return DateTime.UtcNow >= NextVerificationDate.Value;
        }

        public string GetComplianceStatus()
        {
            if (!IsActive)
                return "Inactive";
            if (IsExpired())
                return "Expired";
            if (IsExpiringSoon())
                return "Expiring Soon";
            if (!Status.Equals("Valid", StringComparison.OrdinalIgnoreCase))
                return Status;
            if (RequiresVerification())
                return "Verification Required";
            
            return "Compliant";
        }
    }
}
