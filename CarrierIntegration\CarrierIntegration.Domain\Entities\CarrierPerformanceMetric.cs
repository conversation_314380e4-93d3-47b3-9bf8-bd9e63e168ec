using CarrierIntegration.Domain.Common;
using CarrierIntegration.Domain.ValueObjects;
using System;

namespace CarrierIntegration.Domain.Entities
{
    public class CarrierPerformanceMetric : BaseEntity
    {
        public Guid CarrierId { get; private set; }
        public DateTime PeriodStart { get; private set; }
        public DateTime PeriodEnd { get; private set; }
        public string MetricType { get; private set; } // OnTimeDelivery, TransitTime, ExceptionRate, etc.
        public decimal Value { get; private set; }
        public string Unit { get; private set; } // Percentage, Days, Count, etc.
        public int TotalShipments { get; private set; }
        public int SuccessfulShipments { get; private set; }
        public int FailedShipments { get; private set; }
        public int ExceptionShipments { get; private set; }
        public decimal AverageTransitTime { get; private set; }
        public decimal OnTimeDeliveryRate { get; private set; }
        public decimal ExceptionRate { get; private set; }
        public decimal DamageClaimRate { get; private set; }
        public decimal CostPerformanceIndex { get; private set; }
        public decimal CustomerSatisfactionScore { get; private set; }
        public Money? AverageCost { get; private set; }
        public Money? TotalRevenue { get; private set; }
        public string? Notes { get; private set; }
        public string? CalculationMethod { get; private set; }
        public DateTime CalculatedAt { get; private set; }
        public string CalculatedBy { get; private set; }

        private CarrierPerformanceMetric() { } // For EF Core

        public CarrierPerformanceMetric(
            Guid carrierId,
            Guid organizationId,
            DateTime periodStart,
            DateTime periodEnd,
            string metricType,
            decimal value,
            string unit,
            string createdBy = "System") : base(organizationId, createdBy)
        {
            if (carrierId == Guid.Empty)
                throw new DomainException("Carrier ID is required");
            if (periodStart >= periodEnd)
                throw new DomainException("Period start must be before period end");
            if (string.IsNullOrWhiteSpace(metricType))
                throw new DomainException("Metric type is required");
            if (string.IsNullOrWhiteSpace(unit))
                throw new DomainException("Unit is required");

            CarrierId = carrierId;
            PeriodStart = periodStart;
            PeriodEnd = periodEnd;
            MetricType = metricType.Trim();
            Value = value;
            Unit = unit.Trim();
            TotalShipments = 0;
            SuccessfulShipments = 0;
            FailedShipments = 0;
            ExceptionShipments = 0;
            AverageTransitTime = 0;
            OnTimeDeliveryRate = 0;
            ExceptionRate = 0;
            DamageClaimRate = 0;
            CostPerformanceIndex = 0;
            CustomerSatisfactionScore = 0;
            CalculatedAt = DateTime.UtcNow;
            CalculatedBy = createdBy;
        }

        public void UpdateShipmentCounts(
            int totalShipments,
            int successfulShipments,
            int failedShipments,
            int exceptionShipments,
            string updatedBy)
        {
            if (totalShipments < 0)
                throw new DomainException("Total shipments cannot be negative");
            if (successfulShipments < 0)
                throw new DomainException("Successful shipments cannot be negative");
            if (failedShipments < 0)
                throw new DomainException("Failed shipments cannot be negative");
            if (exceptionShipments < 0)
                throw new DomainException("Exception shipments cannot be negative");

            TotalShipments = totalShipments;
            SuccessfulShipments = successfulShipments;
            FailedShipments = failedShipments;
            ExceptionShipments = exceptionShipments;

            // Recalculate rates
            if (TotalShipments > 0)
            {
                OnTimeDeliveryRate = (decimal)SuccessfulShipments / TotalShipments * 100;
                ExceptionRate = (decimal)ExceptionShipments / TotalShipments * 100;
            }

            Update(updatedBy);
        }

        public void UpdatePerformanceMetrics(
            decimal averageTransitTime,
            decimal onTimeDeliveryRate,
            decimal exceptionRate,
            decimal damageClaimRate,
            decimal costPerformanceIndex,
            decimal customerSatisfactionScore,
            string updatedBy)
        {
            if (averageTransitTime < 0)
                throw new DomainException("Average transit time cannot be negative");
            if (onTimeDeliveryRate < 0 || onTimeDeliveryRate > 100)
                throw new DomainException("On-time delivery rate must be between 0 and 100");
            if (exceptionRate < 0 || exceptionRate > 100)
                throw new DomainException("Exception rate must be between 0 and 100");
            if (damageClaimRate < 0 || damageClaimRate > 100)
                throw new DomainException("Damage claim rate must be between 0 and 100");
            if (customerSatisfactionScore < 0 || customerSatisfactionScore > 10)
                throw new DomainException("Customer satisfaction score must be between 0 and 10");

            AverageTransitTime = averageTransitTime;
            OnTimeDeliveryRate = onTimeDeliveryRate;
            ExceptionRate = exceptionRate;
            DamageClaimRate = damageClaimRate;
            CostPerformanceIndex = costPerformanceIndex;
            CustomerSatisfactionScore = customerSatisfactionScore;

            Update(updatedBy);
        }

        public void UpdateFinancialMetrics(
            Money? averageCost,
            Money? totalRevenue,
            string updatedBy)
        {
            AverageCost = averageCost;
            TotalRevenue = totalRevenue;

            Update(updatedBy);
        }

        public void UpdateCalculationInfo(
            string? calculationMethod,
            string? notes,
            string updatedBy)
        {
            CalculationMethod = calculationMethod?.Trim();
            Notes = notes?.Trim();
            CalculatedAt = DateTime.UtcNow;
            CalculatedBy = updatedBy;

            Update(updatedBy);
        }

        public bool IsCurrentPeriod()
        {
            var now = DateTime.UtcNow;
            return now >= PeriodStart && now <= PeriodEnd;
        }

        public bool IsValidMetric()
        {
            return TotalShipments > 0 && 
                   OnTimeDeliveryRate >= 0 && OnTimeDeliveryRate <= 100 &&
                   ExceptionRate >= 0 && ExceptionRate <= 100;
        }

        public decimal GetSuccessRate()
        {
            return TotalShipments > 0 ? (decimal)SuccessfulShipments / TotalShipments * 100 : 0;
        }

        public decimal GetFailureRate()
        {
            return TotalShipments > 0 ? (decimal)FailedShipments / TotalShipments * 100 : 0;
        }

        public string GetPerformanceGrade()
        {
            var score = (OnTimeDeliveryRate * 0.4m) + 
                       ((100 - ExceptionRate) * 0.3m) + 
                       (CustomerSatisfactionScore * 10 * 0.2m) + 
                       ((100 - DamageClaimRate) * 0.1m);

            return score switch
            {
                >= 90 => "A",
                >= 80 => "B", 
                >= 70 => "C",
                >= 60 => "D",
                _ => "F"
            };
        }
    }
}
