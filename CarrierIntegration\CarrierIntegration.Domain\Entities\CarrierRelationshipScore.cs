using CarrierIntegration.Domain.Common;
using System;
using System.Collections.Generic;
using System.Linq;

namespace CarrierIntegration.Domain.Entities
{
    public class CarrierRelationshipScore : BaseEntity
    {
        public Guid CarrierId { get; private set; }
        public DateTime CalculationDate { get; private set; }
        public DateTime PeriodStart { get; private set; }
        public DateTime PeriodEnd { get; private set; }
        
        // Overall Relationship Score (0-100)
        public decimal OverallScore { get; private set; }
        public string ScoreGrade { get; private set; } = null!; // A+, A, B+, B, C+, C, D, F
        public string ScoreTrend { get; private set; } = null!; // Improving, Stable, Declining
        
        // Component Scores (0-100 each)
        public decimal PerformanceScore { get; private set; }
        public decimal ReliabilityScore { get; private set; }
        public decimal CostEffectivenessScore { get; private set; }
        public decimal ServiceQualityScore { get; private set; }
        public decimal ComplianceScore { get; private set; }
        public decimal CommunicationScore { get; private set; }
        public decimal InnovationScore { get; private set; }
        public decimal StrategicValueScore { get; private set; }
        
        // Performance Metrics
        public decimal OnTimeDeliveryRate { get; private set; }
        public decimal DamageRate { get; private set; }
        public decimal ClaimResolutionTime { get; private set; }
        public decimal CustomerSatisfactionScore { get; private set; }
        public decimal ServiceLevelCompliance { get; private set; }
        
        // Financial Metrics
        public decimal CostCompetitiveness { get; private set; }
        public decimal VolumeCommitmentCompliance { get; private set; }
        public decimal PaymentTermsCompliance { get; private set; }
        public decimal TotalSpend { get; private set; }
        public decimal CostSavingsAchieved { get; private set; }
        
        // Relationship Metrics
        public int TotalShipments { get; private set; }
        public int SuccessfulShipments { get; private set; }
        public int FailedShipments { get; private set; }
        public int ExceptionShipments { get; private set; }
        public int EscalationsCount { get; private set; }
        public int IssuesResolvedCount { get; private set; }
        public decimal AverageIssueResolutionTime { get; private set; }
        
        // Strategic Metrics
        public decimal CapacityUtilization { get; private set; }
        public decimal ServiceExpansionRate { get; private set; }
        public decimal TechnologyAdoptionScore { get; private set; }
        public decimal SustainabilityScore { get; private set; }
        public decimal RiskScore { get; private set; }
        
        // Scoring Weights (configurable)
        public decimal PerformanceWeight { get; private set; } = 25m;
        public decimal ReliabilityWeight { get; private set; } = 20m;
        public decimal CostEffectivenessWeight { get; private set; } = 20m;
        public decimal ServiceQualityWeight { get; private set; } = 15m;
        public decimal ComplianceWeight { get; private set; } = 10m;
        public decimal CommunicationWeight { get; private set; } = 5m;
        public decimal InnovationWeight { get; private set; } = 3m;
        public decimal StrategicValueWeight { get; private set; } = 2m;
        
        // Recommendations and Actions
        public string? StrengthAreas { get; private set; }
        public string? ImprovementAreas { get; private set; }
        public string? RecommendedActions { get; private set; }
        public string? RiskFactors { get; private set; }
        public string? OpportunityAreas { get; private set; }
        public string? NextReviewDate { get; private set; }
        
        // Calculation Metadata
        public string CalculationMethod { get; private set; } = null!;
        public string DataSources { get; private set; } = null!;
        public decimal ConfidenceLevel { get; private set; }
        public string? Notes { get; private set; }

        private CarrierRelationshipScore() { } // For EF Core

        public CarrierRelationshipScore(
            Guid carrierId,
            Guid organizationId,
            DateTime periodStart,
            DateTime periodEnd,
            string calculationMethod = "Standard",
            string createdBy = "System") : base(organizationId, createdBy)
        {
            if (carrierId == Guid.Empty)
                throw new DomainException("Carrier ID is required");
            if (periodStart >= periodEnd)
                throw new DomainException("Period start must be before period end");
            if (string.IsNullOrWhiteSpace(calculationMethod))
                throw new DomainException("Calculation method is required");

            CarrierId = carrierId;
            CalculationDate = DateTime.UtcNow;
            PeriodStart = periodStart;
            PeriodEnd = periodEnd;
            CalculationMethod = calculationMethod.Trim();
            DataSources = "Performance Metrics, Financial Data, Compliance Records";
            ConfidenceLevel = 0.85m;
            
            // Initialize scores to zero
            OverallScore = 0;
            ScoreGrade = "N/A";
            ScoreTrend = "Stable";
            PerformanceScore = 0;
            ReliabilityScore = 0;
            CostEffectivenessScore = 0;
            ServiceQualityScore = 0;
            ComplianceScore = 0;
            CommunicationScore = 0;
            InnovationScore = 0;
            StrategicValueScore = 0;
        }

        public void UpdatePerformanceMetrics(
            decimal onTimeDeliveryRate,
            decimal damageRate,
            decimal claimResolutionTime,
            decimal customerSatisfactionScore,
            decimal serviceLevelCompliance,
            string updatedBy)
        {
            ValidatePercentage(onTimeDeliveryRate, nameof(onTimeDeliveryRate));
            ValidatePercentage(damageRate, nameof(damageRate));
            ValidatePercentage(serviceLevelCompliance, nameof(serviceLevelCompliance));
            
            if (claimResolutionTime < 0)
                throw new DomainException("Claim resolution time cannot be negative");
            if (customerSatisfactionScore < 0 || customerSatisfactionScore > 10)
                throw new DomainException("Customer satisfaction score must be between 0 and 10");

            OnTimeDeliveryRate = onTimeDeliveryRate;
            DamageRate = damageRate;
            ClaimResolutionTime = claimResolutionTime;
            CustomerSatisfactionScore = customerSatisfactionScore;
            ServiceLevelCompliance = serviceLevelCompliance;

            // Recalculate performance score
            PerformanceScore = CalculatePerformanceScore();
            RecalculateOverallScore();
            
            Update(updatedBy);
        }

        public void UpdateFinancialMetrics(
            decimal costCompetitiveness,
            decimal volumeCommitmentCompliance,
            decimal paymentTermsCompliance,
            decimal totalSpend,
            decimal costSavingsAchieved,
            string updatedBy)
        {
            ValidatePercentage(costCompetitiveness, nameof(costCompetitiveness));
            ValidatePercentage(volumeCommitmentCompliance, nameof(volumeCommitmentCompliance));
            ValidatePercentage(paymentTermsCompliance, nameof(paymentTermsCompliance));
            
            if (totalSpend < 0)
                throw new DomainException("Total spend cannot be negative");

            CostCompetitiveness = costCompetitiveness;
            VolumeCommitmentCompliance = volumeCommitmentCompliance;
            PaymentTermsCompliance = paymentTermsCompliance;
            TotalSpend = totalSpend;
            CostSavingsAchieved = costSavingsAchieved;

            // Recalculate cost effectiveness score
            CostEffectivenessScore = CalculateCostEffectivenessScore();
            RecalculateOverallScore();
            
            Update(updatedBy);
        }

        public void UpdateShipmentMetrics(
            int totalShipments,
            int successfulShipments,
            int failedShipments,
            int exceptionShipments,
            int escalationsCount,
            int issuesResolvedCount,
            decimal averageIssueResolutionTime,
            string updatedBy)
        {
            if (totalShipments < 0)
                throw new DomainException("Total shipments cannot be negative");
            if (successfulShipments < 0)
                throw new DomainException("Successful shipments cannot be negative");
            if (failedShipments < 0)
                throw new DomainException("Failed shipments cannot be negative");
            if (exceptionShipments < 0)
                throw new DomainException("Exception shipments cannot be negative");
            if (escalationsCount < 0)
                throw new DomainException("Escalations count cannot be negative");
            if (issuesResolvedCount < 0)
                throw new DomainException("Issues resolved count cannot be negative");
            if (averageIssueResolutionTime < 0)
                throw new DomainException("Average issue resolution time cannot be negative");

            TotalShipments = totalShipments;
            SuccessfulShipments = successfulShipments;
            FailedShipments = failedShipments;
            ExceptionShipments = exceptionShipments;
            EscalationsCount = escalationsCount;
            IssuesResolvedCount = issuesResolvedCount;
            AverageIssueResolutionTime = averageIssueResolutionTime;

            // Recalculate reliability score
            ReliabilityScore = CalculateReliabilityScore();
            RecalculateOverallScore();
            
            Update(updatedBy);
        }

        public void UpdateStrategicMetrics(
            decimal capacityUtilization,
            decimal serviceExpansionRate,
            decimal technologyAdoptionScore,
            decimal sustainabilityScore,
            decimal riskScore,
            string updatedBy)
        {
            ValidatePercentage(capacityUtilization, nameof(capacityUtilization));
            ValidateScore(technologyAdoptionScore, nameof(technologyAdoptionScore));
            ValidateScore(sustainabilityScore, nameof(sustainabilityScore));
            ValidateScore(riskScore, nameof(riskScore));

            CapacityUtilization = capacityUtilization;
            ServiceExpansionRate = serviceExpansionRate;
            TechnologyAdoptionScore = technologyAdoptionScore;
            SustainabilityScore = sustainabilityScore;
            RiskScore = riskScore;

            // Recalculate strategic value score
            StrategicValueScore = CalculateStrategicValueScore();
            RecalculateOverallScore();
            
            Update(updatedBy);
        }

        public void UpdateComponentScores(
            decimal serviceQualityScore,
            decimal complianceScore,
            decimal communicationScore,
            decimal innovationScore,
            string updatedBy)
        {
            ValidateScore(serviceQualityScore, nameof(serviceQualityScore));
            ValidateScore(complianceScore, nameof(complianceScore));
            ValidateScore(communicationScore, nameof(communicationScore));
            ValidateScore(innovationScore, nameof(innovationScore));

            ServiceQualityScore = serviceQualityScore;
            ComplianceScore = complianceScore;
            CommunicationScore = communicationScore;
            InnovationScore = innovationScore;

            RecalculateOverallScore();
            Update(updatedBy);
        }

        public void UpdateScoringWeights(
            decimal performanceWeight,
            decimal reliabilityWeight,
            decimal costEffectivenessWeight,
            decimal serviceQualityWeight,
            decimal complianceWeight,
            decimal communicationWeight,
            decimal innovationWeight,
            decimal strategicValueWeight,
            string updatedBy)
        {
            var totalWeight = performanceWeight + reliabilityWeight + costEffectivenessWeight + 
                             serviceQualityWeight + complianceWeight + communicationWeight + 
                             innovationWeight + strategicValueWeight;

            if (Math.Abs(totalWeight - 100m) > 0.01m)
                throw new DomainException("Total weights must equal 100%");

            PerformanceWeight = performanceWeight;
            ReliabilityWeight = reliabilityWeight;
            CostEffectivenessWeight = costEffectivenessWeight;
            ServiceQualityWeight = serviceQualityWeight;
            ComplianceWeight = complianceWeight;
            CommunicationWeight = communicationWeight;
            InnovationWeight = innovationWeight;
            StrategicValueWeight = strategicValueWeight;

            RecalculateOverallScore();
            Update(updatedBy);
        }

        public void UpdateRecommendations(
            string? strengthAreas,
            string? improvementAreas,
            string? recommendedActions,
            string? riskFactors,
            string? opportunityAreas,
            string? nextReviewDate,
            string updatedBy)
        {
            StrengthAreas = strengthAreas?.Trim();
            ImprovementAreas = improvementAreas?.Trim();
            RecommendedActions = recommendedActions?.Trim();
            RiskFactors = riskFactors?.Trim();
            OpportunityAreas = opportunityAreas?.Trim();
            NextReviewDate = nextReviewDate?.Trim();

            Update(updatedBy);
        }

        public void UpdateScoreTrend(string trend, string updatedBy)
        {
            var validTrends = new[] { "Improving", "Stable", "Declining" };
            if (!validTrends.Contains(trend))
                throw new DomainException($"Invalid trend. Valid values are: {string.Join(", ", validTrends)}");

            ScoreTrend = trend;
            Update(updatedBy);
        }

        public void UpdateConfidenceLevel(decimal confidenceLevel, string dataSources, string? notes, string updatedBy)
        {
            if (confidenceLevel < 0 || confidenceLevel > 1)
                throw new DomainException("Confidence level must be between 0 and 1");
            if (string.IsNullOrWhiteSpace(dataSources))
                throw new DomainException("Data sources are required");

            ConfidenceLevel = confidenceLevel;
            DataSources = dataSources.Trim();
            Notes = notes?.Trim();

            Update(updatedBy);
        }

        private void RecalculateOverallScore()
        {
            OverallScore = (PerformanceScore * PerformanceWeight / 100m) +
                          (ReliabilityScore * ReliabilityWeight / 100m) +
                          (CostEffectivenessScore * CostEffectivenessWeight / 100m) +
                          (ServiceQualityScore * ServiceQualityWeight / 100m) +
                          (ComplianceScore * ComplianceWeight / 100m) +
                          (CommunicationScore * CommunicationWeight / 100m) +
                          (InnovationScore * InnovationWeight / 100m) +
                          (StrategicValueScore * StrategicValueWeight / 100m);

            ScoreGrade = CalculateGrade(OverallScore);
        }

        private decimal CalculatePerformanceScore()
        {
            var score = (OnTimeDeliveryRate * 0.4m) +
                       ((100 - DamageRate) * 0.2m) +
                       (ServiceLevelCompliance * 0.2m) +
                       (CustomerSatisfactionScore * 10 * 0.2m);

            return Math.Min(100, Math.Max(0, score));
        }

        private decimal CalculateReliabilityScore()
        {
            if (TotalShipments == 0) return 0;

            var successRate = (decimal)SuccessfulShipments / TotalShipments * 100;
            var exceptionRate = (decimal)ExceptionShipments / TotalShipments * 100;
            var escalationRate = TotalShipments > 0 ? (decimal)EscalationsCount / TotalShipments * 100 : 0;
            var resolutionEfficiency = AverageIssueResolutionTime > 0 ? Math.Min(100, 100 / AverageIssueResolutionTime * 24) : 100;

            var score = (successRate * 0.4m) +
                       ((100 - exceptionRate) * 0.3m) +
                       ((100 - escalationRate) * 0.2m) +
                       (resolutionEfficiency * 0.1m);

            return Math.Min(100, Math.Max(0, score));
        }

        private decimal CalculateCostEffectivenessScore()
        {
            var score = (CostCompetitiveness * 0.4m) +
                       (VolumeCommitmentCompliance * 0.3m) +
                       (PaymentTermsCompliance * 0.2m) +
                       (Math.Min(100, CostSavingsAchieved / Math.Max(1, TotalSpend) * 1000) * 0.1m);

            return Math.Min(100, Math.Max(0, score));
        }

        private decimal CalculateStrategicValueScore()
        {
            var score = (CapacityUtilization * 0.3m) +
                       (TechnologyAdoptionScore * 0.25m) +
                       (SustainabilityScore * 0.2m) +
                       ((100 - RiskScore) * 0.15m) +
                       (Math.Min(100, ServiceExpansionRate * 10) * 0.1m);

            return Math.Min(100, Math.Max(0, score));
        }

        private string CalculateGrade(decimal score)
        {
            return score switch
            {
                >= 97 => "A+",
                >= 93 => "A",
                >= 90 => "A-",
                >= 87 => "B+",
                >= 83 => "B",
                >= 80 => "B-",
                >= 77 => "C+",
                >= 73 => "C",
                >= 70 => "C-",
                >= 67 => "D+",
                >= 63 => "D",
                >= 60 => "D-",
                _ => "F"
            };
        }

        private static void ValidatePercentage(decimal value, string parameterName)
        {
            if (value < 0 || value > 100)
                throw new DomainException($"{parameterName} must be between 0 and 100");
        }

        private static void ValidateScore(decimal value, string parameterName)
        {
            if (value < 0 || value > 100)
                throw new DomainException($"{parameterName} must be between 0 and 100");
        }

        public bool IsExcellentPerformer()
        {
            return OverallScore >= 90;
        }

        public bool RequiresImprovement()
        {
            return OverallScore < 70;
        }

        public bool IsAtRisk()
        {
            return OverallScore < 60 || RiskScore > 70;
        }

        public List<string> GetTopStrengths()
        {
            var strengths = new List<(string Name, decimal Score)>
            {
                ("Performance", PerformanceScore),
                ("Reliability", ReliabilityScore),
                ("Cost Effectiveness", CostEffectivenessScore),
                ("Service Quality", ServiceQualityScore),
                ("Compliance", ComplianceScore),
                ("Communication", CommunicationScore),
                ("Innovation", InnovationScore),
                ("Strategic Value", StrategicValueScore)
            };

            return strengths
                .Where(s => s.Score >= 80)
                .OrderByDescending(s => s.Score)
                .Take(3)
                .Select(s => s.Name)
                .ToList();
        }

        public List<string> GetImprovementPriorities()
        {
            var areas = new List<(string Name, decimal Score)>
            {
                ("Performance", PerformanceScore),
                ("Reliability", ReliabilityScore),
                ("Cost Effectiveness", CostEffectivenessScore),
                ("Service Quality", ServiceQualityScore),
                ("Compliance", ComplianceScore),
                ("Communication", CommunicationScore),
                ("Innovation", InnovationScore),
                ("Strategic Value", StrategicValueScore)
            };

            return areas
                .Where(a => a.Score < 70)
                .OrderBy(a => a.Score)
                .Take(3)
                .Select(a => a.Name)
                .ToList();
        }
    }
}
