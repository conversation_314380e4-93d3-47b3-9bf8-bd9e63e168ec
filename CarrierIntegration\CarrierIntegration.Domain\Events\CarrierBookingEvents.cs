using CarrierIntegration.Domain.Common;
using System;

namespace CarrierIntegration.Domain.Events
{
    public class CarrierBookingCreatedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid BookingId { get; }
        public Guid CarrierId { get; }
        public string BookingReference { get; }

        public CarrierBookingCreatedEvent(Guid bookingId, Guid? organizationId, Guid carrierId, string bookingReference)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            BookingId = bookingId;
            CarrierId = carrierId;
            BookingReference = bookingReference;
        }
    }

    public class CarrierBookingConfirmedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid BookingId { get; }
        public Guid CarrierId { get; }
        public string BookingReference { get; }

        public CarrierBookingConfirmedEvent(Guid bookingId, Guid? organizationId, Guid carrierId, string bookingReference)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            BookingId = bookingId;
            CarrierId = carrierId;
            BookingReference = bookingReference;
        }
    }

    public class CarrierBookingModifiedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid BookingId { get; }
        public Guid CarrierId { get; }
        public string BookingReference { get; }
        public string Reason { get; }

        public CarrierBookingModifiedEvent(Guid bookingId, Guid? organizationId, Guid carrierId, string bookingReference, string reason)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            BookingId = bookingId;
            CarrierId = carrierId;
            BookingReference = bookingReference;
            Reason = reason;
        }
    }

    public class CarrierBookingCancelledEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid BookingId { get; }
        public Guid CarrierId { get; }
        public string BookingReference { get; }
        public string Reason { get; }

        public CarrierBookingCancelledEvent(Guid bookingId, Guid? organizationId, Guid carrierId, string bookingReference, string reason)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            BookingId = bookingId;
            CarrierId = carrierId;
            BookingReference = bookingReference;
            Reason = reason;
        }
    }

    public class CarrierBookingCompletedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid BookingId { get; }
        public Guid CarrierId { get; }
        public string BookingReference { get; }

        public CarrierBookingCompletedEvent(Guid bookingId, Guid? organizationId, Guid carrierId, string bookingReference)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            BookingId = bookingId;
            CarrierId = carrierId;
            BookingReference = bookingReference;
        }
    }

    public class CarrierBookingFailedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid BookingId { get; }
        public Guid CarrierId { get; }
        public string BookingReference { get; }
        public string Reason { get; }

        public CarrierBookingFailedEvent(Guid bookingId, Guid? organizationId, Guid carrierId, string bookingReference, string reason)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            BookingId = bookingId;
            CarrierId = carrierId;
            BookingReference = bookingReference;
            Reason = reason;
        }
    }
}
