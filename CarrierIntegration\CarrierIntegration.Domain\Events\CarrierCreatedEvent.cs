using CarrierIntegration.Domain.Common;
using CarrierIntegration.Domain.Enums;
using System;

namespace CarrierIntegration.Domain.Events
{
    public class CarrierCreatedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid CarrierId { get; }
        public string CarrierName { get; }
        public string CarrierCode { get; }
        public CarrierType CarrierType { get; }

        public CarrierCreatedEvent(Guid carrierId, Guid? organizationId, string carrierName, string carrierCode, CarrierType carrierType)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            CarrierId = carrierId;
            CarrierName = carrierName;
            CarrierCode = carrierCode;
            CarrierType = carrierType;
        }
    }
}
