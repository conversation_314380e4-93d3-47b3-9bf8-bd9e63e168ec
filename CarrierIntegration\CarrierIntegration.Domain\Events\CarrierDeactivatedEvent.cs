using CarrierIntegration.Domain.Common;
using System;

namespace CarrierIntegration.Domain.Events
{
    public class CarrierDeactivatedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid CarrierId { get; }
        public string CarrierName { get; }
        public string Reason { get; }

        public CarrierDeactivatedEvent(Guid carrierId, Guid? organizationId, string carrierName, string reason)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            CarrierId = carrierId;
            CarrierName = carrierName;
            Reason = reason;
        }
    }

    public class CarrierSuspendedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid CarrierId { get; }
        public string CarrierName { get; }
        public string Reason { get; }

        public CarrierSuspendedEvent(Guid carrierId, Guid? organizationId, string carrierName, string reason)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            CarrierId = carrierId;
            CarrierName = carrierName;
            Reason = reason;
        }
    }

    public class CarrierTerminatedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid CarrierId { get; }
        public string CarrierName { get; }
        public string Reason { get; }

        public CarrierTerminatedEvent(Guid carrierId, Guid? organizationId, string carrierName, string reason)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            CarrierId = carrierId;
            CarrierName = carrierName;
            Reason = reason;
        }
    }

    public class CarrierHealthCheckFailedEvent : IDomainEvent
    {
        public Guid Id { get; }
        public DateTime OccurredOn { get; }
        public Guid? OrganizationId { get; }
        public Guid CarrierId { get; }
        public string CarrierName { get; }
        public string Message { get; }

        public CarrierHealthCheckFailedEvent(Guid carrierId, Guid? organizationId, string carrierName, string message)
        {
            Id = Guid.NewGuid();
            OccurredOn = DateTime.UtcNow;
            OrganizationId = organizationId;
            CarrierId = carrierId;
            CarrierName = carrierName;
            Message = message;
        }
    }
}
