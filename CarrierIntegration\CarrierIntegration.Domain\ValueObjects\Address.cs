using CarrierIntegration.Domain.Common;
using System;
using System.Collections.Generic;

namespace CarrierIntegration.Domain.ValueObjects
{
    public class Address : ValueObject
    {
        public string Street1 { get; private set; }
        public string? Street2 { get; private set; }
        public string City { get; private set; }
        public string State { get; private set; }
        public string PostalCode { get; private set; }
        public string Country { get; private set; }
        public decimal? Latitude { get; private set; }
        public decimal? Longitude { get; private set; }

        private Address() { } // For EF Core

        public Address(
            string street1,
            string city,
            string state,
            string postalCode,
            string country,
            string? street2 = null,
            decimal? latitude = null,
            decimal? longitude = null)
        {
            if (string.IsNullOrWhiteSpace(street1))
                throw new DomainException("Street1 is required");
            if (string.IsNullOrWhiteSpace(city))
                throw new DomainException("City is required");
            if (string.IsNullOrWhiteSpace(state))
                throw new DomainException("State is required");
            if (string.IsNullOrWhiteSpace(postalCode))
                throw new DomainException("Postal code is required");
            if (string.IsNullOrWhiteSpace(country))
                throw new DomainException("Country is required");

            Street1 = street1.Trim();
            Street2 = street2?.Trim();
            City = city.Trim();
            State = state.Trim();
            PostalCode = postalCode.Trim();
            Country = country.Trim();
            Latitude = latitude;
            Longitude = longitude;
        }

        public Address WithCoordinates(decimal latitude, decimal longitude)
        {
            return new Address(Street1, City, State, PostalCode, Country, Street2, latitude, longitude);
        }

        public string GetFullAddress()
        {
            var parts = new List<string> { Street1 };
            if (!string.IsNullOrWhiteSpace(Street2))
                parts.Add(Street2);
            parts.Add($"{City}, {State} {PostalCode}");
            parts.Add(Country);
            return string.Join(", ", parts);
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Street1;
            yield return Street2 ?? string.Empty;
            yield return City;
            yield return State;
            yield return PostalCode;
            yield return Country;
        }

        public override string ToString()
        {
            return GetFullAddress();
        }
    }
}
