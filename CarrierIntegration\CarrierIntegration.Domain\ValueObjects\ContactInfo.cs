using CarrierIntegration.Domain.Common;
using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

namespace CarrierIntegration.Domain.ValueObjects
{
    public class ContactInfo : ValueObject
    {
        public string Name { get; private set; }
        public string? Email { get; private set; }
        public string? Phone { get; private set; }
        public string? Title { get; private set; }
        public string? Department { get; private set; }

        private ContactInfo() { } // For EF Core

        public ContactInfo(
            string name,
            string? email = null,
            string? phone = null,
            string? title = null,
            string? department = null)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new DomainException("Contact name is required");

            if (!string.IsNullOrWhiteSpace(email) && !IsValidEmail(email))
                throw new DomainException("Invalid email format");

            if (!string.IsNullOrWhiteSpace(phone) && !IsValidPhone(phone))
                throw new DomainException("Invalid phone format");

            Name = name.Trim();
            Email = email?.Trim();
            Phone = phone?.Trim();
            Title = title?.Trim();
            Department = department?.Trim();
        }

        private static bool IsValidEmail(string email)
        {
            var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", RegexOptions.IgnoreCase);
            return emailRegex.IsMatch(email);
        }

        private static bool IsValidPhone(string phone)
        {
            var phoneRegex = new Regex(@"^[\+]?[1-9][\d]{0,15}$");
            var cleanPhone = Regex.Replace(phone, @"[\s\-\(\)]", "");
            return phoneRegex.IsMatch(cleanPhone);
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Name;
            yield return Email ?? string.Empty;
            yield return Phone ?? string.Empty;
            yield return Title ?? string.Empty;
            yield return Department ?? string.Empty;
        }

        public override string ToString()
        {
            var parts = new List<string> { Name };
            if (!string.IsNullOrWhiteSpace(Title))
                parts.Add($"({Title})");
            if (!string.IsNullOrWhiteSpace(Email))
                parts.Add(Email);
            if (!string.IsNullOrWhiteSpace(Phone))
                parts.Add(Phone);
            return string.Join(" - ", parts);
        }
    }
}
