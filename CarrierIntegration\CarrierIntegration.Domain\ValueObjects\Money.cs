using CarrierIntegration.Domain.Common;
using CarrierIntegration.Domain.Enums;
using System;
using System.Collections.Generic;

namespace CarrierIntegration.Domain.ValueObjects
{
    public class Money : ValueObject
    {
        public decimal Amount { get; private set; }
        public Currency Currency { get; private set; }

        private Money() { } // For EF Core

        public Money(decimal amount, Currency currency = Currency.USD)
        {
            if (amount < 0)
                throw new DomainException("Amount cannot be negative");

            Amount = Math.Round(amount, 2);
            Currency = currency;
        }

        public static Money Zero(Currency currency = Currency.USD) => new(0, currency);

        public Money Add(Money other)
        {
            if (Currency != other.Currency)
                throw new DomainException($"Cannot add different currencies: {Currency} and {other.Currency}");

            return new Money(Amount + other.Amount, Currency);
        }

        public Money Subtract(Money other)
        {
            if (Currency != other.Currency)
                throw new DomainException($"Cannot subtract different currencies: {Currency} and {other.Currency}");

            return new Money(Amount - other.Amount, Currency);
        }

        public Money Multiply(decimal factor)
        {
            return new Money(Amount * factor, Currency);
        }

        public Money Divide(decimal divisor)
        {
            if (divisor == 0)
                throw new DomainException("Cannot divide by zero");

            return new Money(Amount / divisor, Currency);
        }

        public bool IsGreaterThan(Money other)
        {
            if (Currency != other.Currency)
                throw new DomainException($"Cannot compare different currencies: {Currency} and {other.Currency}");

            return Amount > other.Amount;
        }

        public bool IsLessThan(Money other)
        {
            if (Currency != other.Currency)
                throw new DomainException($"Cannot compare different currencies: {Currency} and {other.Currency}");

            return Amount < other.Amount;
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return Amount;
            yield return Currency;
        }

        public override string ToString()
        {
            return $"{Amount:C} {Currency}";
        }
    }
}
