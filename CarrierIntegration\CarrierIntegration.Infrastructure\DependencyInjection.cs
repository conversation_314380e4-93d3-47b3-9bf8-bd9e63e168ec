using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Infrastructure.Persistence;
using CarrierIntegration.Infrastructure.Persistence.Repositories;
using CarrierIntegration.Infrastructure.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;

namespace CarrierIntegration.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Add Entity Framework
            services.AddDbContext<CarrierIntegrationDbContext>(options =>
                options.UseNpgsql(
                    configuration.GetConnectionString("DefaultConnection"),
                    b => b.MigrationsAssembly(typeof(CarrierIntegrationDbContext).Assembly.FullName)));

            // Add Redis
            var redisConnectionString = configuration.GetConnectionString("Redis");
            if (!string.IsNullOrEmpty(redisConnectionString))
            {
                services.AddSingleton<IConnectionMultiplexer>(provider =>
                    ConnectionMultiplexer.Connect(redisConnectionString));

                services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = redisConnectionString;
                });
            }

            // Add repositories
            services.AddScoped<ICarrierRepository, CarrierRepository>();
            services.AddScoped<ICarrierBookingRepository, CarrierBookingRepository>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            // Add external services
            services.AddScoped<ICarrierApiService, CarrierApiService>();
            services.AddScoped<IIdentityIntegrationService, IdentityIntegrationService>();
            services.AddScoped<IUserManagementIntegrationService, UserManagementIntegrationService>();
            services.AddScoped<IMasterManagementIntegrationService, MasterManagementIntegrationService>();
            services.AddScoped<IOrderManagementIntegrationService, OrderManagementIntegrationService>();
            services.AddScoped<IShipmentManagementIntegrationService, ShipmentManagementIntegrationService>();
            services.AddScoped<IPricingManagementIntegrationService, PricingManagementIntegrationService>();

            // Add carrier-specific API services
            services.AddScoped<IUpsApiService, UpsApiService>();
            services.AddScoped<IFedExApiService, FedExApiService>();
            services.AddScoped<IUspsApiService, UspsApiService>();
            services.AddScoped<IDhlApiService, DhlApiService>();

            // Add background services
            services.AddHostedService<CarrierHealthCheckService>();
            services.AddHostedService<BookingRetryService>();
            services.AddHostedService<CarrierPerformanceMonitoringService>();

            // Add caching service
            services.AddScoped<ICachingService, CachingService>();

            // Add event publishing service
            services.AddScoped<IEventPublishingService, EventPublishingService>();

            // Add analytics services
            services.AddScoped<ICarrierAnalyticsService, Analytics.CarrierAnalyticsService>();
            services.AddScoped<ICarrierDashboardService, Analytics.CarrierDashboardService>();
            services.AddScoped<IPredictiveAnalyticsService, Analytics.PredictiveAnalyticsService>();

            // Add optimization services
            services.AddScoped<ICarrierOptimizationEngine, Optimization.CarrierOptimizationEngine>();
            services.AddScoped<IMachineLearningService, MachineLearningService>();

            // Add carrier-specific API services
            services.AddScoped<ICarrierSpecificApiService, Carriers.UpsApiService>();

            // Add data seeding service
            services.AddScoped<IDataSeedingService, DataSeedingService>();

            return services;
        }
    }
}
