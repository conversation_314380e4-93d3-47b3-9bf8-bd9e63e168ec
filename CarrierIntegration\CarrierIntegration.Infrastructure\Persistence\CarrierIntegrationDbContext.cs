using CarrierIntegration.Domain.Common;
using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Infrastructure.Persistence.Configurations;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence
{
    public class CarrierIntegrationDbContext : DbContext
    {
        public CarrierIntegrationDbContext(DbContextOptions<CarrierIntegrationDbContext> options) : base(options)
        {
        }

        public DbSet<Carrier> Carriers => Set<Carrier>();
        public DbSet<CarrierService> CarrierServices => Set<CarrierService>();
        public DbSet<CarrierAccount> CarrierAccounts => Set<CarrierAccount>();
        public DbSet<CarrierBooking> CarrierBookings => Set<CarrierBooking>();
        public DbSet<CarrierPerformanceMetric> CarrierPerformanceMetrics => Set<CarrierPerformanceMetric>();
        public DbSet<CarrierCompliance> CarrierCompliances => Set<CarrierCompliance>();
        public DbSet<CarrierContract> CarrierContracts => Set<CarrierContract>();
        public DbSet<CarrierRelationshipScore> CarrierRelationshipScores => Set<CarrierRelationshipScore>();

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // Apply all configurations from the current assembly
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // Configure global query filters for soft delete
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                if (typeof(BaseEntity).IsAssignableFrom(entityType.ClrType))
                {
                    var method = typeof(CarrierIntegrationDbContext)
                        .GetMethod(nameof(SetGlobalQueryFilter), BindingFlags.NonPublic | BindingFlags.Static)?
                        .MakeGenericMethod(entityType.ClrType);
                    method?.Invoke(null, new object[] { modelBuilder });
                }
            }

            base.OnModelCreating(modelBuilder);
        }

        private static void SetGlobalQueryFilter<T>(ModelBuilder modelBuilder) where T : BaseEntity
        {
            modelBuilder.Entity<T>().HasQueryFilter(e => !e.IsDeleted);
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            // Update audit fields before saving
            foreach (var entry in ChangeTracker.Entries<BaseEntity>())
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.SetCreatedBy("System"); // This should be set from the current user context
                        break;
                    case EntityState.Modified:
                        entry.Entity.Update("System"); // This should be set from the current user context
                        break;
                }
            }

            var result = await base.SaveChangesAsync(cancellationToken);

            // Dispatch domain events after saving
            await DispatchDomainEventsAsync();

            return result;
        }

        private async Task DispatchDomainEventsAsync()
        {
            var domainEntities = ChangeTracker
                .Entries<BaseEntity>()
                .Where(x => x.Entity.DomainEvents.Any())
                .ToList();

            var domainEvents = domainEntities
                .SelectMany(x => x.Entity.DomainEvents)
                .ToList();

            domainEntities.ForEach(entity => entity.Entity.ClearDomainEvents());

            // Here you would typically dispatch events to a message broker
            // For now, we'll just clear them
            foreach (var domainEvent in domainEvents)
            {
                // TODO: Publish domain event to message broker
                await Task.CompletedTask;
            }
        }
    }
}
