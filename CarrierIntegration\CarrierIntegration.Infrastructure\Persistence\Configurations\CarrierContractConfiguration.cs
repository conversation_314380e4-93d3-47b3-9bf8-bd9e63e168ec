using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace CarrierIntegration.Infrastructure.Persistence.Configurations
{
    public class CarrierContractConfiguration : IEntityTypeConfiguration<CarrierContract>
    {
        public void Configure(EntityTypeBuilder<CarrierContract> builder)
        {
            builder.ToTable("CarrierContracts");

            builder.<PERSON><PERSON>ey(cc => cc.Id);

            builder.Property(cc => cc.Id)
                .ValueGeneratedOnAdd();

            builder.Property(cc => cc.ContractNumber)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(cc => cc.ContractName)
                .IsRequired()
                .HasMaxLength(200);

            builder.Property(cc => cc.ContractType)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(cc => cc.Status)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(cc => cc.PaymentTerms)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cc => cc.Currency)
                .IsRequired()
                .HasMaxLength(3)
                .HasDefaultValue("USD");

            builder.Property(cc => cc.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(cc => cc.UpdatedBy)
                .HasMaxLength(100);

            // Indexes
            builder.HasIndex(cc => new { cc.OrganizationId, cc.ContractNumber })
                .IsUnique()
                .HasDatabaseName("IX_CarrierContracts_OrganizationId_ContractNumber");

            builder.HasIndex(cc => cc.CarrierId)
                .HasDatabaseName("IX_CarrierContracts_CarrierId");

            builder.HasIndex(cc => cc.Status)
                .HasDatabaseName("IX_CarrierContracts_Status");

            builder.HasIndex(cc => cc.ExpirationDate)
                .HasDatabaseName("IX_CarrierContracts_ExpirationDate");

            // Relationships
            builder.HasOne(cc => cc.Carrier)
                .WithMany(c => c.Contracts)
                .HasForeignKey(cc => cc.CarrierId)
                .OnDelete(DeleteBehavior.Cascade);

            // Value object configurations
            builder.OwnsOne(cc => cc.MinimumCommitment, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("MinimumCommitmentAmount")
                    .HasColumnType("decimal(18,2)");
                money.Property(m => m.Currency)
                    .HasColumnName("MinimumCommitmentCurrency")
                    .HasMaxLength(3);
            });

            builder.OwnsOne(cc => cc.MaximumLiability, money =>
            {
                money.Property(m => m.Amount)
                    .HasColumnName("MaximumLiabilityAmount")
                    .HasColumnType("decimal(18,2)");
                money.Property(m => m.Currency)
                    .HasColumnName("MaximumLiabilityCurrency")
                    .HasMaxLength(3);
            });

            // Decimal properties
            builder.Property(cc => cc.VolumeDiscountThreshold)
                .HasColumnType("decimal(18,2)");

            builder.Property(cc => cc.VolumeDiscountRate)
                .HasColumnType("decimal(5,4)");

            builder.Property(cc => cc.OnTimeDeliveryTarget)
                .HasColumnType("decimal(5,2)");

            builder.Property(cc => cc.OnTimeDeliveryPenalty)
                .HasColumnType("decimal(18,2)");

            builder.Property(cc => cc.DamageClaimLimit)
                .HasColumnType("decimal(18,2)");

            builder.Property(cc => cc.ServiceLevelCredit)
                .HasColumnType("decimal(18,2)");

            builder.Property(cc => cc.CurrentVolumeCommitment)
                .HasColumnType("decimal(18,2)");

            builder.Property(cc => cc.ActualVolume)
                .HasColumnType("decimal(18,2)");

            builder.Property(cc => cc.CommitmentPercentage)
                .HasColumnType("decimal(5,2)");

            builder.Property(cc => cc.CurrentSpend)
                .HasColumnType("decimal(18,2)");

            builder.Property(cc => cc.AveragePerformanceScore)
                .HasColumnType("decimal(5,2)");

            // Text properties
            builder.Property(cc => cc.RenewalTerms)
                .HasMaxLength(1000);

            builder.Property(cc => cc.InvoicingFrequency)
                .HasMaxLength(50);

            builder.Property(cc => cc.BillingMethod)
                .HasMaxLength(50);

            builder.Property(cc => cc.PerformanceIncentives)
                .HasMaxLength(1000);

            builder.Property(cc => cc.QualityStandards)
                .HasMaxLength(1000);

            builder.Property(cc => cc.GeographicCoverage)
                .HasMaxLength(1000);

            builder.Property(cc => cc.ServiceTypes)
                .HasMaxLength(1000);

            builder.Property(cc => cc.ExcludedServices)
                .HasMaxLength(1000);

            builder.Property(cc => cc.WeightLimitations)
                .HasMaxLength(500);

            builder.Property(cc => cc.DimensionLimitations)
                .HasMaxLength(500);

            builder.Property(cc => cc.CommodityRestrictions)
                .HasMaxLength(1000);

            builder.Property(cc => cc.SpecialHandlingTerms)
                .HasMaxLength(1000);

            builder.Property(cc => cc.LiabilityTerms)
                .HasMaxLength(2000);

            builder.Property(cc => cc.InsuranceRequirements)
                .HasMaxLength(1000);

            builder.Property(cc => cc.ComplianceRequirements)
                .HasMaxLength(1000);

            builder.Property(cc => cc.DisputeResolution)
                .HasMaxLength(1000);

            builder.Property(cc => cc.GoverningLaw)
                .HasMaxLength(200);

            builder.Property(cc => cc.ForceMateure)
                .HasMaxLength(1000);

            builder.Property(cc => cc.ContractOwner)
                .HasMaxLength(100);

            builder.Property(cc => cc.CarrierRepresentative)
                .HasMaxLength(100);

            builder.Property(cc => cc.LegalReviewStatus)
                .HasMaxLength(50);

            builder.Property(cc => cc.ReviewNotes)
                .HasMaxLength(2000);

            builder.Property(cc => cc.Amendments)
                .HasMaxLength(2000);

            builder.Property(cc => cc.AttachedDocuments)
                .HasMaxLength(1000);

            builder.Property(cc => cc.Notes)
                .HasMaxLength(2000);

            builder.Property(cc => cc.TerminationReason)
                .HasMaxLength(500);
        }
    }
}
