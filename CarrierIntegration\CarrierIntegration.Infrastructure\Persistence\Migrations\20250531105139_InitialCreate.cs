﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CarrierIntegration.Infrastructure.Persistence.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CarrierBookings",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CarrierId = table.Column<Guid>(type: "uuid", nullable: false),
                    CarrierAccountId = table.Column<Guid>(type: "uuid", nullable: true),
                    CarrierServiceId = table.Column<Guid>(type: "uuid", nullable: true),
                    OrderId = table.Column<Guid>(type: "uuid", nullable: true),
                    ShipmentId = table.Column<Guid>(type: "uuid", nullable: true),
                    BookingReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CarrierBookingReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    StatusMessage = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    BookingDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ConfirmationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RequestedPickupDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    RequestedDeliveryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ConfirmedPickupDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ConfirmedDeliveryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    PickupTimeWindow = table.Column<TimeSpan>(type: "interval", nullable: true),
                    DeliveryTimeWindow = table.Column<TimeSpan>(type: "interval", nullable: true),
                    PickupAddressStreet1 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PickupAddressStreet2 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    PickupAddressCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PickupAddressState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PickupAddressPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    PickupAddressCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    PickupAddressLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    PickupAddressLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    DeliveryAddressStreet1 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    DeliveryAddressStreet2 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    DeliveryAddressCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DeliveryAddressState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DeliveryAddressPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    DeliveryAddressCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    DeliveryAddressLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    DeliveryAddressLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    PickupContactName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PickupContactEmail = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    PickupContactPhone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    PickupContactTitle = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PickupContactDepartment = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    DeliveryContactName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    DeliveryContactEmail = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    DeliveryContactPhone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    DeliveryContactTitle = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    DeliveryContactDepartment = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PickupInstructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DeliveryInstructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    TotalWeightValue = table.Column<decimal>(type: "numeric(18,3)", precision: 18, scale: 3, nullable: false),
                    TotalWeightUnit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    TotalDimensionsLength = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    TotalDimensionsWidth = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    TotalDimensionsHeight = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    TotalDimensionsUnit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    PackageCount = table.Column<int>(type: "integer", nullable: false),
                    PackageDescription = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DeclaredValueAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    DeclaredValueCurrency = table.Column<int>(type: "integer", nullable: true),
                    CommodityCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    HazmatClass = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    RequiresSignature = table.Column<bool>(type: "boolean", nullable: false),
                    RequiresInsurance = table.Column<bool>(type: "boolean", nullable: false),
                    IsCOD = table.Column<bool>(type: "boolean", nullable: false),
                    CODAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    CODCurrency = table.Column<int>(type: "integer", nullable: true),
                    CODPaymentMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IsResidentialPickup = table.Column<bool>(type: "boolean", nullable: false),
                    IsResidentialDelivery = table.Column<bool>(type: "boolean", nullable: false),
                    RequiresAppointment = table.Column<bool>(type: "boolean", nullable: false),
                    RequiresLiftgate = table.Column<bool>(type: "boolean", nullable: false),
                    RequiresInsideDelivery = table.Column<bool>(type: "boolean", nullable: false),
                    RequiresWhiteGlove = table.Column<bool>(type: "boolean", nullable: false),
                    SpecialServices = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    AccessorialServices = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    EstimatedCostAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    EstimatedCostCurrency = table.Column<int>(type: "integer", nullable: true),
                    ActualCostAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    ActualCostCurrency = table.Column<int>(type: "integer", nullable: true),
                    CostBreakdown = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    CarrierServiceCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CarrierServiceName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    EstimatedTransitDays = table.Column<int>(type: "integer", nullable: true),
                    TrackingNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LabelUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DocumentsUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ManifestNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BillOfLadingNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ProNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    EquipmentType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    EquipmentNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    DriverName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    DriverPhone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    VehicleInfo = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CarrierNotes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    InternalNotes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    CustomerReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ShipperReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PoNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    InvoiceNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CancelledDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CancellationReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CancelledBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CompletedDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CompletionNotes = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ApiRequestData = table.Column<string>(type: "text", nullable: true),
                    ApiResponseData = table.Column<string>(type: "text", nullable: true),
                    LastApiCall = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApiCallCount = table.Column<int>(type: "integer", nullable: false),
                    LastError = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    LastErrorDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RetryCount = table.Column<int>(type: "integer", nullable: false),
                    NextRetryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CarrierBookings", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Carriers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Website = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    PrimaryContactName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    PrimaryContactEmail = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    PrimaryContactPhone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    PrimaryContactTitle = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PrimaryContactDepartment = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    HeadquartersStreet1 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    HeadquartersStreet2 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    HeadquartersCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    HeadquartersState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    HeadquartersPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    HeadquartersCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    HeadquartersLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    HeadquartersLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    ApiEndpoint = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ApiVersion = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    LastHealthCheck = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    HealthCheckPassed = table.Column<bool>(type: "boolean", nullable: false),
                    HealthCheckMessage = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    MaxWeight = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxWeightUnit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    MaxDimensions = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxDimensionsUnit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    SupportsTracking = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsRating = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsLabeling = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsPickup = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsDeliveryConfirmation = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsInternational = table.Column<bool>(type: "boolean", nullable: false),
                    RequiresAccount = table.Column<bool>(type: "boolean", nullable: false),
                    TimeZone = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OperatingHours = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    SupportContact = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    BillingContact = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ContractStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ContractEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ContractTerms = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    BaseRate = table.Column<decimal>(type: "numeric", nullable: true),
                    BaseCurrency = table.Column<int>(type: "integer", nullable: true),
                    InsuranceProvider = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    InsurancePolicyNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    InsuranceExpiryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LicenseNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LicenseExpiryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DotNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    McNumber = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ScacCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    IataCode = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    CustomsCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Carriers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CarrierAccounts",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CarrierId = table.Column<Guid>(type: "uuid", nullable: false),
                    AccountNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    AccountName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    IsDefault = table.Column<bool>(type: "boolean", nullable: false),
                    Username = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PasswordHash = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ApiKey = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ApiSecret = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AccessToken = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RefreshToken = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    TokenExpiryDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AuthenticationMethod = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    AuthenticationUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    BillingContactName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    BillingContactEmail = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    BillingContactPhone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    BillingContactTitle = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BillingContactDepartment = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    BillingAddressStreet1 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    BillingAddressStreet2 = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    BillingAddressCity = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    BillingAddressState = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    BillingAddressPostalCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    BillingAddressCountry = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    BillingAddressLatitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    BillingAddressLongitude = table.Column<decimal>(type: "numeric(10,7)", precision: 10, scale: 7, nullable: true),
                    TechnicalContactName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    TechnicalContactEmail = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    TechnicalContactPhone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    TechnicalContactTitle = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    TechnicalContactDepartment = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CustomerNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    MeterNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    LicenseKey = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DeveloperKey = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Environment = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    BaseUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    TestBaseUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    IsTestMode = table.Column<bool>(type: "boolean", nullable: false),
                    CreditLimit = table.Column<decimal>(type: "numeric", nullable: true),
                    CreditCurrency = table.Column<int>(type: "integer", nullable: true),
                    CurrentBalance = table.Column<decimal>(type: "numeric", nullable: true),
                    PaymentTerms = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    BillingCycle = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    LastBillingDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    NextBillingDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    AutoPay = table.Column<bool>(type: "boolean", nullable: false),
                    PaymentMethod = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ContractNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ContractStartDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ContractEndDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ServiceLevel = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    DiscountCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    DiscountPercentage = table.Column<decimal>(type: "numeric", nullable: true),
                    SpecialRates = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    RestrictedServices = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    AllowedServices = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    MaxShipmentsPerDay = table.Column<int>(type: "integer", nullable: true),
                    MaxShipmentsPerMonth = table.Column<int>(type: "integer", nullable: true),
                    MaxShipmentValue = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxShipmentValueCurrency = table.Column<int>(type: "integer", nullable: true),
                    TimeZone = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    PreferredLanguage = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    NotificationPreferences = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ReportingPreferences = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    LastLoginDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastApiCall = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ApiCallsToday = table.Column<int>(type: "integer", nullable: false),
                    ApiCallsThisMonth = table.Column<int>(type: "integer", nullable: false),
                    MaxApiCallsPerDay = table.Column<int>(type: "integer", nullable: true),
                    MaxApiCallsPerMonth = table.Column<int>(type: "integer", nullable: true),
                    Notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    InternalReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ExternalReference = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Tags = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ActivationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeactivationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeactivationReason = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CarrierAccounts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CarrierAccounts_Carriers_CarrierId",
                        column: x => x.CarrierId,
                        principalTable: "Carriers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CarrierCompliances",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CarrierId = table.Column<Guid>(type: "uuid", nullable: false),
                    ComplianceType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    ComplianceName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    ComplianceNumber = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IssuingAuthority = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    IssueDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    DocumentUrl = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    DocumentPath = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    Notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    IsRequired = table.Column<bool>(type: "boolean", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    LastVerificationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastVerificationBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    VerificationMethod = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    NextVerificationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    RenewalProcess = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ContactPerson = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ContactEmail = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    ContactPhone = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CoverageAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    CoverageCurrency = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    PolicyDetails = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Restrictions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    GeographicScope = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    ServiceScope = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CarrierCompliances", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CarrierCompliances_Carriers_CarrierId",
                        column: x => x.CarrierId,
                        principalTable: "Carriers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CarrierPerformanceMetrics",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CarrierId = table.Column<Guid>(type: "uuid", nullable: false),
                    PeriodStart = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    PeriodEnd = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    MetricType = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Value = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    Unit = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    TotalShipments = table.Column<int>(type: "integer", nullable: false),
                    SuccessfulShipments = table.Column<int>(type: "integer", nullable: false),
                    FailedShipments = table.Column<int>(type: "integer", nullable: false),
                    ExceptionShipments = table.Column<int>(type: "integer", nullable: false),
                    AverageTransitTime = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: false),
                    OnTimeDeliveryRate = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: false),
                    ExceptionRate = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: false),
                    DamageClaimRate = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: false),
                    CostPerformanceIndex = table.Column<decimal>(type: "numeric(18,4)", precision: 18, scale: 4, nullable: false),
                    CustomerSatisfactionScore = table.Column<decimal>(type: "numeric(3,1)", precision: 3, scale: 1, nullable: false),
                    AverageCostAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    AverageCostCurrency = table.Column<int>(type: "integer", nullable: true),
                    TotalRevenueAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    TotalRevenueCurrency = table.Column<int>(type: "integer", nullable: true),
                    Notes = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    CalculationMethod = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    CalculatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    CalculatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CarrierPerformanceMetrics", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CarrierPerformanceMetrics_Carriers_CarrierId",
                        column: x => x.CarrierId,
                        principalTable: "Carriers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CarrierServices",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CarrierId = table.Column<Guid>(type: "uuid", nullable: false),
                    ServiceCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    ServiceName = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ServiceLevel = table.Column<int>(type: "integer", nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    TransitDays = table.Column<int>(type: "integer", nullable: false),
                    TransitDaysMax = table.Column<int>(type: "integer", nullable: true),
                    IsGuaranteed = table.Column<bool>(type: "boolean", nullable: false),
                    CutoffTime = table.Column<TimeSpan>(type: "interval", nullable: true),
                    RequiresSignature = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsInsurance = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsCOD = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsHazmat = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsInternational = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsResidential = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsCommercial = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsPickup = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsDropoff = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsSaturdayDelivery = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsSundayDelivery = table.Column<bool>(type: "boolean", nullable: false),
                    SupportsHolidayDelivery = table.Column<bool>(type: "boolean", nullable: false),
                    MinWeight = table.Column<decimal>(type: "numeric", nullable: true),
                    MinWeightUnit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    MaxWeight = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxWeightUnit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    MaxLength = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxWidth = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxHeight = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxDimensionsUnit = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: true),
                    MaxDeclaredValue = table.Column<decimal>(type: "numeric", nullable: true),
                    MaxDeclaredValueCurrency = table.Column<int>(type: "integer", nullable: true),
                    ServiceArea = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    RestrictedAreas = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    SpecialInstructions = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    BaseRateAmount = table.Column<decimal>(type: "numeric(18,2)", precision: 18, scale: 2, nullable: true),
                    BaseRateCurrency = table.Column<int>(type: "integer", nullable: true),
                    RateStructure = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    FuelSurchargeStructure = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    AccessorialCharges = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    ApiServiceCode = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    TrackingUrlTemplate = table.Column<string>(type: "character varying(500)", maxLength: 500, nullable: true),
                    LabelFormat = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    RequiresAccount = table.Column<bool>(type: "boolean", nullable: false),
                    AccountRequirements = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    ComplianceRequirements = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    DocumentationRequirements = table.Column<string>(type: "character varying(1000)", maxLength: 1000, nullable: true),
                    EffectiveDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Terms = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    Conditions = table.Column<string>(type: "character varying(2000)", maxLength: 2000, nullable: true),
                    SupportedCountries = table.Column<string>(type: "text", nullable: false),
                    RestrictedCountries = table.Column<string>(type: "text", nullable: false),
                    SupportedPostalCodes = table.Column<string>(type: "text", nullable: false),
                    RestrictedPostalCodes = table.Column<string>(type: "text", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    UpdatedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    OrganizationId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CarrierServices", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CarrierServices_Carriers_CarrierId",
                        column: x => x.CarrierId,
                        principalTable: "Carriers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_CarrierAccounts_CarrierId_AccountNumber",
                table: "CarrierAccounts",
                columns: new[] { "CarrierId", "AccountNumber" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CarrierAccounts_CreatedAt",
                table: "CarrierAccounts",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierAccounts_IsActive",
                table: "CarrierAccounts",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierAccounts_IsDefault",
                table: "CarrierAccounts",
                column: "IsDefault");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierAccounts_OrganizationId",
                table: "CarrierAccounts",
                column: "OrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierBookings_BookingDate",
                table: "CarrierBookings",
                column: "BookingDate");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierBookings_CarrierId",
                table: "CarrierBookings",
                column: "CarrierId");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierBookings_CreatedAt",
                table: "CarrierBookings",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierBookings_OrderId",
                table: "CarrierBookings",
                column: "OrderId");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierBookings_OrganizationId_BookingReference",
                table: "CarrierBookings",
                columns: new[] { "OrganizationId", "BookingReference" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CarrierBookings_ShipmentId",
                table: "CarrierBookings",
                column: "ShipmentId");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierBookings_Status",
                table: "CarrierBookings",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierBookings_TrackingNumber",
                table: "CarrierBookings",
                column: "TrackingNumber");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierCompliances_Carrier_Type_Name",
                table: "CarrierCompliances",
                columns: new[] { "CarrierId", "ComplianceType", "ComplianceName" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CarrierCompliances_ComplianceType",
                table: "CarrierCompliances",
                column: "ComplianceType");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierCompliances_CreatedAt",
                table: "CarrierCompliances",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierCompliances_ExpirationDate",
                table: "CarrierCompliances",
                column: "ExpirationDate");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierCompliances_IsActive",
                table: "CarrierCompliances",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierCompliances_IsRequired",
                table: "CarrierCompliances",
                column: "IsRequired");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierCompliances_NextVerificationDate",
                table: "CarrierCompliances",
                column: "NextVerificationDate");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierCompliances_OrganizationId",
                table: "CarrierCompliances",
                column: "OrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierCompliances_Status",
                table: "CarrierCompliances",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierPerformanceMetrics_CalculatedAt",
                table: "CarrierPerformanceMetrics",
                column: "CalculatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierPerformanceMetrics_Carrier_Period_Type",
                table: "CarrierPerformanceMetrics",
                columns: new[] { "CarrierId", "PeriodStart", "PeriodEnd", "MetricType" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CarrierPerformanceMetrics_CreatedAt",
                table: "CarrierPerformanceMetrics",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierPerformanceMetrics_MetricType",
                table: "CarrierPerformanceMetrics",
                column: "MetricType");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierPerformanceMetrics_OrganizationId",
                table: "CarrierPerformanceMetrics",
                column: "OrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierPerformanceMetrics_PeriodStart",
                table: "CarrierPerformanceMetrics",
                column: "PeriodStart");

            migrationBuilder.CreateIndex(
                name: "IX_Carriers_CreatedAt",
                table: "Carriers",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Carriers_IsActive",
                table: "Carriers",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_Carriers_OrganizationId",
                table: "Carriers",
                column: "OrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_Carriers_OrganizationId_Code",
                table: "Carriers",
                columns: new[] { "OrganizationId", "Code" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Carriers_Status",
                table: "Carriers",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_Carriers_Type",
                table: "Carriers",
                column: "Type");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierServices_CarrierId_ServiceCode",
                table: "CarrierServices",
                columns: new[] { "CarrierId", "ServiceCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CarrierServices_CreatedAt",
                table: "CarrierServices",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierServices_IsActive",
                table: "CarrierServices",
                column: "IsActive");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierServices_OrganizationId",
                table: "CarrierServices",
                column: "OrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_CarrierServices_ServiceLevel",
                table: "CarrierServices",
                column: "ServiceLevel");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CarrierAccounts");

            migrationBuilder.DropTable(
                name: "CarrierBookings");

            migrationBuilder.DropTable(
                name: "CarrierCompliances");

            migrationBuilder.DropTable(
                name: "CarrierPerformanceMetrics");

            migrationBuilder.DropTable(
                name: "CarrierServices");

            migrationBuilder.DropTable(
                name: "Carriers");
        }
    }
}
