﻿// <auto-generated />
using System;
using CarrierIntegration.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace CarrierIntegration.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(CarrierIntegrationDbContext))]
    partial class CarrierIntegrationDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.Carrier", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("ApiEndpoint")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ApiVersion")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int?>("BaseCurrency")
                        .HasColumnType("integer");

                    b.Property<decimal?>("BaseRate")
                        .HasColumnType("numeric");

                    b.Property<string>("BillingContact")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("ContractEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ContractStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ContractTerms")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomsCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("DotNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("HealthCheckMessage")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<bool>("HealthCheckPassed")
                        .HasColumnType("boolean");

                    b.Property<string>("IataCode")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime?>("InsuranceExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("InsurancePolicyNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("InsuranceProvider")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastHealthCheck")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LicenseExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LicenseNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("MaxDimensions")
                        .HasColumnType("numeric");

                    b.Property<string>("MaxDimensionsUnit")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<string>("MaxWeightUnit")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("McNumber")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("OperatingHours")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<bool>("RequiresAccount")
                        .HasColumnType("boolean");

                    b.Property<string>("ScacCode")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("SupportContact")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("SupportsDeliveryConfirmation")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsInternational")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsLabeling")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsPickup")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsRating")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsTracking")
                        .HasColumnType("boolean");

                    b.Property<string>("TimeZone")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Website")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_Carriers_CreatedAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_Carriers_IsActive");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("IX_Carriers_OrganizationId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_Carriers_Status");

                    b.HasIndex("Type")
                        .HasDatabaseName("IX_Carriers_Type");

                    b.HasIndex("OrganizationId", "Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Carriers_OrganizationId_Code");

                    b.ToTable("Carriers", (string)null);
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierAccount", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("AccessToken")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("AccountNumber")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ActivationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("AllowedServices")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("ApiCallsThisMonth")
                        .HasColumnType("integer");

                    b.Property<int>("ApiCallsToday")
                        .HasColumnType("integer");

                    b.Property<string>("ApiKey")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ApiSecret")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("AuthenticationMethod")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("AuthenticationUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("AutoPay")
                        .HasColumnType("boolean");

                    b.Property<string>("BaseUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("BillingCycle")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("CarrierId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("ContractEndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ContractNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("ContractStartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("CreditCurrency")
                        .HasColumnType("integer");

                    b.Property<decimal?>("CreditLimit")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("CurrentBalance")
                        .HasColumnType("numeric");

                    b.Property<string>("CustomerNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DeactivationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeactivationReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("DeveloperKey")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DiscountCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("DiscountPercentage")
                        .HasColumnType("numeric");

                    b.Property<string>("Environment")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ExternalReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("InternalReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsTestMode")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastApiCall")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastBillingDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("LastLoginDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LicenseKey")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int?>("MaxApiCallsPerDay")
                        .HasColumnType("integer");

                    b.Property<int?>("MaxApiCallsPerMonth")
                        .HasColumnType("integer");

                    b.Property<decimal?>("MaxShipmentValue")
                        .HasColumnType("numeric");

                    b.Property<int?>("MaxShipmentValueCurrency")
                        .HasColumnType("integer");

                    b.Property<int?>("MaxShipmentsPerDay")
                        .HasColumnType("integer");

                    b.Property<int?>("MaxShipmentsPerMonth")
                        .HasColumnType("integer");

                    b.Property<string>("MeterNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("NextBillingDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("NotificationPreferences")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("PaymentMethod")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("PaymentTerms")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("PreferredLanguage")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("RefreshToken")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ReportingPreferences")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("RestrictedServices")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ServiceLevel")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SpecialRates")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Tags")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("TestBaseUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("TimeZone")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("TokenExpiryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Username")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CarrierAccounts_CreatedAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_CarrierAccounts_IsActive");

                    b.HasIndex("IsDefault")
                        .HasDatabaseName("IX_CarrierAccounts_IsDefault");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("IX_CarrierAccounts_OrganizationId");

                    b.HasIndex("CarrierId", "AccountNumber")
                        .IsUnique()
                        .HasDatabaseName("IX_CarrierAccounts_CarrierId_AccountNumber");

                    b.ToTable("CarrierAccounts", (string)null);
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierBooking", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("AccessorialServices")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("ApiCallCount")
                        .HasColumnType("integer");

                    b.Property<string>("ApiRequestData")
                        .HasColumnType("text");

                    b.Property<string>("ApiResponseData")
                        .HasColumnType("text");

                    b.Property<string>("BillOfLadingNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime>("BookingDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("BookingReference")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CODPaymentMethod")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("CancellationReason")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("CancelledBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("CancelledDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CarrierAccountId")
                        .HasColumnType("uuid");

                    b.Property<string>("CarrierBookingReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid>("CarrierId")
                        .HasColumnType("uuid");

                    b.Property<string>("CarrierNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("CarrierServiceCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid?>("CarrierServiceId")
                        .HasColumnType("uuid");

                    b.Property<string>("CarrierServiceName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("CommodityCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("CompletedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CompletionNotes")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("ConfirmationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ConfirmedDeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ConfirmedPickupDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CostBreakdown")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CustomerReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DeliveryInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<TimeSpan?>("DeliveryTimeWindow")
                        .HasColumnType("interval");

                    b.Property<string>("DocumentsUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DriverName")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("DriverPhone")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("EquipmentNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("EquipmentType")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<int?>("EstimatedTransitDays")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("ExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("HazmatClass")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("InternalNotes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("InvoiceNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<bool>("IsCOD")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsResidentialDelivery")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsResidentialPickup")
                        .HasColumnType("boolean");

                    b.Property<string>("LabelUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime?>("LastApiCall")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastError")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime?>("LastErrorDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ManifestNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("NextRetryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("OrderId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<int>("PackageCount")
                        .HasColumnType("integer");

                    b.Property<string>("PackageDescription")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("PickupInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<TimeSpan?>("PickupTimeWindow")
                        .HasColumnType("interval");

                    b.Property<string>("PoNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ProNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("RequestedDeliveryDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("RequestedPickupDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("RequiresAppointment")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresInsideDelivery")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresInsurance")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresLiftgate")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresSignature")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresWhiteGlove")
                        .HasColumnType("boolean");

                    b.Property<int>("RetryCount")
                        .HasColumnType("integer");

                    b.Property<Guid?>("ShipmentId")
                        .HasColumnType("uuid");

                    b.Property<string>("ShipperReference")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("SpecialServices")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("StatusMessage")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("TrackingNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("VehicleInfo")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.HasKey("Id");

                    b.HasIndex("BookingDate")
                        .HasDatabaseName("IX_CarrierBookings_BookingDate");

                    b.HasIndex("CarrierId")
                        .HasDatabaseName("IX_CarrierBookings_CarrierId");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CarrierBookings_CreatedAt");

                    b.HasIndex("OrderId")
                        .HasDatabaseName("IX_CarrierBookings_OrderId");

                    b.HasIndex("ShipmentId")
                        .HasDatabaseName("IX_CarrierBookings_ShipmentId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_CarrierBookings_Status");

                    b.HasIndex("TrackingNumber")
                        .HasDatabaseName("IX_CarrierBookings_TrackingNumber");

                    b.HasIndex("OrganizationId", "BookingReference")
                        .IsUnique()
                        .HasDatabaseName("IX_CarrierBookings_OrganizationId_BookingReference");

                    b.ToTable("CarrierBookings", (string)null);
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierCompliance", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<Guid>("CarrierId")
                        .HasColumnType("uuid");

                    b.Property<string>("ComplianceName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ComplianceNumber")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ComplianceType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ContactEmail")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ContactPerson")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("ContactPhone")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("CoverageAmount")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<string>("CoverageCurrency")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("DocumentPath")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("DocumentUrl")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<DateTime>("ExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("GeographicScope")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsRequired")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("IssueDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("IssuingAuthority")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("LastVerificationBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastVerificationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("NextVerificationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("PolicyDetails")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("RenewalProcess")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Restrictions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ServiceScope")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("Status")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("VerificationMethod")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.HasKey("Id");

                    b.HasIndex("ComplianceType")
                        .HasDatabaseName("IX_CarrierCompliances_ComplianceType");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CarrierCompliances_CreatedAt");

                    b.HasIndex("ExpirationDate")
                        .HasDatabaseName("IX_CarrierCompliances_ExpirationDate");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_CarrierCompliances_IsActive");

                    b.HasIndex("IsRequired")
                        .HasDatabaseName("IX_CarrierCompliances_IsRequired");

                    b.HasIndex("NextVerificationDate")
                        .HasDatabaseName("IX_CarrierCompliances_NextVerificationDate");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("IX_CarrierCompliances_OrganizationId");

                    b.HasIndex("Status")
                        .HasDatabaseName("IX_CarrierCompliances_Status");

                    b.HasIndex("CarrierId", "ComplianceType", "ComplianceName")
                        .IsUnique()
                        .HasDatabaseName("IX_CarrierCompliances_Carrier_Type_Name");

                    b.ToTable("CarrierCompliances", (string)null);
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierPerformanceMetric", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<decimal>("AverageTransitTime")
                        .HasPrecision(18, 2)
                        .HasColumnType("numeric(18,2)");

                    b.Property<DateTime>("CalculatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CalculatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("CalculationMethod")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<Guid>("CarrierId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("CostPerformanceIndex")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("CustomerSatisfactionScore")
                        .HasPrecision(3, 1)
                        .HasColumnType("numeric(3,1)");

                    b.Property<decimal>("DamageClaimRate")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("ExceptionRate")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<int>("ExceptionShipments")
                        .HasColumnType("integer");

                    b.Property<int>("FailedShipments")
                        .HasColumnType("integer");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<string>("MetricType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Notes")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<decimal>("OnTimeDeliveryRate")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime>("PeriodEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime>("PeriodStart")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("SuccessfulShipments")
                        .HasColumnType("integer");

                    b.Property<int>("TotalShipments")
                        .HasColumnType("integer");

                    b.Property<string>("Unit")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal>("Value")
                        .HasPrecision(18, 4)
                        .HasColumnType("numeric(18,4)");

                    b.HasKey("Id");

                    b.HasIndex("CalculatedAt")
                        .HasDatabaseName("IX_CarrierPerformanceMetrics_CalculatedAt");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CarrierPerformanceMetrics_CreatedAt");

                    b.HasIndex("MetricType")
                        .HasDatabaseName("IX_CarrierPerformanceMetrics_MetricType");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("IX_CarrierPerformanceMetrics_OrganizationId");

                    b.HasIndex("PeriodStart")
                        .HasDatabaseName("IX_CarrierPerformanceMetrics_PeriodStart");

                    b.HasIndex("CarrierId", "PeriodStart", "PeriodEnd", "MetricType")
                        .IsUnique()
                        .HasDatabaseName("IX_CarrierPerformanceMetrics_Carrier_Period_Type");

                    b.ToTable("CarrierPerformanceMetrics", (string)null);
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierService", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("AccessorialCharges")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("AccountRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("ApiServiceCode")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("CarrierId")
                        .HasColumnType("uuid");

                    b.Property<string>("ComplianceRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("Conditions")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<TimeSpan?>("CutoffTime")
                        .HasColumnType("interval");

                    b.Property<DateTime?>("DeletedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeletedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("DocumentationRequirements")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<DateTime?>("EffectiveDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FuelSurchargeStructure")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsGuaranteed")
                        .HasColumnType("boolean");

                    b.Property<string>("LabelFormat")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("MaxDeclaredValue")
                        .HasColumnType("numeric");

                    b.Property<int?>("MaxDeclaredValueCurrency")
                        .HasColumnType("integer");

                    b.Property<string>("MaxDimensionsUnit")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<decimal?>("MaxHeight")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxLength")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MaxWeight")
                        .HasColumnType("numeric");

                    b.Property<string>("MaxWeightUnit")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<decimal?>("MaxWidth")
                        .HasColumnType("numeric");

                    b.Property<decimal?>("MinWeight")
                        .HasColumnType("numeric");

                    b.Property<string>("MinWeightUnit")
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<string>("RateStructure")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<bool>("RequiresAccount")
                        .HasColumnType("boolean");

                    b.Property<bool>("RequiresSignature")
                        .HasColumnType("boolean");

                    b.Property<string>("RestrictedAreas")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("RestrictedCountries")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RestrictedCountries");

                    b.Property<string>("RestrictedPostalCodes")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("RestrictedPostalCodes");

                    b.Property<string>("ServiceArea")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<string>("ServiceCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("ServiceLevel")
                        .HasColumnType("integer");

                    b.Property<string>("ServiceName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("SpecialInstructions")
                        .HasMaxLength(1000)
                        .HasColumnType("character varying(1000)");

                    b.Property<string>("SupportedCountries")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("SupportedCountries");

                    b.Property<string>("SupportedPostalCodes")
                        .IsRequired()
                        .HasColumnType("text")
                        .HasColumnName("SupportedPostalCodes");

                    b.Property<bool>("SupportsCOD")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsCommercial")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsDropoff")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsHazmat")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsHolidayDelivery")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsInsurance")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsInternational")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsPickup")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsResidential")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsSaturdayDelivery")
                        .HasColumnType("boolean");

                    b.Property<bool>("SupportsSundayDelivery")
                        .HasColumnType("boolean");

                    b.Property<string>("Terms")
                        .HasMaxLength(2000)
                        .HasColumnType("character varying(2000)");

                    b.Property<string>("TrackingUrlTemplate")
                        .HasMaxLength(500)
                        .HasColumnType("character varying(500)");

                    b.Property<int>("TransitDays")
                        .HasColumnType("integer");

                    b.Property<int?>("TransitDaysMax")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("UpdatedBy")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt")
                        .HasDatabaseName("IX_CarrierServices_CreatedAt");

                    b.HasIndex("IsActive")
                        .HasDatabaseName("IX_CarrierServices_IsActive");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("IX_CarrierServices_OrganizationId");

                    b.HasIndex("ServiceLevel")
                        .HasDatabaseName("IX_CarrierServices_ServiceLevel");

                    b.HasIndex("CarrierId", "ServiceCode")
                        .IsUnique()
                        .HasDatabaseName("IX_CarrierServices_CarrierId_ServiceCode");

                    b.ToTable("CarrierServices", (string)null);
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.Carrier", b =>
                {
                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Address", "HeadquartersAddress", b1 =>
                        {
                            b1.Property<Guid>("CarrierId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("HeadquartersCity");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("HeadquartersCountry");

                            b1.Property<decimal?>("Latitude")
                                .HasPrecision(10, 7)
                                .HasColumnType("numeric(10,7)")
                                .HasColumnName("HeadquartersLatitude");

                            b1.Property<decimal?>("Longitude")
                                .HasPrecision(10, 7)
                                .HasColumnType("numeric(10,7)")
                                .HasColumnName("HeadquartersLongitude");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("HeadquartersPostalCode");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("HeadquartersState");

                            b1.Property<string>("Street1")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("HeadquartersStreet1");

                            b1.Property<string>("Street2")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("HeadquartersStreet2");

                            b1.HasKey("CarrierId");

                            b1.ToTable("Carriers");

                            b1.WithOwner()
                                .HasForeignKey("CarrierId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.ContactInfo", "PrimaryContact", b1 =>
                        {
                            b1.Property<Guid>("CarrierId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Department")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PrimaryContactDepartment");

                            b1.Property<string>("Email")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("PrimaryContactEmail");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("PrimaryContactName");

                            b1.Property<string>("Phone")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("PrimaryContactPhone");

                            b1.Property<string>("Title")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PrimaryContactTitle");

                            b1.HasKey("CarrierId");

                            b1.ToTable("Carriers");

                            b1.WithOwner()
                                .HasForeignKey("CarrierId");
                        });

                    b.Navigation("HeadquartersAddress")
                        .IsRequired();

                    b.Navigation("PrimaryContact")
                        .IsRequired();
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierAccount", b =>
                {
                    b.HasOne("CarrierIntegration.Domain.Entities.Carrier", null)
                        .WithMany("Accounts")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Address", "BillingAddress", b1 =>
                        {
                            b1.Property<Guid>("CarrierAccountId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("BillingAddressCity");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("BillingAddressCountry");

                            b1.Property<decimal?>("Latitude")
                                .HasPrecision(10, 7)
                                .HasColumnType("numeric(10,7)")
                                .HasColumnName("BillingAddressLatitude");

                            b1.Property<decimal?>("Longitude")
                                .HasPrecision(10, 7)
                                .HasColumnType("numeric(10,7)")
                                .HasColumnName("BillingAddressLongitude");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("BillingAddressPostalCode");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("BillingAddressState");

                            b1.Property<string>("Street1")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("BillingAddressStreet1");

                            b1.Property<string>("Street2")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("BillingAddressStreet2");

                            b1.HasKey("CarrierAccountId");

                            b1.ToTable("CarrierAccounts");

                            b1.WithOwner()
                                .HasForeignKey("CarrierAccountId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.ContactInfo", "BillingContact", b1 =>
                        {
                            b1.Property<Guid>("CarrierAccountId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Department")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("BillingContactDepartment");

                            b1.Property<string>("Email")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("BillingContactEmail");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("BillingContactName");

                            b1.Property<string>("Phone")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("BillingContactPhone");

                            b1.Property<string>("Title")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("BillingContactTitle");

                            b1.HasKey("CarrierAccountId");

                            b1.ToTable("CarrierAccounts");

                            b1.WithOwner()
                                .HasForeignKey("CarrierAccountId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.ContactInfo", "TechnicalContact", b1 =>
                        {
                            b1.Property<Guid>("CarrierAccountId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Department")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("TechnicalContactDepartment");

                            b1.Property<string>("Email")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("TechnicalContactEmail");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("TechnicalContactName");

                            b1.Property<string>("Phone")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("TechnicalContactPhone");

                            b1.Property<string>("Title")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("TechnicalContactTitle");

                            b1.HasKey("CarrierAccountId");

                            b1.ToTable("CarrierAccounts");

                            b1.WithOwner()
                                .HasForeignKey("CarrierAccountId");
                        });

                    b.Navigation("BillingAddress")
                        .IsRequired();

                    b.Navigation("BillingContact")
                        .IsRequired();

                    b.Navigation("TechnicalContact");
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierBooking", b =>
                {
                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Money", "ActualCost", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("ActualCostAmount");

                            b1.Property<int>("Currency")
                                .HasColumnType("integer")
                                .HasColumnName("ActualCostCurrency");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Money", "CODAmount", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("CODAmount");

                            b1.Property<int>("Currency")
                                .HasColumnType("integer")
                                .HasColumnName("CODCurrency");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Money", "DeclaredValue", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("DeclaredValueAmount");

                            b1.Property<int>("Currency")
                                .HasColumnType("integer")
                                .HasColumnName("DeclaredValueCurrency");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Address", "DeliveryAddress", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("DeliveryAddressCity");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("DeliveryAddressCountry");

                            b1.Property<decimal?>("Latitude")
                                .HasPrecision(10, 7)
                                .HasColumnType("numeric(10,7)")
                                .HasColumnName("DeliveryAddressLatitude");

                            b1.Property<decimal?>("Longitude")
                                .HasPrecision(10, 7)
                                .HasColumnType("numeric(10,7)")
                                .HasColumnName("DeliveryAddressLongitude");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("DeliveryAddressPostalCode");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("DeliveryAddressState");

                            b1.Property<string>("Street1")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("DeliveryAddressStreet1");

                            b1.Property<string>("Street2")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("DeliveryAddressStreet2");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.ContactInfo", "DeliveryContact", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Department")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("DeliveryContactDepartment");

                            b1.Property<string>("Email")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("DeliveryContactEmail");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("DeliveryContactName");

                            b1.Property<string>("Phone")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("DeliveryContactPhone");

                            b1.Property<string>("Title")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("DeliveryContactTitle");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Money", "EstimatedCost", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("EstimatedCostAmount");

                            b1.Property<int>("Currency")
                                .HasColumnType("integer")
                                .HasColumnName("EstimatedCostCurrency");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Address", "PickupAddress", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PickupAddressCity");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PickupAddressCountry");

                            b1.Property<decimal?>("Latitude")
                                .HasPrecision(10, 7)
                                .HasColumnType("numeric(10,7)")
                                .HasColumnName("PickupAddressLatitude");

                            b1.Property<decimal?>("Longitude")
                                .HasPrecision(10, 7)
                                .HasColumnType("numeric(10,7)")
                                .HasColumnName("PickupAddressLongitude");

                            b1.Property<string>("PostalCode")
                                .IsRequired()
                                .HasMaxLength(20)
                                .HasColumnType("character varying(20)")
                                .HasColumnName("PickupAddressPostalCode");

                            b1.Property<string>("State")
                                .IsRequired()
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PickupAddressState");

                            b1.Property<string>("Street1")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("PickupAddressStreet1");

                            b1.Property<string>("Street2")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("PickupAddressStreet2");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.ContactInfo", "PickupContact", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Department")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PickupContactDepartment");

                            b1.Property<string>("Email")
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("PickupContactEmail");

                            b1.Property<string>("Name")
                                .IsRequired()
                                .HasMaxLength(200)
                                .HasColumnType("character varying(200)")
                                .HasColumnName("PickupContactName");

                            b1.Property<string>("Phone")
                                .HasMaxLength(50)
                                .HasColumnType("character varying(50)")
                                .HasColumnName("PickupContactPhone");

                            b1.Property<string>("Title")
                                .HasMaxLength(100)
                                .HasColumnType("character varying(100)")
                                .HasColumnName("PickupContactTitle");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Dimensions", "TotalDimensions", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Height")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("TotalDimensionsHeight");

                            b1.Property<decimal>("Length")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("TotalDimensionsLength");

                            b1.Property<string>("Unit")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)")
                                .HasColumnName("TotalDimensionsUnit");

                            b1.Property<decimal>("Width")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("TotalDimensionsWidth");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Weight", "TotalWeight", b1 =>
                        {
                            b1.Property<Guid>("CarrierBookingId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Unit")
                                .IsRequired()
                                .HasMaxLength(10)
                                .HasColumnType("character varying(10)")
                                .HasColumnName("TotalWeightUnit");

                            b1.Property<decimal>("Value")
                                .HasPrecision(18, 3)
                                .HasColumnType("numeric(18,3)")
                                .HasColumnName("TotalWeightValue");

                            b1.HasKey("CarrierBookingId");

                            b1.ToTable("CarrierBookings");

                            b1.WithOwner()
                                .HasForeignKey("CarrierBookingId");
                        });

                    b.Navigation("ActualCost");

                    b.Navigation("CODAmount");

                    b.Navigation("DeclaredValue");

                    b.Navigation("DeliveryAddress")
                        .IsRequired();

                    b.Navigation("DeliveryContact")
                        .IsRequired();

                    b.Navigation("EstimatedCost");

                    b.Navigation("PickupAddress")
                        .IsRequired();

                    b.Navigation("PickupContact")
                        .IsRequired();

                    b.Navigation("TotalDimensions");

                    b.Navigation("TotalWeight")
                        .IsRequired();
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierCompliance", b =>
                {
                    b.HasOne("CarrierIntegration.Domain.Entities.Carrier", null)
                        .WithMany("ComplianceRecords")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierPerformanceMetric", b =>
                {
                    b.HasOne("CarrierIntegration.Domain.Entities.Carrier", null)
                        .WithMany("PerformanceMetrics")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Money", "AverageCost", b1 =>
                        {
                            b1.Property<Guid>("CarrierPerformanceMetricId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("AverageCostAmount");

                            b1.Property<int>("Currency")
                                .HasColumnType("integer")
                                .HasColumnName("AverageCostCurrency");

                            b1.HasKey("CarrierPerformanceMetricId");

                            b1.ToTable("CarrierPerformanceMetrics");

                            b1.WithOwner()
                                .HasForeignKey("CarrierPerformanceMetricId");
                        });

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Money", "TotalRevenue", b1 =>
                        {
                            b1.Property<Guid>("CarrierPerformanceMetricId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("TotalRevenueAmount");

                            b1.Property<int>("Currency")
                                .HasColumnType("integer")
                                .HasColumnName("TotalRevenueCurrency");

                            b1.HasKey("CarrierPerformanceMetricId");

                            b1.ToTable("CarrierPerformanceMetrics");

                            b1.WithOwner()
                                .HasForeignKey("CarrierPerformanceMetricId");
                        });

                    b.Navigation("AverageCost");

                    b.Navigation("TotalRevenue");
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.CarrierService", b =>
                {
                    b.HasOne("CarrierIntegration.Domain.Entities.Carrier", null)
                        .WithMany("Services")
                        .HasForeignKey("CarrierId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("CarrierIntegration.Domain.ValueObjects.Money", "BaseRate", b1 =>
                        {
                            b1.Property<Guid>("CarrierServiceId")
                                .HasColumnType("uuid");

                            b1.Property<decimal>("Amount")
                                .HasPrecision(18, 2)
                                .HasColumnType("numeric(18,2)")
                                .HasColumnName("BaseRateAmount");

                            b1.Property<int>("Currency")
                                .HasColumnType("integer")
                                .HasColumnName("BaseRateCurrency");

                            b1.HasKey("CarrierServiceId");

                            b1.ToTable("CarrierServices");

                            b1.WithOwner()
                                .HasForeignKey("CarrierServiceId");
                        });

                    b.Navigation("BaseRate");
                });

            modelBuilder.Entity("CarrierIntegration.Domain.Entities.Carrier", b =>
                {
                    b.Navigation("Accounts");

                    b.Navigation("ComplianceRecords");

                    b.Navigation("PerformanceMetrics");

                    b.Navigation("Services");
                });
#pragma warning restore 612, 618
        }
    }
}
