using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence.Repositories
{
    public class CarrierBookingRepository : ICarrierBookingRepository
    {
        private readonly CarrierIntegrationDbContext _context;

        public CarrierBookingRepository(CarrierIntegrationDbContext context)
        {
            _context = context;
        }

        public async Task<CarrierBooking?> GetByIdAsync(Guid id)
        {
            return await _context.CarrierBookings
                .FirstOrDefaultAsync(b => b.Id == id);
        }

        public async Task<CarrierBooking?> GetByReferenceAsync(string bookingReference, Guid organizationId)
        {
            return await _context.CarrierBookings
                .FirstOrDefaultAsync(b => b.BookingReference == bookingReference && b.OrganizationId == organizationId);
        }

        public async Task<IReadOnlyList<CarrierBooking>> GetByCarrierIdAsync(Guid carrierId, Guid organizationId)
        {
            return await _context.CarrierBookings
                .Where(b => b.CarrierId == carrierId && b.OrganizationId == organizationId)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<CarrierBooking>> GetByOrderIdAsync(Guid orderId, Guid organizationId)
        {
            return await _context.CarrierBookings
                .Where(b => b.OrderId == orderId && b.OrganizationId == organizationId)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<CarrierBooking>> GetByShipmentIdAsync(Guid shipmentId, Guid organizationId)
        {
            return await _context.CarrierBookings
                .Where(b => b.ShipmentId == shipmentId && b.OrganizationId == organizationId)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<CarrierBooking>> GetByStatusAsync(BookingStatus status, Guid organizationId)
        {
            return await _context.CarrierBookings
                .Where(b => b.Status == status && b.OrganizationId == organizationId)
                .OrderByDescending(b => b.CreatedAt)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<CarrierBooking>> GetExpiredBookingsAsync(Guid organizationId)
        {
            var now = DateTime.UtcNow;
            return await _context.CarrierBookings
                .Where(b => b.OrganizationId == organizationId && 
                           b.ExpirationDate.HasValue && 
                           b.ExpirationDate.Value < now &&
                           b.Status != BookingStatus.Completed &&
                           b.Status != BookingStatus.Cancelled)
                .OrderBy(b => b.ExpirationDate)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<CarrierBooking>> GetBookingsForRetryAsync(Guid organizationId)
        {
            var now = DateTime.UtcNow;
            return await _context.CarrierBookings
                .Where(b => b.OrganizationId == organizationId &&
                           b.NextRetryDate.HasValue &&
                           b.NextRetryDate.Value <= now &&
                           b.Status == BookingStatus.Failed)
                .OrderBy(b => b.NextRetryDate)
                .ToListAsync();
        }

        public async Task<(IReadOnlyList<CarrierBooking> bookings, int totalCount)> GetPagedAsync(
            Guid organizationId,
            int pageNumber,
            int pageSize,
            string? searchTerm = null,
            Guid? carrierId = null,
            BookingStatus? status = null,
            DateTime? fromDate = null,
            DateTime? toDate = null)
        {
            var query = _context.CarrierBookings
                .Where(b => b.OrganizationId == organizationId);

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var search = searchTerm.ToLowerInvariant();
                query = query.Where(b =>
                    b.BookingReference.ToLower().Contains(search) ||
                    (b.CarrierBookingReference != null && b.CarrierBookingReference.ToLower().Contains(search)) ||
                    (b.TrackingNumber != null && b.TrackingNumber.ToLower().Contains(search)));
            }

            if (carrierId.HasValue)
            {
                query = query.Where(b => b.CarrierId == carrierId.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(b => b.Status == status.Value);
            }

            if (fromDate.HasValue)
            {
                query = query.Where(b => b.BookingDate >= fromDate.Value);
            }

            if (toDate.HasValue)
            {
                query = query.Where(b => b.BookingDate <= toDate.Value);
            }

            var totalCount = await query.CountAsync();

            var bookings = await query
                .OrderByDescending(b => b.CreatedAt)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (bookings, totalCount);
        }

        public async Task<CarrierBooking> AddAsync(CarrierBooking booking)
        {
            _context.CarrierBookings.Add(booking);
            return booking;
        }

        public async Task UpdateAsync(CarrierBooking booking)
        {
            _context.CarrierBookings.Update(booking);
            await Task.CompletedTask;
        }

        public async Task DeleteAsync(CarrierBooking booking)
        {
            _context.CarrierBookings.Remove(booking);
            await Task.CompletedTask;
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.CarrierBookings.AnyAsync(b => b.Id == id);
        }

        public async Task<bool> ReferenceExistsAsync(string bookingReference, Guid organizationId, Guid? excludeId = null)
        {
            var query = _context.CarrierBookings
                .Where(b => b.BookingReference == bookingReference && b.OrganizationId == organizationId);

            if (excludeId.HasValue)
            {
                query = query.Where(b => b.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }
    }
}
