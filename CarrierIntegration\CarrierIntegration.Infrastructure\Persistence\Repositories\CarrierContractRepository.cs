using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence.Repositories
{
    public class CarrierContractRepository : BaseRepository<CarrierContract>, ICarrierContractRepository
    {
        public CarrierContractRepository(CarrierIntegrationDbContext context) : base(context)
        {
        }

        public async Task<List<CarrierContract>> GetByCarrierIdAsync(Guid carrierId)
        {
            return await _context.CarrierContracts
                .Where(cc => cc.CarrierId == carrierId)
                .OrderByDescending(cc => cc.EffectiveDate)
                .ToListAsync();
        }

        public async Task<List<CarrierContract>> GetByOrganizationIdAsync(Guid organizationId)
        {
            return await _context.CarrierContracts
                .Include(cc => cc.Carrier)
                .Where(cc => cc.OrganizationId == organizationId)
                .OrderByDescending(cc => cc.EffectiveDate)
                .ToListAsync();
        }

        public async Task<List<CarrierContract>> GetActiveContractsAsync(Guid organizationId)
        {
            var currentDate = DateTime.UtcNow;
            
            return await _context.CarrierContracts
                .Include(cc => cc.Carrier)
                .Where(cc => cc.OrganizationId == organizationId && 
                            cc.Status == "Active" &&
                            cc.EffectiveDate <= currentDate &&
                            (cc.ExpirationDate == null || cc.ExpirationDate > currentDate))
                .OrderByDescending(cc => cc.EffectiveDate)
                .ToListAsync();
        }

        public async Task<List<CarrierContract>> GetExpiringContractsAsync(Guid organizationId, int daysAhead)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(daysAhead);
            
            return await _context.CarrierContracts
                .Include(cc => cc.Carrier)
                .Where(cc => cc.OrganizationId == organizationId && 
                            cc.ExpirationDate.HasValue && 
                            cc.ExpirationDate.Value <= cutoffDate &&
                            cc.ExpirationDate.Value >= DateTime.UtcNow &&
                            cc.Status == "Active")
                .OrderBy(cc => cc.ExpirationDate)
                .ToListAsync();
        }

        public async Task<List<CarrierContract>> GetByStatusAsync(Guid organizationId, string status)
        {
            return await _context.CarrierContracts
                .Include(cc => cc.Carrier)
                .Where(cc => cc.OrganizationId == organizationId && cc.Status == status)
                .OrderByDescending(cc => cc.EffectiveDate)
                .ToListAsync();
        }

        public async Task<List<CarrierContract>> GetByContractTypeAsync(Guid organizationId, string contractType)
        {
            return await _context.CarrierContracts
                .Include(cc => cc.Carrier)
                .Where(cc => cc.OrganizationId == organizationId && cc.ContractType == contractType)
                .OrderByDescending(cc => cc.EffectiveDate)
                .ToListAsync();
        }

        public async Task<CarrierContract?> GetByContractNumberAsync(string contractNumber, Guid organizationId)
        {
            return await _context.CarrierContracts
                .Include(cc => cc.Carrier)
                .FirstOrDefaultAsync(cc => cc.ContractNumber == contractNumber && cc.OrganizationId == organizationId);
        }

        public async Task<bool> ContractNumberExistsAsync(string contractNumber, Guid organizationId, Guid? excludeId = null)
        {
            var query = _context.CarrierContracts
                .Where(cc => cc.ContractNumber == contractNumber && cc.OrganizationId == organizationId);

            if (excludeId.HasValue)
                query = query.Where(cc => cc.Id != excludeId.Value);

            return await query.AnyAsync();
        }
    }
}
