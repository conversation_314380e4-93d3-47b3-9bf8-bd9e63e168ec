using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence.Repositories
{
    public class CarrierRelationshipScoreRepository : BaseRepository<CarrierRelationshipScore>, ICarrierRelationshipScoreRepository
    {
        public CarrierRelationshipScoreRepository(CarrierIntegrationDbContext context) : base(context)
        {
        }

        public async Task<List<CarrierRelationshipScore>> GetByCarrierIdAsync(Guid carrierId)
        {
            return await _context.CarrierRelationshipScores
                .Where(crs => crs.CarrierId == carrierId)
                .OrderByDescending(crs => crs.CalculationDate)
                .ToListAsync();
        }

        public async Task<List<CarrierRelationshipScore>> GetByOrganizationIdAsync(Guid organizationId)
        {
            return await _context.CarrierRelationshipScores
                .Include(crs => crs.Carrier)
                .Where(crs => crs.OrganizationId == organizationId)
                .OrderByDescending(crs => crs.CalculationDate)
                .ToListAsync();
        }

        public async Task<CarrierRelationshipScore?> GetLatestScoreAsync(Guid carrierId, Guid organizationId)
        {
            return await _context.CarrierRelationshipScores
                .Include(crs => crs.Carrier)
                .Where(crs => crs.CarrierId == carrierId && crs.OrganizationId == organizationId)
                .OrderByDescending(crs => crs.CalculationDate)
                .FirstOrDefaultAsync();
        }

        public async Task<List<CarrierRelationshipScore>> GetByPeriodAsync(Guid organizationId, DateTime startDate, DateTime endDate)
        {
            return await _context.CarrierRelationshipScores
                .Include(crs => crs.Carrier)
                .Where(crs => crs.OrganizationId == organizationId && 
                             crs.CalculationDate >= startDate && 
                             crs.CalculationDate <= endDate)
                .OrderByDescending(crs => crs.CalculationDate)
                .ToListAsync();
        }

        public async Task<List<CarrierRelationshipScore>> GetTopPerformingCarriersAsync(Guid organizationId, int count = 10)
        {
            // Get the latest score for each carrier
            var latestScores = await _context.CarrierRelationshipScores
                .Include(crs => crs.Carrier)
                .Where(crs => crs.OrganizationId == organizationId)
                .GroupBy(crs => crs.CarrierId)
                .Select(g => g.OrderByDescending(crs => crs.CalculationDate).First())
                .OrderByDescending(crs => crs.OverallScore)
                .Take(count)
                .ToListAsync();

            return latestScores;
        }

        public async Task<List<CarrierRelationshipScore>> GetByScoreRangeAsync(Guid organizationId, decimal minScore, decimal maxScore)
        {
            // Get the latest score for each carrier within the range
            var latestScores = await _context.CarrierRelationshipScores
                .Include(crs => crs.Carrier)
                .Where(crs => crs.OrganizationId == organizationId)
                .GroupBy(crs => crs.CarrierId)
                .Select(g => g.OrderByDescending(crs => crs.CalculationDate).First())
                .Where(crs => crs.OverallScore >= minScore && crs.OverallScore <= maxScore)
                .OrderByDescending(crs => crs.OverallScore)
                .ToListAsync();

            return latestScores;
        }
    }
}
