using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence.Repositories
{
    public class CarrierRepository : ICarrierRepository
    {
        private readonly CarrierIntegrationDbContext _context;

        public CarrierRepository(CarrierIntegrationDbContext context)
        {
            _context = context;
        }

        public async Task<Carrier?> GetByIdAsync(Guid id)
        {
            return await _context.Carriers
                .Include(c => c.Services)
                .Include(c => c.Accounts)
                .FirstOrDefaultAsync(c => c.Id == id);
        }

        public async Task<Carrier?> GetByCodeAsync(string code, Guid organizationId)
        {
            return await _context.Carriers
                .Include(c => c.Services)
                .Include(c => c.Accounts)
                .FirstOrDefaultAsync(c => c.Code == code && c.OrganizationId == organizationId);
        }

        public async Task<IReadOnlyList<Carrier>> GetAllAsync(Guid organizationId)
        {
            return await _context.Carriers
                .Where(c => c.OrganizationId == organizationId)
                .Include(c => c.Services)
                .Include(c => c.Accounts)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<Carrier>> GetActiveCarriersAsync(Guid organizationId)
        {
            return await _context.Carriers
                .Where(c => c.OrganizationId == organizationId && c.IsActive)
                .Include(c => c.Services.Where(s => s.IsActive))
                .Include(c => c.Accounts.Where(a => a.IsActive))
                .OrderBy(c => c.Priority)
                .ThenBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<Carrier>> GetCarriersByTypeAsync(CarrierType type, Guid organizationId)
        {
            return await _context.Carriers
                .Where(c => c.OrganizationId == organizationId && c.Type == type)
                .Include(c => c.Services)
                .Include(c => c.Accounts)
                .OrderBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<IReadOnlyList<Carrier>> GetCarriersWithCapabilityAsync(string capability, Guid organizationId)
        {
            var query = _context.Carriers
                .Where(c => c.OrganizationId == organizationId && c.IsActive);

            query = capability.ToLowerInvariant() switch
            {
                "tracking" => query.Where(c => c.SupportsTracking),
                "rating" => query.Where(c => c.SupportsRating),
                "labeling" => query.Where(c => c.SupportsLabeling),
                "pickup" => query.Where(c => c.SupportsPickup),
                "international" => query.Where(c => c.SupportsInternational),
                "delivery_confirmation" => query.Where(c => c.SupportsDeliveryConfirmation),
                _ => query
            };

            return await query
                .Include(c => c.Services.Where(s => s.IsActive))
                .Include(c => c.Accounts.Where(a => a.IsActive))
                .OrderBy(c => c.Priority)
                .ThenBy(c => c.Name)
                .ToListAsync();
        }

        public async Task<(IReadOnlyList<Carrier> carriers, int totalCount)> GetPagedAsync(
            Guid organizationId,
            int pageNumber,
            int pageSize,
            string? searchTerm = null,
            CarrierType? type = null,
            CarrierStatus? status = null)
        {
            var query = _context.Carriers
                .Where(c => c.OrganizationId == organizationId);

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var search = searchTerm.ToLowerInvariant();
                query = query.Where(c =>
                    c.Name.ToLower().Contains(search) ||
                    c.Code.ToLower().Contains(search) ||
                    (c.Description != null && c.Description.ToLower().Contains(search)));
            }

            if (type.HasValue)
            {
                query = query.Where(c => c.Type == type.Value);
            }

            if (status.HasValue)
            {
                query = query.Where(c => c.Status == status.Value);
            }

            var totalCount = await query.CountAsync();

            var carriers = await query
                .Include(c => c.Services.Where(s => s.IsActive))
                .Include(c => c.Accounts.Where(a => a.IsActive))
                .OrderBy(c => c.Priority)
                .ThenBy(c => c.Name)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (carriers, totalCount);
        }

        public async Task<Carrier> AddAsync(Carrier carrier)
        {
            _context.Carriers.Add(carrier);
            return carrier;
        }

        public async Task UpdateAsync(Carrier carrier)
        {
            _context.Carriers.Update(carrier);
            await Task.CompletedTask;
        }

        public async Task DeleteAsync(Carrier carrier)
        {
            _context.Carriers.Remove(carrier);
            await Task.CompletedTask;
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            return await _context.Carriers.AnyAsync(c => c.Id == id);
        }

        public async Task<bool> CodeExistsAsync(string code, Guid organizationId, Guid? excludeId = null)
        {
            var query = _context.Carriers
                .Where(c => c.Code == code && c.OrganizationId == organizationId);

            if (excludeId.HasValue)
            {
                query = query.Where(c => c.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }
    }
}
