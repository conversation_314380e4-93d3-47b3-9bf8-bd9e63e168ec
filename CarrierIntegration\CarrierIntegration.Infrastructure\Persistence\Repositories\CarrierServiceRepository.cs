using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence.Repositories
{
    public class CarrierServiceRepository : BaseRepository<CarrierService>, ICarrierServiceRepository
    {
        public CarrierServiceRepository(CarrierIntegrationDbContext context) : base(context)
        {
        }

        public async Task<List<CarrierService>> GetByCarrierIdAsync(Guid carrierId)
        {
            return await _context.CarrierServices
                .Where(cs => cs.CarrierId == carrierId)
                .OrderBy(cs => cs.ServiceName)
                .ToListAsync();
        }

        public async Task<List<CarrierService>> GetByOrganizationIdAsync(Guid organizationId)
        {
            return await _context.CarrierServices
                .Include(cs => cs.Carrier)
                .Where(cs => cs.OrganizationId == organizationId)
                .OrderBy(cs => cs.Carrier.Name)
                .ThenBy(cs => cs.ServiceName)
                .ToListAsync();
        }

        public async Task<List<CarrierService>> GetActiveServicesAsync(Guid organizationId)
        {
            return await _context.CarrierServices
                .Include(cs => cs.Carrier)
                .Where(cs => cs.OrganizationId == organizationId && cs.IsActive)
                .OrderBy(cs => cs.Carrier.Name)
                .ThenBy(cs => cs.ServiceName)
                .ToListAsync();
        }

        public async Task<CarrierService?> GetByServiceCodeAsync(string serviceCode, Guid carrierId)
        {
            return await _context.CarrierServices
                .Include(cs => cs.Carrier)
                .FirstOrDefaultAsync(cs => cs.ServiceCode == serviceCode && cs.CarrierId == carrierId);
        }

        public async Task<List<CarrierService>> GetByServiceLevelAsync(string serviceLevel, Guid organizationId)
        {
            return await _context.CarrierServices
                .Include(cs => cs.Carrier)
                .Where(cs => cs.OrganizationId == organizationId && cs.ServiceLevel == serviceLevel)
                .OrderBy(cs => cs.Carrier.Name)
                .ThenBy(cs => cs.ServiceName)
                .ToListAsync();
        }

        public async Task<bool> ServiceCodeExistsAsync(string serviceCode, Guid carrierId, Guid? excludeId = null)
        {
            var query = _context.CarrierServices
                .Where(cs => cs.ServiceCode == serviceCode && cs.CarrierId == carrierId);

            if (excludeId.HasValue)
                query = query.Where(cs => cs.Id != excludeId.Value);

            return await query.AnyAsync();
        }
    }
}
