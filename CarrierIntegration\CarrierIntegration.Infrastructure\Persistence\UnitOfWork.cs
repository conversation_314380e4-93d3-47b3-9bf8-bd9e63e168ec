using CarrierIntegration.Application.Common.Interfaces;
using Microsoft.EntityFrameworkCore.Storage;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Persistence
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly CarrierIntegrationDbContext _context;
        private IDbContextTransaction? _transaction;

        public UnitOfWork(CarrierIntegrationDbContext context)
        {
            _context = context;
            Carriers = new Repositories.CarrierRepository(_context);
            CarrierServices = new Repositories.CarrierServiceRepository(_context);
            CarrierAccounts = new Repositories.CarrierAccountRepository(_context);
            CarrierBookings = new Repositories.CarrierBookingRepository(_context);
            CarrierPerformanceMetrics = new Repositories.CarrierPerformanceMetricRepository(_context);
            CarrierCompliances = new Repositories.CarrierComplianceRepository(_context);
            CarrierContracts = new Repositories.CarrierContractRepository(_context);
            CarrierRelationshipScores = new Repositories.CarrierRelationshipScoreRepository(_context);
        }

        public ICarrierRepository Carriers { get; }
        public ICarrierServiceRepository CarrierServices { get; }
        public ICarrierAccountRepository CarrierAccounts { get; }
        public ICarrierBookingRepository CarrierBookings { get; }
        public ICarrierPerformanceMetricRepository CarrierPerformanceMetrics { get; }
        public ICarrierComplianceRepository CarrierCompliances { get; }
        public ICarrierContractRepository CarrierContracts { get; }
        public ICarrierRelationshipScoreRepository CarrierRelationshipScores { get; }

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            return await _context.SaveChangesAsync(cancellationToken);
        }

        public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
        {
            _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
        }

        public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_transaction != null)
            {
                await _transaction.CommitAsync(cancellationToken);
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
        {
            if (_transaction != null)
            {
                await _transaction.RollbackAsync(cancellationToken);
                await _transaction.DisposeAsync();
                _transaction = null;
            }
        }

        public void Dispose()
        {
            _transaction?.Dispose();
            _context.Dispose();
        }
    }
}
