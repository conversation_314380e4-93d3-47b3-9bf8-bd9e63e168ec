using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Services
{
    public interface ICachingService
    {
        Task<T?> GetAsync<T>(string key) where T : class;
        Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class;
        Task RemoveAsync(string key);
        Task RemoveByPatternAsync(string pattern);
        string GenerateKey(string prefix, params object[] identifiers);
    }

    public class CachingService : ICachingService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly ILogger<CachingService> _logger;
        private readonly JsonSerializerSettings _jsonSettings;

        public CachingService(
            IDistributedCache distributedCache,
            ILogger<CachingService> logger)
        {
            _distributedCache = distributedCache;
            _logger = logger;
            
            _jsonSettings = new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Auto,
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                NullValueHandling = NullValueHandling.Ignore
            };
        }

        public async Task<T?> GetAsync<T>(string key) where T : class
        {
            try
            {
                var cachedValue = await _distributedCache.GetStringAsync(key);
                
                if (string.IsNullOrEmpty(cachedValue))
                {
                    _logger.LogDebug("Cache miss for key: {Key}", key);
                    return null;
                }

                var result = JsonConvert.DeserializeObject<T>(cachedValue, _jsonSettings);
                _logger.LogDebug("Cache hit for key: {Key}", key);
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving cached value for key: {Key}", key);
                return null;
            }
        }

        public async Task SetAsync<T>(string key, T value, TimeSpan? expiration = null) where T : class
        {
            try
            {
                if (value == null)
                {
                    _logger.LogWarning("Attempted to cache null value for key: {Key}", key);
                    return;
                }

                var serializedValue = JsonConvert.SerializeObject(value, _jsonSettings);
                
                var options = new DistributedCacheEntryOptions();
                
                if (expiration.HasValue)
                {
                    options.SetAbsoluteExpiration(expiration.Value);
                }
                else
                {
                    // Default expiration of 1 hour
                    options.SetAbsoluteExpiration(TimeSpan.FromHours(1));
                }

                // Set sliding expiration to 30 minutes
                options.SetSlidingExpiration(TimeSpan.FromMinutes(30));

                await _distributedCache.SetStringAsync(key, serializedValue, options);
                
                _logger.LogDebug("Cached value for key: {Key} with expiration: {Expiration}", 
                    key, expiration?.ToString() ?? "1 hour");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error caching value for key: {Key}", key);
            }
        }

        public async Task RemoveAsync(string key)
        {
            try
            {
                await _distributedCache.RemoveAsync(key);
                _logger.LogDebug("Removed cached value for key: {Key}", key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached value for key: {Key}", key);
            }
        }

        public async Task RemoveByPatternAsync(string pattern)
        {
            try
            {
                // Note: Redis pattern-based deletion requires additional implementation
                // This is a placeholder for pattern-based cache invalidation
                _logger.LogInformation("Pattern-based cache removal requested for pattern: {Pattern}", pattern);
                
                // For now, we'll log this operation
                // In a full implementation, you would need to:
                // 1. Connect to Redis directly
                // 2. Use SCAN command to find keys matching the pattern
                // 3. Delete matching keys
                
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cached values by pattern: {Pattern}", pattern);
            }
        }

        public string GenerateKey(string prefix, params object[] identifiers)
        {
            if (string.IsNullOrWhiteSpace(prefix))
                throw new ArgumentException("Prefix cannot be null or empty", nameof(prefix));

            if (identifiers == null || identifiers.Length == 0)
                return prefix;

            var keyParts = new string[identifiers.Length + 1];
            keyParts[0] = prefix;

            for (int i = 0; i < identifiers.Length; i++)
            {
                keyParts[i + 1] = identifiers[i]?.ToString() ?? "null";
            }

            return string.Join(":", keyParts);
        }
    }

    // Cache key constants for consistency
    public static class CacheKeys
    {
        public const string CARRIER_PREFIX = "carrier";
        public const string CARRIER_RATES_PREFIX = "carrier:rates";
        public const string CARRIER_SERVICES_PREFIX = "carrier:services";
        public const string CARRIER_ACCOUNTS_PREFIX = "carrier:accounts";
        public const string CARRIER_PERFORMANCE_PREFIX = "carrier:performance";
        public const string CARRIER_COMPLIANCE_PREFIX = "carrier:compliance";
        public const string CARRIER_HEALTH_PREFIX = "carrier:health";
        
        // Cache expiration times
        public static readonly TimeSpan CARRIER_CACHE_DURATION = TimeSpan.FromHours(4);
        public static readonly TimeSpan RATES_CACHE_DURATION = TimeSpan.FromMinutes(15);
        public static readonly TimeSpan SERVICES_CACHE_DURATION = TimeSpan.FromHours(2);
        public static readonly TimeSpan ACCOUNTS_CACHE_DURATION = TimeSpan.FromHours(1);
        public static readonly TimeSpan PERFORMANCE_CACHE_DURATION = TimeSpan.FromHours(6);
        public static readonly TimeSpan COMPLIANCE_CACHE_DURATION = TimeSpan.FromHours(12);
        public static readonly TimeSpan HEALTH_CACHE_DURATION = TimeSpan.FromMinutes(5);
    }

    // Cached data wrapper for metadata
    public class CachedData<T>
    {
        public T Data { get; set; }
        public DateTime CachedAt { get; set; }
        public TimeSpan ExpiresIn { get; set; }
        public string Source { get; set; }

        public CachedData(T data, string source = "Unknown")
        {
            Data = data;
            CachedAt = DateTime.UtcNow;
            Source = source;
        }

        public bool IsExpired => DateTime.UtcNow > CachedAt.Add(ExpiresIn);
    }
}
