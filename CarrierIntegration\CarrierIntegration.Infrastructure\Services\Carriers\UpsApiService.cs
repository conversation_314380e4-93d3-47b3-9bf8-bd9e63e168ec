using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Application.DTOs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Services.Carriers
{
    public class UpsApiService : ICarrierSpecificApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private readonly ILogger<UpsApiService> _logger;
        private readonly string _baseUrl;
        private readonly string _accessKey;
        private readonly string _username;
        private readonly string _password;

        public UpsApiService(
            HttpClient httpClient,
            IConfiguration configuration,
            ILogger<UpsApiService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _logger = logger;

            _baseUrl = _configuration["Carriers:UPS:BaseUrl"] ?? "https://onlinetools.ups.com/rest";
            _accessKey = _configuration["Carriers:UPS:AccessKey"] ?? "";
            _username = _configuration["Carriers:UPS:Username"] ?? "";
            _password = _configuration["Carriers:UPS:Password"] ?? "";

            ConfigureHttpClient();
        }

        public string CarrierCode => "UPS";

        public async Task<CarrierApiResponse<List<CarrierRateDto>>> GetRatesAsync(CarrierRateRequest request)
        {
            try
            {
                _logger.LogInformation("Getting UPS rates for shipment from {Origin} to {Destination}",
                    request.OriginAddress.City, request.DestinationAddress.City);

                var upsRequest = BuildUpsRateRequest(request);
                var requestJson = JsonConvert.SerializeObject(upsRequest);

                var httpRequest = new HttpRequestMessage(HttpMethod.Post, "/Rate")
                {
                    Content = new StringContent(requestJson, Encoding.UTF8, "application/json")
                };

                AddAuthenticationHeaders(httpRequest);

                var response = await _httpClient.SendAsync(httpRequest);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var upsResponse = JsonConvert.DeserializeObject<UpsRateResponse>(responseContent);
                    var rates = ParseUpsRateResponse(upsResponse, request);

                    _logger.LogInformation("Successfully retrieved {RateCount} UPS rates", rates.Count);
                    return CarrierApiResponse<List<CarrierRateDto>>.Success(rates);
                }
                else
                {
                    _logger.LogWarning("UPS rate request failed with status {StatusCode}: {Response}",
                        response.StatusCode, responseContent);
                    return CarrierApiResponse<List<CarrierRateDto>>.Failure($"UPS API error: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting UPS rates");
                return CarrierApiResponse<List<CarrierRateDto>>.Failure($"UPS rate request failed: {ex.Message}");
            }
        }

        public async Task<CarrierApiResponse<CarrierBookingResponseDto>> CreateBookingAsync(CarrierBookingRequestDto request)
        {
            try
            {
                _logger.LogInformation("Creating UPS booking for reference {BookingReference}", request.BookingReference);

                var upsRequest = BuildUpsShipmentRequest(request);
                var requestJson = JsonConvert.SerializeObject(upsRequest);

                var httpRequest = new HttpRequestMessage(HttpMethod.Post, "/Ship")
                {
                    Content = new StringContent(requestJson, Encoding.UTF8, "application/json")
                };

                AddAuthenticationHeaders(httpRequest);

                var response = await _httpClient.SendAsync(httpRequest);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var upsResponse = JsonConvert.DeserializeObject<UpsShipmentResponse>(responseContent);
                    var bookingResponse = ParseUpsShipmentResponse(upsResponse, request);

                    _logger.LogInformation("Successfully created UPS booking with tracking number {TrackingNumber}",
                        bookingResponse.TrackingNumber);
                    return CarrierApiResponse<CarrierBookingResponseDto>.Success(bookingResponse);
                }
                else
                {
                    _logger.LogWarning("UPS booking request failed with status {StatusCode}: {Response}",
                        response.StatusCode, responseContent);
                    return CarrierApiResponse<CarrierBookingResponseDto>.Failure($"UPS API error: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating UPS booking");
                return CarrierApiResponse<CarrierBookingResponseDto>.Failure($"UPS booking failed: {ex.Message}");
            }
        }

        public async Task<CarrierApiResponse<CarrierTrackingDto>> GetTrackingAsync(string trackingNumber)
        {
            try
            {
                _logger.LogInformation("Getting UPS tracking for {TrackingNumber}", trackingNumber);

                var httpRequest = new HttpRequestMessage(HttpMethod.GET, $"/Track/{trackingNumber}");
                AddAuthenticationHeaders(httpRequest);

                var response = await _httpClient.SendAsync(httpRequest);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var upsResponse = JsonConvert.DeserializeObject<UpsTrackingResponse>(responseContent);
                    var tracking = ParseUpsTrackingResponse(upsResponse);

                    _logger.LogInformation("Successfully retrieved UPS tracking for {TrackingNumber}", trackingNumber);
                    return CarrierApiResponse<CarrierTrackingDto>.Success(tracking);
                }
                else
                {
                    _logger.LogWarning("UPS tracking request failed with status {StatusCode}: {Response}",
                        response.StatusCode, responseContent);
                    return CarrierApiResponse<CarrierTrackingDto>.Failure($"UPS API error: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting UPS tracking");
                return CarrierApiResponse<CarrierTrackingDto>.Failure($"UPS tracking failed: {ex.Message}");
            }
        }

        public async Task<CarrierApiResponse<bool>> CancelBookingAsync(string bookingReference, string reason)
        {
            try
            {
                _logger.LogInformation("Cancelling UPS booking {BookingReference}", bookingReference);

                var upsRequest = new
                {
                    VoidShipmentRequest = new
                    {
                        Request = new
                        {
                            RequestOption = "1"
                        },
                        ShipmentIdentificationNumber = bookingReference
                    }
                };

                var requestJson = JsonConvert.SerializeObject(upsRequest);
                var httpRequest = new HttpRequestMessage(HttpMethod.Post, "/Void")
                {
                    Content = new StringContent(requestJson, Encoding.UTF8, "application/json")
                };

                AddAuthenticationHeaders(httpRequest);

                var response = await _httpClient.SendAsync(httpRequest);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Successfully cancelled UPS booking {BookingReference}", bookingReference);
                    return CarrierApiResponse<bool>.Success(true);
                }
                else
                {
                    _logger.LogWarning("UPS cancellation failed with status {StatusCode}: {Response}",
                        response.StatusCode, responseContent);
                    return CarrierApiResponse<bool>.Failure($"UPS API error: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling UPS booking");
                return CarrierApiResponse<bool>.Failure($"UPS cancellation failed: {ex.Message}");
            }
        }

        public async Task<CarrierApiResponse<bool>> ValidateAddressAsync(AddressDto address)
        {
            try
            {
                _logger.LogInformation("Validating UPS address for {City}, {State}", address.City, address.State);

                var upsRequest = BuildUpsAddressValidationRequest(address);
                var requestJson = JsonConvert.SerializeObject(upsRequest);

                var httpRequest = new HttpRequestMessage(HttpMethod.Post, "/AddressValidation")
                {
                    Content = new StringContent(requestJson, Encoding.UTF8, "application/json")
                };

                AddAuthenticationHeaders(httpRequest);

                var response = await _httpClient.SendAsync(httpRequest);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var upsResponse = JsonConvert.DeserializeObject<UpsAddressValidationResponse>(responseContent);
                    var isValid = ParseUpsAddressValidationResponse(upsResponse);

                    _logger.LogInformation("UPS address validation result: {IsValid}", isValid);
                    return CarrierApiResponse<bool>.Success(isValid);
                }
                else
                {
                    _logger.LogWarning("UPS address validation failed with status {StatusCode}: {Response}",
                        response.StatusCode, responseContent);
                    return CarrierApiResponse<bool>.Failure($"UPS API error: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating UPS address");
                return CarrierApiResponse<bool>.Failure($"UPS address validation failed: {ex.Message}");
            }
        }

        private void ConfigureHttpClient()
        {
            _httpClient.BaseAddress = new Uri(_baseUrl);
            _httpClient.DefaultRequestHeaders.Add("User-Agent", "TriTrackz-CarrierIntegration/1.0");
            _httpClient.Timeout = TimeSpan.FromSeconds(30);
        }

        private void AddAuthenticationHeaders(HttpRequestMessage request)
        {
            request.Headers.Add("AccessLicenseNumber", _accessKey);
            request.Headers.Add("Username", _username);
            request.Headers.Add("Password", _password);
        }

        private object BuildUpsRateRequest(CarrierRateRequest request)
        {
            return new
            {
                RateRequest = new
                {
                    Request = new
                    {
                        RequestOption = "Rate",
                        TransactionReference = new
                        {
                            CustomerContext = request.CustomerReference ?? "TriTrackz Rate Request"
                        }
                    },
                    Shipment = new
                    {
                        Shipper = new
                        {
                            Address = new
                            {
                                AddressLine = request.OriginAddress.Street1,
                                City = request.OriginAddress.City,
                                StateProvinceCode = request.OriginAddress.State,
                                PostalCode = request.OriginAddress.PostalCode,
                                CountryCode = request.OriginAddress.Country
                            }
                        },
                        ShipTo = new
                        {
                            Address = new
                            {
                                AddressLine = request.DestinationAddress.Street1,
                                City = request.DestinationAddress.City,
                                StateProvinceCode = request.DestinationAddress.State,
                                PostalCode = request.DestinationAddress.PostalCode,
                                CountryCode = request.DestinationAddress.Country
                            }
                        },
                        Package = request.Packages.Select(p => new
                        {
                            PackagingType = new { Code = "02" }, // Customer Supplied Package
                            Dimensions = new
                            {
                                UnitOfMeasurement = new { Code = p.Dimensions?.Unit ?? "IN" },
                                Length = p.Dimensions?.Length.ToString("F2") ?? "1",
                                Width = p.Dimensions?.Width.ToString("F2") ?? "1",
                                Height = p.Dimensions?.Height.ToString("F2") ?? "1"
                            },
                            PackageWeight = new
                            {
                                UnitOfMeasurement = new { Code = p.Weight.Unit },
                                Weight = p.Weight.Value.ToString("F2")
                            }
                        }).ToArray()
                    }
                }
            };
        }

        private List<CarrierRateDto> ParseUpsRateResponse(UpsRateResponse response, CarrierRateRequest request)
        {
            var rates = new List<CarrierRateDto>();

            if (response?.RateResponse?.RatedShipment != null)
            {
                foreach (var ratedShipment in response.RateResponse.RatedShipment)
                {
                    rates.Add(new CarrierRateDto
                    {
                        CarrierId = request.CarrierId,
                        CarrierName = "UPS",
                        ServiceCode = ratedShipment.Service?.Code ?? "Unknown",
                        ServiceName = GetUpsServiceName(ratedShipment.Service?.Code),
                        ServiceLevel = GetServiceLevel(ratedShipment.Service?.Code),
                        TotalCost = new MoneyDto
                        {
                            Amount = decimal.Parse(ratedShipment.TotalCharges?.MonetaryValue ?? "0"),
                            Currency = ratedShipment.TotalCharges?.CurrencyCode ?? "USD"
                        },
                        TransitDays = int.Parse(ratedShipment.GuaranteedDelivery?.BusinessDaysInTransit ?? "1"),
                        IsGuaranteed = !string.IsNullOrEmpty(ratedShipment.GuaranteedDelivery?.DeliveryByTime),
                        EstimatedDeliveryDate = request.ShipDate.AddDays(int.Parse(ratedShipment.GuaranteedDelivery?.BusinessDaysInTransit ?? "1")),
                        CostBreakdown = new Dictionary<string, decimal>
                        {
                            ["Base"] = decimal.Parse(ratedShipment.TransportationCharges?.MonetaryValue ?? "0"),
                            ["Fuel"] = decimal.Parse(ratedShipment.FuelSurcharge?.MonetaryValue ?? "0"),
                            ["Total"] = decimal.Parse(ratedShipment.TotalCharges?.MonetaryValue ?? "0")
                        }
                    });
                }
            }

            return rates;
        }

        private object BuildUpsShipmentRequest(CarrierBookingRequestDto request)
        {
            // Implementation for UPS shipment request
            return new { /* UPS shipment request structure */ };
        }

        private CarrierBookingResponseDto ParseUpsShipmentResponse(UpsShipmentResponse response, CarrierBookingRequestDto request)
        {
            // Implementation for parsing UPS shipment response
            return new CarrierBookingResponseDto();
        }

        private CarrierTrackingDto ParseUpsTrackingResponse(UpsTrackingResponse response)
        {
            // Implementation for parsing UPS tracking response
            return new CarrierTrackingDto();
        }

        private object BuildUpsAddressValidationRequest(AddressDto address)
        {
            // Implementation for UPS address validation request
            return new { };
        }

        private bool ParseUpsAddressValidationResponse(UpsAddressValidationResponse response)
        {
            // Implementation for parsing UPS address validation response
            return true;
        }

        private string GetUpsServiceName(string serviceCode)
        {
            return serviceCode switch
            {
                "01" => "UPS Next Day Air",
                "02" => "UPS 2nd Day Air",
                "03" => "UPS Ground",
                "12" => "UPS 3 Day Select",
                "13" => "UPS Next Day Air Saver",
                "14" => "UPS Next Day Air Early",
                "59" => "UPS 2nd Day Air A.M.",
                _ => $"UPS Service {serviceCode}"
            };
        }

        private ServiceLevel GetServiceLevel(string serviceCode)
        {
            return serviceCode switch
            {
                "01" or "14" => ServiceLevel.Overnight,
                "13" => ServiceLevel.Overnight,
                "02" or "59" => ServiceLevel.TwoDay,
                "12" => ServiceLevel.ThreeDay,
                "03" => ServiceLevel.Ground,
                _ => ServiceLevel.Standard
            };
        }
    }

    // UPS API Response Models
    public class UpsRateResponse
    {
        public UpsRateResponseData RateResponse { get; set; }
    }

    public class UpsRateResponseData
    {
        public UpsRatedShipment[] RatedShipment { get; set; }
    }

    public class UpsRatedShipment
    {
        public UpsService Service { get; set; }
        public UpsCharges TotalCharges { get; set; }
        public UpsCharges TransportationCharges { get; set; }
        public UpsCharges FuelSurcharge { get; set; }
        public UpsGuaranteedDelivery GuaranteedDelivery { get; set; }
    }

    public class UpsService
    {
        public string Code { get; set; }
    }

    public class UpsCharges
    {
        public string CurrencyCode { get; set; }
        public string MonetaryValue { get; set; }
    }

    public class UpsGuaranteedDelivery
    {
        public string BusinessDaysInTransit { get; set; }
        public string DeliveryByTime { get; set; }
    }

    public class UpsShipmentResponse
    {
        // UPS shipment response structure
    }

    public class UpsTrackingResponse
    {
        // UPS tracking response structure
    }

    public class UpsAddressValidationResponse
    {
        // UPS address validation response structure
    }
}
