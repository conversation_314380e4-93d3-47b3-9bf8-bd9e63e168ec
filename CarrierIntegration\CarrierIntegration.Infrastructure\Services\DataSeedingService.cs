using CarrierIntegration.Application.Common.Interfaces;
using CarrierIntegration.Domain.Entities;
using CarrierIntegration.Domain.Enums;
using CarrierIntegration.Domain.ValueObjects;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Services
{
    public interface IDataSeedingService
    {
        Task SeedDefaultCarriersAsync(Guid organizationId);
        Task SeedCarrierServicesAsync(Guid carrierId);
        Task SeedCarrierAccountsAsync(Guid carrierId, Guid organizationId);
    }

    public class DataSeedingService : IDataSeedingService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DataSeedingService> _logger;

        public DataSeedingService(IUnitOfWork unitOfWork, ILogger<DataSeedingService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task SeedDefaultCarriersAsync(Guid organizationId)
        {
            _logger.LogInformation("Seeding default carriers for organization {OrganizationId}", organizationId);

            var carriers = new List<Carrier>
            {
                CreateUpsCarrier(organizationId),
                CreateFedExCarrier(organizationId),
                CreateUspsCarrier(organizationId),
                CreateDhlCarrier(organizationId),
                CreateXpoCarrier(organizationId),
                CreateOldDominionCarrier(organizationId)
            };

            foreach (var carrier in carriers)
            {
                var existingCarrier = await _unitOfWork.Carriers.GetByCodeAsync(carrier.Code, organizationId);
                if (existingCarrier == null)
                {
                    await _unitOfWork.Carriers.AddAsync(carrier);
                    _logger.LogInformation("Added carrier {CarrierName} ({CarrierCode})", carrier.Name, carrier.Code);
                }
            }

            await _unitOfWork.SaveChangesAsync();
            _logger.LogInformation("Completed seeding default carriers for organization {OrganizationId}", organizationId);
        }

        public async Task SeedCarrierServicesAsync(Guid carrierId)
        {
            var carrier = await _unitOfWork.Carriers.GetByIdAsync(carrierId);
            if (carrier == null)
            {
                _logger.LogWarning("Carrier {CarrierId} not found for service seeding", carrierId);
                return;
            }

            _logger.LogInformation("Seeding services for carrier {CarrierName} ({CarrierId})", carrier.Name, carrierId);

            var services = carrier.Code switch
            {
                "UPS" => CreateUpsServices(carrierId, carrier.OrganizationId),
                "FEDEX" => CreateFedExServices(carrierId, carrier.OrganizationId),
                "USPS" => CreateUspsServices(carrierId, carrier.OrganizationId),
                "DHL" => CreateDhlServices(carrierId, carrier.OrganizationId),
                "XPO" => CreateXpoServices(carrierId, carrier.OrganizationId),
                "ODFL" => CreateOldDominionServices(carrierId, carrier.OrganizationId),
                _ => new List<CarrierService>()
            };

            foreach (var service in services)
            {
                carrier.AddService(service);
            }

            await _unitOfWork.SaveChangesAsync();
            _logger.LogInformation("Completed seeding {ServiceCount} services for carrier {CarrierName}", services.Count, carrier.Name);
        }

        public async Task SeedCarrierAccountsAsync(Guid carrierId, Guid organizationId)
        {
            var carrier = await _unitOfWork.Carriers.GetByIdAsync(carrierId);
            if (carrier == null)
            {
                _logger.LogWarning("Carrier {CarrierId} not found for account seeding", carrierId);
                return;
            }

            _logger.LogInformation("Seeding demo account for carrier {CarrierName} ({CarrierId})", carrier.Name, carrierId);

            var billingContact = new ContactInfo(
                "Demo Account Manager",
                "<EMAIL>",
                "+1-555-0123",
                "Account Manager",
                "Billing");

            var billingAddress = new Address(
                "123 Demo Street",
                "Demo City",
                "Demo State",
                "12345",
                "United States");

            var account = new CarrierAccount(
                carrierId,
                organizationId,
                $"DEMO-{carrier.Code}-001",
                $"Demo {carrier.Name} Account",
                billingContact,
                billingAddress,
                "DataSeeding");

            // Configure demo account settings
            account.UpdateEnvironmentSettings("Sandbox", null, null, true, "DataSeeding");
            account.SetAsDefault("DataSeeding");

            carrier.AddAccount(account);

            await _unitOfWork.SaveChangesAsync();
            _logger.LogInformation("Completed seeding demo account for carrier {CarrierName}", carrier.Name);
        }

        private static Carrier CreateUpsCarrier(Guid organizationId)
        {
            var contact = new ContactInfo(
                "UPS Customer Service",
                "<EMAIL>",
                "******-742-5877",
                "Customer Service Representative",
                "Customer Support");

            var address = new Address(
                "55 Glenlake Parkway NE",
                "Atlanta",
                "Georgia",
                "30328",
                "United States");

            var carrier = new Carrier(
                organizationId,
                "United Parcel Service",
                "UPS",
                CarrierType.NationalCarrier,
                contact,
                address,
                "DataSeeding");

            carrier.UpdateApiConfiguration(
                "https://onlinetools.ups.com/api",
                "v1",
                "America/New_York",
                "Monday-Friday: 8:00 AM - 6:00 PM EST",
                "DataSeeding");

            carrier.UpdateCapabilities(
                true, true, true, true, true, true, true, "DataSeeding");

            carrier.UpdateLimitations(150, "LBS", 108, "IN", "DataSeeding");

            carrier.SetPriority(10, "DataSeeding");

            return carrier;
        }

        private static Carrier CreateFedExCarrier(Guid organizationId)
        {
            var contact = new ContactInfo(
                "FedEx Customer Service",
                "<EMAIL>",
                "******-463-3339",
                "Customer Service Representative",
                "Customer Support");

            var address = new Address(
                "942 South Shady Grove Road",
                "Memphis",
                "Tennessee",
                "38120",
                "United States");

            var carrier = new Carrier(
                organizationId,
                "Federal Express Corporation",
                "FEDEX",
                CarrierType.NationalCarrier,
                contact,
                address,
                "DataSeeding");

            carrier.UpdateApiConfiguration(
                "https://apis.fedex.com",
                "v1",
                "America/Chicago",
                "Monday-Friday: 8:00 AM - 6:00 PM CST",
                "DataSeeding");

            carrier.UpdateCapabilities(
                true, true, true, true, true, true, true, "DataSeeding");

            carrier.UpdateLimitations(150, "LBS", 119, "IN", "DataSeeding");

            carrier.SetPriority(10, "DataSeeding");

            return carrier;
        }

        private static Carrier CreateUspsCarrier(Guid organizationId)
        {
            var contact = new ContactInfo(
                "USPS Customer Service",
                "<EMAIL>",
                "******-275-8777",
                "Customer Service Representative",
                "Customer Support");

            var address = new Address(
                "475 L'Enfant Plaza SW",
                "Washington",
                "District of Columbia",
                "20260",
                "United States");

            var carrier = new Carrier(
                organizationId,
                "United States Postal Service",
                "USPS",
                CarrierType.NationalCarrier,
                contact,
                address,
                "DataSeeding");

            carrier.UpdateApiConfiguration(
                "https://api.usps.com",
                "v3",
                "America/New_York",
                "Monday-Saturday: 8:00 AM - 5:00 PM EST",
                "DataSeeding");

            carrier.UpdateCapabilities(
                true, true, true, true, true, true, true, "DataSeeding");

            carrier.UpdateLimitations(70, "LBS", 108, "IN", "DataSeeding");

            carrier.SetPriority(15, "DataSeeding");

            return carrier;
        }

        private static Carrier CreateDhlCarrier(Guid organizationId)
        {
            var contact = new ContactInfo(
                "DHL Customer Service",
                "<EMAIL>",
                "******-225-5345",
                "Customer Service Representative",
                "Customer Support");

            var address = new Address(
                "1200 South Pine Island Road",
                "Plantation",
                "Florida",
                "33324",
                "United States");

            var carrier = new Carrier(
                organizationId,
                "DHL Express",
                "DHL",
                CarrierType.InternationalCarrier,
                contact,
                address,
                "DataSeeding");

            carrier.UpdateApiConfiguration(
                "https://api-eu.dhl.com",
                "v1",
                "America/New_York",
                "Monday-Friday: 8:00 AM - 6:00 PM EST",
                "DataSeeding");

            carrier.UpdateCapabilities(
                true, true, true, true, true, true, true, "DataSeeding");

            carrier.UpdateLimitations(154, "LBS", 120, "IN", "DataSeeding");

            carrier.SetPriority(20, "DataSeeding");

            return carrier;
        }

        private static Carrier CreateXpoCarrier(Guid organizationId)
        {
            var contact = new ContactInfo(
                "XPO Logistics Customer Service",
                "<EMAIL>",
                "******-976-6951",
                "Customer Service Representative",
                "Customer Support");

            var address = new Address(
                "Five American Lane",
                "Greenwich",
                "Connecticut",
                "06831",
                "United States");

            var carrier = new Carrier(
                organizationId,
                "XPO Logistics",
                "XPO",
                CarrierType.LTLCarrier,
                contact,
                address,
                "DataSeeding");

            carrier.UpdateCapabilities(
                true, true, false, true, false, false, true, "DataSeeding");

            carrier.UpdateLimitations(30000, "LBS", 600, "IN", "DataSeeding");

            carrier.SetPriority(30, "DataSeeding");

            return carrier;
        }

        private static Carrier CreateOldDominionCarrier(Guid organizationId)
        {
            var contact = new ContactInfo(
                "Old Dominion Customer Service",
                "<EMAIL>",
                "******-432-6335",
                "Customer Service Representative",
                "Customer Support");

            var address = new Address(
                "500 Old Dominion Way",
                "Thomasville",
                "North Carolina",
                "27360",
                "United States");

            var carrier = new Carrier(
                organizationId,
                "Old Dominion Freight Line",
                "ODFL",
                CarrierType.LTLCarrier,
                contact,
                address,
                "DataSeeding");

            carrier.UpdateCapabilities(
                true, true, false, true, false, false, true, "DataSeeding");

            carrier.UpdateLimitations(40000, "LBS", 600, "IN", "DataSeeding");

            carrier.SetPriority(25, "DataSeeding");

            return carrier;
        }

        private static List<CarrierService> CreateUpsServices(Guid carrierId, Guid organizationId)
        {
            return new List<CarrierService>
            {
                new CarrierService(carrierId, organizationId, "UPS_GROUND", "UPS Ground", ServiceLevel.Ground, 5, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "UPS_3DAY", "UPS 3 Day Select", ServiceLevel.ThreeDay, 3, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "UPS_2DAY", "UPS 2nd Day Air", ServiceLevel.TwoDay, 2, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "UPS_NEXT_DAY", "UPS Next Day Air", ServiceLevel.NextDay, 1, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "UPS_NEXT_DAY_SAVER", "UPS Next Day Air Saver", ServiceLevel.NextDay, 1, "DataSeeding")
            };
        }

        private static List<CarrierService> CreateFedExServices(Guid carrierId, Guid organizationId)
        {
            return new List<CarrierService>
            {
                new CarrierService(carrierId, organizationId, "FEDEX_GROUND", "FedEx Ground", ServiceLevel.Ground, 5, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "FEDEX_EXPRESS_SAVER", "FedEx Express Saver", ServiceLevel.ThreeDay, 3, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "FEDEX_2DAY", "FedEx 2Day", ServiceLevel.TwoDay, 2, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "FEDEX_OVERNIGHT", "FedEx Standard Overnight", ServiceLevel.NextDay, 1, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "FEDEX_PRIORITY", "FedEx Priority Overnight", ServiceLevel.NextDay, 1, "DataSeeding")
            };
        }

        private static List<CarrierService> CreateUspsServices(Guid carrierId, Guid organizationId)
        {
            return new List<CarrierService>
            {
                new CarrierService(carrierId, organizationId, "USPS_GROUND", "USPS Ground Advantage", ServiceLevel.Ground, 5, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "USPS_PRIORITY", "USPS Priority Mail", ServiceLevel.Priority, 3, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "USPS_EXPRESS", "USPS Priority Mail Express", ServiceLevel.NextDay, 1, "DataSeeding")
            };
        }

        private static List<CarrierService> CreateDhlServices(Guid carrierId, Guid organizationId)
        {
            return new List<CarrierService>
            {
                new CarrierService(carrierId, organizationId, "DHL_EXPRESS", "DHL Express Worldwide", ServiceLevel.Express, 3, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "DHL_EXPRESS_12", "DHL Express 12:00", ServiceLevel.Express, 2, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "DHL_EXPRESS_10", "DHL Express 10:30", ServiceLevel.Express, 1, "DataSeeding")
            };
        }

        private static List<CarrierService> CreateXpoServices(Guid carrierId, Guid organizationId)
        {
            return new List<CarrierService>
            {
                new CarrierService(carrierId, organizationId, "XPO_LTL", "XPO LTL Standard", ServiceLevel.Standard, 5, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "XPO_EXPEDITED", "XPO Expedited", ServiceLevel.Expedited, 3, "DataSeeding")
            };
        }

        private static List<CarrierService> CreateOldDominionServices(Guid carrierId, Guid organizationId)
        {
            return new List<CarrierService>
            {
                new CarrierService(carrierId, organizationId, "ODFL_LTL", "Old Dominion LTL", ServiceLevel.Standard, 5, "DataSeeding"),
                new CarrierService(carrierId, organizationId, "ODFL_EXPEDITED", "Old Dominion Expedited", ServiceLevel.Expedited, 3, "DataSeeding")
            };
        }
    }
}
