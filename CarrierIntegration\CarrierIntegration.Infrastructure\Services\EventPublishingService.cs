using CarrierIntegration.Domain.Common;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RabbitMQ.Client;
using System;
using System.Text;
using System.Threading.Tasks;

namespace CarrierIntegration.Infrastructure.Services
{
    public interface IEventPublishingService
    {
        Task PublishDomainEventAsync<T>(T domainEvent) where T : IDomainEvent;
        Task PublishIntegrationEventAsync<T>(T integrationEvent) where T : class;
        Task PublishCarrierEventAsync(string eventType, object eventData, Guid carrierId, Guid organizationId);
    }

    public class EventPublishingService : IEventPublishingService
    {
        private readonly IConnection _connection;
        private readonly IModel _channel;
        private readonly ILogger<EventPublishingService> _logger;
        private readonly JsonSerializerSettings _jsonSettings;

        // Exchange and queue names
        private const string DOMAIN_EVENTS_EXCHANGE = "carrier.domain.events";
        private const string INTEGRATION_EVENTS_EXCHANGE = "carrier.integration.events";
        private const string CARRIER_EVENTS_EXCHANGE = "carrier.events";

        public EventPublishingService(
            IConnection connection,
            ILogger<EventPublishingService> logger)
        {
            _connection = connection;
            _logger = logger;
            _channel = _connection.CreateModel();

            _jsonSettings = new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Auto,
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                NullValueHandling = NullValueHandling.Ignore
            };

            SetupExchangesAndQueues();
        }

        public async Task PublishDomainEventAsync<T>(T domainEvent) where T : IDomainEvent
        {
            try
            {
                var eventType = typeof(T).Name;
                var eventData = JsonConvert.SerializeObject(domainEvent, _jsonSettings);
                var body = Encoding.UTF8.GetBytes(eventData);

                var properties = _channel.CreateBasicProperties();
                properties.Persistent = true;
                properties.MessageId = Guid.NewGuid().ToString();
                properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());
                properties.Type = eventType;
                properties.Headers = new Dictionary<string, object>
                {
                    ["EventType"] = eventType,
                    ["Source"] = "CarrierIntegration",
                    ["Version"] = "1.0"
                };

                _channel.BasicPublish(
                    exchange: DOMAIN_EVENTS_EXCHANGE,
                    routingKey: $"domain.{eventType.ToLowerInvariant()}",
                    basicProperties: properties,
                    body: body);

                _logger.LogInformation("Published domain event {EventType} with ID {EventId}",
                    eventType, properties.MessageId);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing domain event {EventType}", typeof(T).Name);
                throw;
            }
        }

        public async Task PublishIntegrationEventAsync<T>(T integrationEvent) where T : class
        {
            try
            {
                var eventType = typeof(T).Name;
                var eventData = JsonConvert.SerializeObject(integrationEvent, _jsonSettings);
                var body = Encoding.UTF8.GetBytes(eventData);

                var properties = _channel.CreateBasicProperties();
                properties.Persistent = true;
                properties.MessageId = Guid.NewGuid().ToString();
                properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());
                properties.Type = eventType;
                properties.Headers = new Dictionary<string, object>
                {
                    ["EventType"] = eventType,
                    ["Source"] = "CarrierIntegration",
                    ["Version"] = "1.0"
                };

                _channel.BasicPublish(
                    exchange: INTEGRATION_EVENTS_EXCHANGE,
                    routingKey: $"integration.{eventType.ToLowerInvariant()}",
                    basicProperties: properties,
                    body: body);

                _logger.LogInformation("Published integration event {EventType} with ID {EventId}",
                    eventType, properties.MessageId);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing integration event {EventType}", typeof(T).Name);
                throw;
            }
        }

        public async Task PublishCarrierEventAsync(string eventType, object eventData, Guid carrierId, Guid organizationId)
        {
            try
            {
                var eventPayload = new
                {
                    EventType = eventType,
                    CarrierId = carrierId,
                    OrganizationId = organizationId,
                    Data = eventData,
                    Timestamp = DateTime.UtcNow
                };

                var serializedData = JsonConvert.SerializeObject(eventPayload, _jsonSettings);
                var body = Encoding.UTF8.GetBytes(serializedData);

                var properties = _channel.CreateBasicProperties();
                properties.Persistent = true;
                properties.MessageId = Guid.NewGuid().ToString();
                properties.Timestamp = new AmqpTimestamp(DateTimeOffset.UtcNow.ToUnixTimeSeconds());
                properties.Type = eventType;
                properties.Headers = new Dictionary<string, object>
                {
                    ["EventType"] = eventType,
                    ["CarrierId"] = carrierId.ToString(),
                    ["OrganizationId"] = organizationId.ToString(),
                    ["Source"] = "CarrierIntegration",
                    ["Version"] = "1.0"
                };

                _channel.BasicPublish(
                    exchange: CARRIER_EVENTS_EXCHANGE,
                    routingKey: $"carrier.{eventType.ToLowerInvariant()}",
                    basicProperties: properties,
                    body: body);

                _logger.LogInformation("Published carrier event {EventType} for carrier {CarrierId} with ID {EventId}",
                    eventType, carrierId, properties.MessageId);

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing carrier event {EventType} for carrier {CarrierId}",
                    eventType, carrierId);
                throw;
            }
        }

        private void SetupExchangesAndQueues()
        {
            try
            {
                // Declare exchanges
                _channel.ExchangeDeclare(
                    exchange: DOMAIN_EVENTS_EXCHANGE,
                    type: ExchangeType.Topic,
                    durable: true,
                    autoDelete: false);

                _channel.ExchangeDeclare(
                    exchange: INTEGRATION_EVENTS_EXCHANGE,
                    type: ExchangeType.Topic,
                    durable: true,
                    autoDelete: false);

                _channel.ExchangeDeclare(
                    exchange: CARRIER_EVENTS_EXCHANGE,
                    type: ExchangeType.Topic,
                    durable: true,
                    autoDelete: false);

                // Declare dead letter exchange
                _channel.ExchangeDeclare(
                    exchange: "carrier.deadletter",
                    type: ExchangeType.Direct,
                    durable: true,
                    autoDelete: false);

                // Declare dead letter queue
                _channel.QueueDeclare(
                    queue: "carrier.deadletter.queue",
                    durable: true,
                    exclusive: false,
                    autoDelete: false,
                    arguments: null);

                _channel.QueueBind(
                    queue: "carrier.deadletter.queue",
                    exchange: "carrier.deadletter",
                    routingKey: "deadletter");

                _logger.LogInformation("Successfully set up RabbitMQ exchanges and queues");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting up RabbitMQ exchanges and queues");
                throw;
            }
        }

        public void Dispose()
        {
            try
            {
                _channel?.Close();
                _channel?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error disposing EventPublishingService");
            }
        }
    }

    // Integration event models
    public class CarrierCreatedIntegrationEvent
    {
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
        public string CarrierName { get; set; } = null!;
        public string CarrierCode { get; set; } = null!;
        public string CarrierType { get; set; } = null!;
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = null!;
    }

    public class CarrierActivatedIntegrationEvent
    {
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
        public string CarrierName { get; set; } = null!;
        public DateTime ActivatedAt { get; set; }
        public string ActivatedBy { get; set; } = null!;
    }

    public class CarrierDeactivatedIntegrationEvent
    {
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
        public string CarrierName { get; set; } = null!;
        public string DeactivationReason { get; set; } = null!;
        public DateTime DeactivatedAt { get; set; }
        public string DeactivatedBy { get; set; } = null!;
    }

    public class CarrierBookingCreatedIntegrationEvent
    {
        public Guid BookingId { get; set; }
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
        public string BookingReference { get; set; } = null!;
        public Guid? OrderId { get; set; }
        public Guid? ShipmentId { get; set; }
        public DateTime RequestedPickupDate { get; set; }
        public DateTime? RequestedDeliveryDate { get; set; }
        public string OriginCity { get; set; } = null!;
        public string DestinationCity { get; set; } = null!;
        public decimal TotalWeight { get; set; }
        public string WeightUnit { get; set; } = null!;
        public int PackageCount { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = null!;
    }

    public class CarrierBookingConfirmedIntegrationEvent
    {
        public Guid BookingId { get; set; }
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
        public string BookingReference { get; set; } = null!;
        public string? CarrierBookingReference { get; set; }
        public string? TrackingNumber { get; set; }
        public DateTime? ConfirmedPickupDate { get; set; }
        public DateTime? ConfirmedDeliveryDate { get; set; }
        public decimal? EstimatedCostAmount { get; set; }
        public string? EstimatedCostCurrency { get; set; }
        public DateTime ConfirmedAt { get; set; }
        public string ConfirmedBy { get; set; } = null!;
    }

    public class CarrierBookingCancelledIntegrationEvent
    {
        public Guid BookingId { get; set; }
        public Guid CarrierId { get; set; }
        public Guid OrganizationId { get; set; }
        public string BookingReference { get; set; } = null!;
        public string CancellationReason { get; set; } = null!;
        public DateTime CancelledAt { get; set; }
        public string CancelledBy { get; set; } = null!;
    }
}
