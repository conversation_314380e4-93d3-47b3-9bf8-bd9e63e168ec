# TriTrackz Microservices Workflow Documentation

## Overview

This document provides comprehensive workflow charts and documentation for the TriTrackz microservices architecture. The system consists of 9 core microservices that work together to provide a complete logistics and shipping management platform.

## Architecture Summary

### Core Services

| Service | Port | Database | Primary Function |
|---------|------|----------|------------------|
| **API Gateway** | 7000 | - | Request routing and authentication |
| **Identity Service** | 7001 | TriTrackzIdentity | Authentication & authorization |
| **User Management** | 7002 | TriTrackzUserManagement | Users & organizations |
| **Master Management** | 7003 | TriTrackzMasterManagement | Geographic & reference data |
| **Customer Management** | 7004 | TriTrackzCustomerManagement | Customer profiles & accounts |
| **Shipper Management** | 7006 | TriTrackzShipperManagement | Shipper profiles & contracts |
| **Order Management** | 7007 | TriTrackzOrderManagement | Order lifecycle management |
| **Shipment Management** | 7008 | TriTrackzShipmentManagement | Shipment tracking & status |
| **Pricing Management** | 7009 | TriTrackzPricingManagement | Rates & pricing calculations |
| **Carrier Integration** | 7010 | TriTrackzCarrierIntegration | External carrier APIs |

### Infrastructure Components

| Component | Port | Purpose |
|-----------|------|---------|
| **PostgreSQL** | 5432 | Primary database (user: timescale) |
| **Redis** | 6379 | Caching layer |
| **RabbitMQ** | 5672 | Message broker for event-driven communication |

## Communication Patterns

### 1. Synchronous Communication (REST APIs)
- **Client → API Gateway → Services**: All external requests go through the API Gateway
- **Service-to-Service**: Direct HTTP calls for immediate data needs
- **Authentication**: JWT tokens validated through Identity Service

### 2. Asynchronous Communication (Event-Driven)
- **RabbitMQ**: Message broker for publishing and consuming domain events
- **Event Types**: User events, organization events, customer events, order events, shipment events
- **Decoupling**: Services can react to events without direct dependencies

### 3. Data Access Patterns
- **Database per Service**: Each microservice has its own PostgreSQL database
- **Caching**: Redis used for performance optimization
- **Multi-tenancy**: Organization-scoped data isolation

## Service Dependencies

### Authentication Flow
1. **Identity Service** → Core authentication and JWT token generation
2. **All Services** → Validate JWT tokens with Identity Service
3. **User Management** → Provides organization context for multi-tenancy

### Data Dependencies
1. **Master Management** → Provides geographic and reference data to all services
2. **User Management** → Provides organization context for data scoping
3. **Customer/Shipper Management** → Provides entity data for orders and shipments
4. **Order Management** → Central hub for order processing
5. **Shipment Management** → Tracks order fulfillment
6. **Pricing Management** → Calculates rates for orders and shipments
7. **Carrier Integration** → Interfaces with external carrier APIs

## Business Process Workflows

### 1. User Onboarding
```
Client → API Gateway → Identity Service (Authentication)
                   → User Management (Organization Setup)
                   → Master Management (Geographic Data Sync)
```

### 2. Customer/Shipper Onboarding
```
Client → API Gateway → Customer/Shipper Management
                   → Master Management (Address Validation)
                   → User Management (Organization Context)
                   → RabbitMQ (Event Publishing)
```

### 3. Order Processing
```
Client → API Gateway → Order Management
                   → Customer Management (Customer Details)
                   → Shipper Management (Shipper Details)
                   → Master Management (Address Validation)
                   → Pricing Management (Rate Calculation)
                   → Carrier Integration (Carrier Rates)
                   → RabbitMQ (Order Events)
```

### 4. Shipment Lifecycle
```
Order Created Event → Shipment Management
                  → Order Management (Order Details)
                  → Carrier Integration (Booking)
                  → RabbitMQ (Shipment Events)
                  → Real-time Tracking Updates
```

## External Integrations

### Carrier APIs
- **UPS API**: Shipping rates, booking, tracking
- **FedEx API**: Shipping rates, booking, tracking
- **USPS API**: Shipping rates, booking, tracking
- **DHL API**: International shipping, tracking

### Third-Party Services
- **Google Maps API**: Address validation, geocoding
- **Weather APIs**: Route optimization
- **Currency APIs**: Multi-currency support
- **Tax APIs**: Tax calculation

## Event-Driven Architecture

### Event Types
1. **User Events**: UserCreated, UserUpdated, UserDeleted
2. **Organization Events**: OrganizationCreated, OrganizationUpdated
3. **Customer Events**: CustomerCreated, CustomerUpdated, CustomerStatusChanged
4. **Shipper Events**: ShipperCreated, ShipperUpdated, ContractUpdated
5. **Order Events**: OrderCreated, OrderUpdated, OrderCancelled
6. **Shipment Events**: ShipmentCreated, TrackingUpdated, DeliveryCompleted
7. **Pricing Events**: RateCalculated, ContractPriceUpdated

### Event Consumers
- Services subscribe to relevant events through RabbitMQ
- Enables loose coupling and scalability
- Supports eventual consistency across services

## Security & Authentication

### JWT Token Flow
1. Client authenticates with Identity Service
2. Identity Service issues JWT token
3. API Gateway validates token for all requests
4. Services validate token for service-to-service calls

### Multi-Tenancy
- Organization-scoped data access
- Automatic data isolation
- Role-based permissions within organizations

## Performance Considerations

### Caching Strategy
- Redis caching for frequently accessed data
- Geographic data caching in Master Management
- User session caching
- Rate calculation caching

### Database Optimization
- PostgreSQL with PostGIS for geographic queries
- Proper indexing for multi-tenant queries
- Connection pooling
- Read replicas for reporting (future)

## Monitoring & Observability

### Health Checks
- Each service exposes `/health` endpoint
- API Gateway health monitoring
- Database connectivity checks
- External service availability checks

### Logging
- Structured logging across all services
- Correlation IDs for request tracing
- Application Insights integration
- Centralized log aggregation

## Deployment Architecture

### Development Environment
- Local development with Docker Compose
- Shared PostgreSQL instance with multiple databases
- Local Redis and RabbitMQ instances
- Service discovery through localhost ports

### Production Considerations
- Container orchestration (Kubernetes/Docker Swarm)
- Load balancing for high availability
- Database clustering and replication
- Message broker clustering
- API Gateway clustering

## Future Enhancements

### Planned Services
1. **Billing Service**: Invoice generation and payment processing
2. **Warehouse Management**: Inventory and fulfillment
3. **Analytics Service**: Business intelligence and reporting
4. **Notification Service**: Email, SMS, and push notifications
5. **Document Management**: Shipping labels, customs documents

### Scalability Improvements
1. **CQRS Implementation**: Separate read/write models
2. **Event Sourcing**: Complete audit trail
3. **Microservice Mesh**: Service-to-service communication
4. **Auto-scaling**: Dynamic resource allocation
5. **Multi-region Deployment**: Global availability

## Troubleshooting Guide

### Common Issues
1. **Service Discovery**: Ensure all services are running on correct ports
2. **Database Connectivity**: Verify PostgreSQL connection strings
3. **Authentication**: Check JWT token validation
4. **Message Broker**: Verify RabbitMQ connectivity
5. **External APIs**: Monitor carrier API availability

### Debugging Tools
1. **Swagger UI**: Available on each service for API testing
2. **RabbitMQ Management**: Web interface for message monitoring
3. **Redis CLI**: Cache inspection and debugging
4. **PostgreSQL Admin**: Database query and monitoring tools

This documentation provides a comprehensive overview of the TriTrackz microservices workflow and architecture. The system is designed for scalability, maintainability, and high availability in a logistics and shipping management context.
