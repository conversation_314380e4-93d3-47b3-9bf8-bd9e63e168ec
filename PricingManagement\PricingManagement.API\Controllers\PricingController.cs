using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Services;

namespace PricingManagement.API.Controllers
{
    /// <summary>
    /// Core pricing and rate calculation endpoints
    /// </summary>
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [Authorize]
    public class PricingController : ControllerBase
    {
        private readonly IPricingEngine _pricingEngine;
        private readonly ILogger<PricingController> _logger;

        public PricingController(IPricingEngine pricingEngine, ILogger<PricingController> logger)
        {
            _pricingEngine = pricingEngine;
            _logger = logger;
        }

        /// <summary>
        /// Calculate shipping rate for given parameters
        /// </summary>
        /// <param name="request">Rate calculation request</param>
        /// <returns>Calculated pricing result</returns>
        [HttpPost("calculate")]
        [ProducesResponseType(typeof(PricingResultDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PricingResultDto>> CalculateRate([FromBody] RateCalculationRequestDto request)
        {
            try
            {
                var result = await _pricingEngine.CalculateRateAsync(request);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid rate calculation request");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating rate");
                return StatusCode(500, new { message = "An error occurred while calculating the rate" });
            }
        }

        /// <summary>
        /// Compare rates across multiple carriers
        /// </summary>
        /// <param name="request">Rate calculation request</param>
        /// <returns>Carrier rate comparison results</returns>
        [HttpPost("compare")]
        [ProducesResponseType(typeof(IEnumerable<CarrierRateComparisonDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<IEnumerable<CarrierRateComparisonDto>>> CompareCarrierRates([FromBody] RateCalculationRequestDto request)
        {
            try
            {
                var result = await _pricingEngine.CompareCarrierRatesAsync(request);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid carrier rate comparison request");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error comparing carrier rates");
                return StatusCode(500, new { message = "An error occurred while comparing carrier rates" });
            }
        }

        /// <summary>
        /// Apply contract-based pricing to a rate calculation
        /// </summary>
        /// <param name="request">Rate calculation request</param>
        /// <param name="contractId">Contract ID to apply</param>
        /// <returns>Contract-based pricing result</returns>
        [HttpPost("contract/{contractId:guid}")]
        [ProducesResponseType(typeof(PricingResultDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PricingResultDto>> ApplyContractPricing([FromBody] RateCalculationRequestDto request, Guid contractId)
        {
            try
            {
                var result = await _pricingEngine.ApplyContractPricingAsync(request, contractId);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid contract pricing request for contract {ContractId}", contractId);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying contract pricing for contract {ContractId}", contractId);
                return StatusCode(500, new { message = "An error occurred while applying contract pricing" });
            }
        }

        /// <summary>
        /// Apply promotional pricing to a rate calculation
        /// </summary>
        /// <param name="request">Rate calculation request</param>
        /// <param name="promotionCode">Promotion code to apply</param>
        /// <returns>Promotional pricing result</returns>
        [HttpPost("promotion/{promotionCode}")]
        [ProducesResponseType(typeof(PricingResultDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PricingResultDto>> ApplyPromotionalPricing([FromBody] RateCalculationRequestDto request, string promotionCode)
        {
            try
            {
                var result = await _pricingEngine.ApplyPromotionalPricingAsync(request, promotionCode);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid promotional pricing request for code {PromotionCode}", promotionCode);
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying promotional pricing for code {PromotionCode}", promotionCode);
                return StatusCode(500, new { message = "An error occurred while applying promotional pricing" });
            }
        }

        /// <summary>
        /// Simulate pricing scenarios for analysis
        /// </summary>
        /// <param name="request">Pricing simulation request</param>
        /// <returns>Simulation results with analysis</returns>
        [HttpPost("simulate")]
        [ProducesResponseType(typeof(PricingSimulationResultDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PricingSimulationResultDto>> SimulatePricing([FromBody] PricingSimulationRequestDto request)
        {
            try
            {
                var result = await _pricingEngine.SimulatePricingAsync(request);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid pricing simulation request");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error simulating pricing");
                return StatusCode(500, new { message = "An error occurred while simulating pricing" });
            }
        }

        /// <summary>
        /// Get applicable pricing rules for a rate calculation
        /// </summary>
        /// <param name="request">Rate calculation request</param>
        /// <returns>List of applicable pricing rules</returns>
        [HttpPost("rules/applicable")]
        [ProducesResponseType(typeof(IEnumerable<PricingRuleDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<IEnumerable<PricingRuleDto>>> GetApplicableRules([FromBody] RateCalculationRequestDto request)
        {
            try
            {
                var result = await _pricingEngine.GetApplicableRulesAsync(request);
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                _logger.LogWarning(ex, "Invalid applicable rules request");
                return BadRequest(new { message = ex.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting applicable rules");
                return StatusCode(500, new { message = "An error occurred while getting applicable rules" });
            }
        }

        /// <summary>
        /// Validate a rate calculation request
        /// </summary>
        /// <param name="request">Rate calculation request to validate</param>
        /// <returns>Validation result</returns>
        [HttpPost("validate")]
        [ProducesResponseType(typeof(ValidationResultDto), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<ValidationResultDto>> ValidateRateCalculation([FromBody] RateCalculationRequestDto request)
        {
            try
            {
                var isValid = await _pricingEngine.ValidateRateCalculationAsync(request);
                var result = new ValidationResultDto
                {
                    IsValid = isValid,
                    Errors = isValid ? new List<string>() : new List<string> { "Rate calculation request is invalid" }
                };
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating rate calculation");
                return StatusCode(500, new { message = "An error occurred while validating the rate calculation" });
            }
        }
    }
}
