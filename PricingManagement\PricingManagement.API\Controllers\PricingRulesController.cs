using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.PricingRules.Commands.CreatePricingRule;
using PricingManagement.Application.PricingRules.Queries.GetPricingRuleById;
using PricingManagement.Application.PricingRules.Queries.GetPricingRules;

namespace PricingManagement.API.Controllers
{
    /// <summary>
    /// Pricing Rules management endpoints
    /// </summary>
    [ApiController]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiVersion("1.0")]
    [Authorize]
    public class PricingRulesController : ControllerBase
    {
        private readonly IMediator _mediator;
        private readonly ILogger<PricingRulesController> _logger;

        public PricingRulesController(IMediator mediator, ILogger<PricingRulesController> logger)
        {
            _mediator = mediator;
            _logger = logger;
        }

        /// <summary>
        /// Get all pricing rules with optional filtering and pagination
        /// </summary>
        /// <param name="query">Query parameters for filtering and pagination</param>
        /// <returns>Paginated list of pricing rules</returns>
        [HttpGet]
        [ProducesResponseType(typeof(PagedResultDto<PricingRuleDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PagedResultDto<PricingRuleDto>>> GetPricingRules([FromQuery] GetPricingRulesQuery query)
        {
            var result = await _mediator.Send(query);
            return Ok(result);
        }

        /// <summary>
        /// Get a specific pricing rule by ID
        /// </summary>
        /// <param name="id">Pricing rule ID</param>
        /// <returns>Pricing rule details</returns>
        [HttpGet("{id:guid}")]
        [ProducesResponseType(typeof(OperationResultDto<PricingRuleDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<PricingRuleDto>>> GetPricingRule(Guid id)
        {
            var result = await _mediator.Send(new GetPricingRuleByIdQuery(id));
            
            if (!result.IsSuccess)
            {
                return NotFound(result);
            }

            return Ok(result);
        }

        /// <summary>
        /// Create a new pricing rule
        /// </summary>
        /// <param name="command">Pricing rule creation data</param>
        /// <returns>Created pricing rule</returns>
        [HttpPost]
        [ProducesResponseType(typeof(OperationResultDto<PricingRuleDto>), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<PricingRuleDto>>> CreatePricingRule([FromBody] CreatePricingRuleCommand command)
        {
            var result = await _mediator.Send(command);
            
            if (!result.IsSuccess)
            {
                return BadRequest(result);
            }

            return CreatedAtAction(
                nameof(GetPricingRule),
                new { id = result.Data!.Id },
                result);
        }

        /// <summary>
        /// Update an existing pricing rule
        /// </summary>
        /// <param name="id">Pricing rule ID</param>
        /// <param name="command">Updated pricing rule data</param>
        /// <returns>Updated pricing rule</returns>
        [HttpPut("{id:guid}")]
        [ProducesResponseType(typeof(OperationResultDto<PricingRuleDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<PricingRuleDto>>> UpdatePricingRule(Guid id, [FromBody] UpdatePricingRuleDto command)
        {
            // TODO: Implement UpdatePricingRuleCommand
            return BadRequest("Update functionality not yet implemented");
        }

        /// <summary>
        /// Activate a pricing rule
        /// </summary>
        /// <param name="id">Pricing rule ID</param>
        /// <returns>Operation result</returns>
        [HttpPost("{id:guid}/activate")]
        [ProducesResponseType(typeof(OperationResultDto<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<bool>>> ActivatePricingRule(Guid id)
        {
            // TODO: Implement ActivatePricingRuleCommand
            return BadRequest("Activate functionality not yet implemented");
        }

        /// <summary>
        /// Deactivate a pricing rule
        /// </summary>
        /// <param name="id">Pricing rule ID</param>
        /// <returns>Operation result</returns>
        [HttpPost("{id:guid}/deactivate")]
        [ProducesResponseType(typeof(OperationResultDto<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<bool>>> DeactivatePricingRule(Guid id)
        {
            // TODO: Implement DeactivatePricingRuleCommand
            return BadRequest("Deactivate functionality not yet implemented");
        }

        /// <summary>
        /// Delete a pricing rule (soft delete)
        /// </summary>
        /// <param name="id">Pricing rule ID</param>
        /// <returns>Operation result</returns>
        [HttpDelete("{id:guid}")]
        [ProducesResponseType(typeof(OperationResultDto<bool>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<OperationResultDto<bool>>> DeletePricingRule(Guid id)
        {
            // TODO: Implement DeletePricingRuleCommand
            return BadRequest("Delete functionality not yet implemented");
        }

        /// <summary>
        /// Get applicable pricing rules for given criteria
        /// </summary>
        /// <param name="serviceType">Service type</param>
        /// <param name="originZone">Origin zone ID</param>
        /// <param name="destinationZone">Destination zone ID</param>
        /// <param name="weight">Package weight</param>
        /// <param name="distance">Distance</param>
        /// <param name="value">Declared value</param>
        /// <param name="customerSegment">Customer segment</param>
        /// <param name="shipperType">Shipper type</param>
        /// <returns>List of applicable pricing rules</returns>
        [HttpGet("applicable")]
        [ProducesResponseType(typeof(List<PricingRuleDto>), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<List<PricingRuleDto>>> GetApplicableRules(
            [FromQuery] string? serviceType = null,
            [FromQuery] string? originZone = null,
            [FromQuery] string? destinationZone = null,
            [FromQuery] decimal? weight = null,
            [FromQuery] decimal? distance = null,
            [FromQuery] decimal? value = null,
            [FromQuery] string? customerSegment = null,
            [FromQuery] string? shipperType = null)
        {
            // TODO: Implement GetApplicablePricingRulesQuery
            return BadRequest("Applicable rules functionality not yet implemented");
        }
    }
}
