{"ConnectionStrings": {"DefaultConnection": "Host=localhost;Database=TriTrackzPricingManagement;Username=timescale;Password=timescale", "Redis": "localhost:6379"}, "IdentityApi": {"BaseUrl": "https://localhost:7001/api/v1"}, "UserManagementApi": {"BaseUrl": "https://localhost:7002/api/v1"}, "MasterManagementApi": {"BaseUrl": "https://localhost:7003/api/v1"}, "CustomerManagementApi": {"BaseUrl": "https://localhost:7004/api/v1"}, "ShipperManagementApi": {"BaseUrl": "https://localhost:7006/api/v1"}, "OrderManagementApi": {"BaseUrl": "https://localhost:7007/api/v1"}, "ShipmentManagementApi": {"BaseUrl": "https://localhost:7008/api/v1"}, "ApiGateway": {"Url": "https://localhost:7000"}, "JwtSettings": {"Secret": "ThisIsAVerySecureKeyThatShouldBeStoredInASecureVault", "Issuer": "TriTrackzIdentity", "Audience": "TriTrackzServices"}, "RabbitMQ": {"Host": "localhost"}, "ExternalServices": {"CurrencyApiUrl": "https://api.exchangerate-api.com/v4/latest/", "CurrencyApiKey": "your-currency-api-key", "TaxApiUrl": "https://api.taxjar.com/v2/", "TaxApiKey": "your-tax-api-key", "FuelSurchargeApiUrl": "https://api.fuelsurcharge.com/v1/", "FuelSurchargeApiKey": "your-fuel-surcharge-api-key"}, "FeatureFlags": {"EnableCaching": true, "EnableRealTimePricing": true, "EnableCarrierIntegration": false, "EnableAdvancedAnalytics": true, "EnablePricingSimulation": true}, "PricingSettings": {"DefaultCurrency": "USD", "MaxQuoteValidityDays": 30, "DefaultDimensionalWeightDivisor": 139, "EnableAutomaticRuleExpiration": true, "RequireApprovalThreshold": 1000.0, "MaxDiscountPercentage": 50.0, "CacheExpirationMinutes": 60}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Microsoft.EntityFrameworkCore": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "logs/pricing-management-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 7, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "AllowedHosts": "*"}