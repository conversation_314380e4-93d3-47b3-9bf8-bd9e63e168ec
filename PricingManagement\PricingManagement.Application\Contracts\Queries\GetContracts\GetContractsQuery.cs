using MediatR;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Enums;
using System;

namespace PricingManagement.Application.Contracts.Queries.GetContracts
{
    public class GetContractsQuery : IRequest<PagedResultDto<ContractDto>>
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; }
        public ContractStatus? Status { get; set; }
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public DateTime? ExpiringBefore { get; set; }
        public bool ActiveOnly { get; set; }
        public bool ExpiringOnly { get; set; }
        public bool ReviewRequired { get; set; }
    }
}
