using PricingManagement.Domain.Enums;
using System;
using System.Collections.Generic;

namespace PricingManagement.Application.DTOs
{
    public class ContractDto
    {
        public Guid Id { get; set; }
        public string ContractNumber { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public string? CustomerName { get; set; }
        public string? ShipperName { get; set; }
        public ContractStatus Status { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime ExpirationDate { get; set; }
        public CurrencyCode Currency { get; set; }
        public string? Terms { get; set; }
        public string? PaymentTerms { get; set; }
        public decimal? MinimumCommitment { get; set; }
        public decimal? MaximumCommitment { get; set; }
        public string? CommitmentType { get; set; }
        public decimal? CommitmentPeriod { get; set; }
        public bool AutoRenewal { get; set; }
        public int? AutoRenewalPeriod { get; set; }
        public string? NotificationPeriod { get; set; }
        public List<string> ServiceTypes { get; set; } = new();
        public Dictionary<string, object> GeographicScope { get; set; } = new();
        public string? SpecialProvisions { get; set; }
        public string? ContractDocument { get; set; }
        public DateTime? LastReviewDate { get; set; }
        public DateTime? NextReviewDate { get; set; }
        public string? ReviewNotes { get; set; }
        public int Version { get; set; }
        public Guid? ParentContractId { get; set; }
        public List<ContractRateDto> Rates { get; set; } = new();
        public List<ContractDiscountDto> Discounts { get; set; } = new();
        public List<ContractSurchargeDto> Surcharges { get; set; } = new();
        public List<ContractCommitmentDto> Commitments { get; set; } = new();
        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public string? UpdatedBy { get; set; }
    }

    public class ContractRateDto
    {
        public string ServiceType { get; set; } = string.Empty;
        public string? OriginZone { get; set; }
        public string? DestinationZone { get; set; }
        public decimal? MinWeight { get; set; }
        public decimal? MaxWeight { get; set; }
        public MoneyDto Rate { get; set; } = new();
        public string? RateType { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
    }

    public class ContractDiscountDto
    {
        public DiscountType DiscountType { get; set; }
        public string Description { get; set; } = string.Empty;
        public decimal? Percentage { get; set; }
        public MoneyDto? FixedAmount { get; set; }
        public decimal? MinVolume { get; set; }
        public decimal? MaxVolume { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
    }

    public class ContractSurchargeDto
    {
        public SurchargeType SurchargeType { get; set; }
        public string Description { get; set; } = string.Empty;
        public MoneyDto Amount { get; set; } = new();
        public string? CalculationBasis { get; set; }
        public DateTime? EffectiveDate { get; set; }
        public DateTime? ExpirationDate { get; set; }
    }

    public class ContractCommitmentDto
    {
        public string CommitmentType { get; set; } = string.Empty;
        public decimal TargetValue { get; set; }
        public decimal? MinimumValue { get; set; }
        public decimal? MaximumValue { get; set; }
        public string Period { get; set; } = string.Empty;
    }

    public class CreateContractDto
    {
        public string ContractNumber { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public string? CustomerName { get; set; }
        public string? ShipperName { get; set; }
        public DateTime EffectiveDate { get; set; }
        public DateTime ExpirationDate { get; set; }
        public CurrencyCode Currency { get; set; } = CurrencyCode.USD;
        public string? Terms { get; set; }
        public string? PaymentTerms { get; set; }
        public decimal? MinimumCommitment { get; set; }
        public decimal? MaximumCommitment { get; set; }
        public string? CommitmentType { get; set; }
        public decimal? CommitmentPeriod { get; set; }
        public bool AutoRenewal { get; set; }
        public int? AutoRenewalPeriod { get; set; }
        public string? NotificationPeriod { get; set; }
        public List<string> ServiceTypes { get; set; } = new();
        public Dictionary<string, object> GeographicScope { get; set; } = new();
        public string? SpecialProvisions { get; set; }
        public List<ContractRateDto> Rates { get; set; } = new();
        public List<ContractDiscountDto> Discounts { get; set; } = new();
        public List<ContractSurchargeDto> Surcharges { get; set; } = new();
        public List<ContractCommitmentDto> Commitments { get; set; } = new();
    }

    public class UpdateContractDto
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? Terms { get; set; }
        public string? PaymentTerms { get; set; }
        public decimal? MinimumCommitment { get; set; }
        public decimal? MaximumCommitment { get; set; }
        public string? CommitmentType { get; set; }
        public decimal? CommitmentPeriod { get; set; }
        public bool AutoRenewal { get; set; }
        public int? AutoRenewalPeriod { get; set; }
        public string? NotificationPeriod { get; set; }
        public List<string> ServiceTypes { get; set; } = new();
        public Dictionary<string, object> GeographicScope { get; set; } = new();
        public string? SpecialProvisions { get; set; }
    }
}
