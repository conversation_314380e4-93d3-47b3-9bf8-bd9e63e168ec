using AutoMapper;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.ValueObjects;
using System.Text.Json;

namespace PricingManagement.Application.Mappings
{
    public class PricingMappingProfile : Profile
    {
        public PricingMappingProfile()
        {
            // PricingRule mappings
            CreateMap<PricingRule, PricingRuleDto>()
                .ForMember(dest => dest.ServiceTypes, opt => opt.MapFrom(src => DeserializeStringList(src.ServiceTypes)))
                .ForMember(dest => dest.OriginZones, opt => opt.MapFrom(src => DeserializeStringList(src.OriginZones)))
                .ForMember(dest => dest.DestinationZones, opt => opt.MapFrom(src => DeserializeStringList(src.DestinationZones)))
                .ForMember(dest => dest.CustomerSegments, opt => opt.MapFrom(src => DeserializeStringList(src.CustomerSegments)))
                .ForMember(dest => dest.ShipperTypes, opt => opt.MapFrom(src => DeserializeStringList(src.ShipperTypes)))
                .ForMember(dest => dest.Tags, opt => opt.MapFrom(src => DeserializeStringList(src.Tags)))
                .ForMember(dest => dest.RuleConfiguration, opt => opt.MapFrom(src => DeserializeDictionary(src.RuleConfiguration)))
                .ForMember(dest => dest.Tiers, opt => opt.MapFrom(src => src.Tiers));

            CreateMap<PricingRuleTier, PricingRuleTierDto>();

            // Quote mappings
            CreateMap<Quote, QuoteDto>()
                .ForMember(dest => dest.SpecialServices, opt => opt.MapFrom(src => DeserializeStringList(src.SpecialServices)))
                .ForMember(dest => dest.LineItems, opt => opt.MapFrom(src => src.LineItems))
                .ForMember(dest => dest.Surcharges, opt => opt.MapFrom(src => src.Surcharges))
                .ForMember(dest => dest.Discounts, opt => opt.MapFrom(src => src.Discounts))
                .ForMember(dest => dest.Taxes, opt => opt.MapFrom(src => src.Taxes));

            CreateMap<QuoteLineItem, QuoteLineItemDto>();
            CreateMap<QuoteSurcharge, SurchargeDto>();
            CreateMap<QuoteDiscount, DiscountDto>();
            CreateMap<QuoteTax, TaxDto>();

            // Contract mappings
            CreateMap<Contract, ContractDto>()
                .ForMember(dest => dest.ServiceTypes, opt => opt.MapFrom(src => DeserializeStringList(src.ServiceTypes)))
                .ForMember(dest => dest.GeographicScope, opt => opt.MapFrom(src => DeserializeDictionary(src.GeographicScope)))
                .ForMember(dest => dest.Rates, opt => opt.MapFrom(src => src.Rates))
                .ForMember(dest => dest.Discounts, opt => opt.MapFrom(src => src.Discounts))
                .ForMember(dest => dest.Surcharges, opt => opt.MapFrom(src => src.Surcharges))
                .ForMember(dest => dest.Commitments, opt => opt.MapFrom(src => src.Commitments));

            CreateMap<ContractRate, ContractRateDto>();
            CreateMap<ContractDiscount, ContractDiscountDto>();
            CreateMap<ContractSurcharge, ContractSurchargeDto>();
            CreateMap<ContractCommitment, ContractCommitmentDto>();

            // Value object mappings
            CreateMap<Money, MoneyDto>()
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Amount))
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency));

            CreateMap<MoneyDto, Money>()
                .ConstructUsing(src => new Money(src.Amount, src.Currency));

            CreateMap<Weight, WeightDto>()
                .ForMember(dest => dest.Value, opt => opt.MapFrom(src => src.Value))
                .ForMember(dest => dest.Unit, opt => opt.MapFrom(src => src.Unit));

            CreateMap<WeightDto, Weight>()
                .ConstructUsing(src => new Weight(src.Value, src.Unit));

            CreateMap<Dimensions, DimensionsDto>()
                .ForMember(dest => dest.Length, opt => opt.MapFrom(src => src.Length))
                .ForMember(dest => dest.Width, opt => opt.MapFrom(src => src.Width))
                .ForMember(dest => dest.Height, opt => opt.MapFrom(src => src.Height))
                .ForMember(dest => dest.Unit, opt => opt.MapFrom(src => src.Unit));

            CreateMap<DimensionsDto, Dimensions>()
                .ConstructUsing(src => new Dimensions(src.Length, src.Width, src.Height, src.Unit));
        }

        private static List<string> DeserializeStringList(string? json)
        {
            if (string.IsNullOrWhiteSpace(json))
                return new List<string>();

            try
            {
                return JsonSerializer.Deserialize<List<string>>(json) ?? new List<string>();
            }
            catch
            {
                return new List<string>();
            }
        }

        private static Dictionary<string, object> DeserializeDictionary(string? json)
        {
            if (string.IsNullOrWhiteSpace(json))
                return new Dictionary<string, object>();

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(json) ?? new Dictionary<string, object>();
            }
            catch
            {
                return new Dictionary<string, object>();
            }
        }
    }
}
