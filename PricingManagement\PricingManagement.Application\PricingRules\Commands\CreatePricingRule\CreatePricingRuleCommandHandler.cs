using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Repositories;
using System.Text.Json;

namespace PricingManagement.Application.PricingRules.Commands.CreatePricingRule
{
    public class CreatePricingRuleCommandHandler : IRequestHandler<CreatePricingRuleCommand, OperationResultDto<PricingRuleDto>>
    {
        private readonly IPricingRuleRepository _pricingRuleRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILogger<CreatePricingRuleCommandHandler> _logger;

        public CreatePricingRuleCommandHandler(
            IPricingRuleRepository pricingRuleRepository,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILogger<CreatePricingRuleCommandHandler> logger)
        {
            _pricingRuleRepository = pricingRuleRepository;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<OperationResultDto<PricingRuleDto>> Handle(CreatePricingRuleCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return OperationResultDto<PricingRuleDto>.Failure("Organization context is required");
                }

                var organizationId = _currentUserService.OrganizationId.Value;
                var currentUser = _currentUserService.Username ?? "System";

                // Check if name already exists
                var nameExists = await _pricingRuleRepository.NameExistsAsync(request.Name, organizationId);
                if (nameExists)
                {
                    return OperationResultDto<PricingRuleDto>.Failure($"A pricing rule with name '{request.Name}' already exists");
                }

                // Serialize configuration and arrays
                var ruleConfiguration = JsonSerializer.Serialize(request.RuleConfiguration);
                var serviceTypes = request.ServiceTypes.Any() ? JsonSerializer.Serialize(request.ServiceTypes) : null;
                var originZones = request.OriginZones.Any() ? JsonSerializer.Serialize(request.OriginZones) : null;
                var destinationZones = request.DestinationZones.Any() ? JsonSerializer.Serialize(request.DestinationZones) : null;
                var customerSegments = request.CustomerSegments.Any() ? JsonSerializer.Serialize(request.CustomerSegments) : null;
                var shipperTypes = request.ShipperTypes.Any() ? JsonSerializer.Serialize(request.ShipperTypes) : null;
                var tags = request.Tags.Any() ? JsonSerializer.Serialize(request.Tags) : null;

                // Create pricing rule
                var pricingRule = new PricingRule(
                    request.Name,
                    request.Description,
                    request.RuleType,
                    request.CalculationMethod,
                    request.Priority,
                    request.EffectiveDate,
                    ruleConfiguration,
                    organizationId,
                    currentUser);

                // Set expiration date if provided
                if (request.ExpirationDate.HasValue)
                {
                    pricingRule.SetEffectivePeriod(request.EffectiveDate, request.ExpirationDate, currentUser);
                }

                // Set criteria
                pricingRule.SetCriteria(
                    serviceTypes,
                    originZones,
                    destinationZones,
                    customerSegments,
                    shipperTypes,
                    request.MinWeight,
                    request.MaxWeight,
                    request.MinDistance,
                    request.MaxDistance,
                    request.MinValue,
                    request.MaxValue,
                    currentUser);

                // Add tiers
                foreach (var tier in request.Tiers)
                {
                    pricingRule.AddTier(tier.FromValue, tier.ToValue, tier.Rate, tier.RateType, currentUser);
                }

                // Set approval requirement
                pricingRule.SetApprovalRequirement(request.RequiresApproval, request.ApprovalWorkflow, currentUser);

                // Set tags
                pricingRule.SetTags(tags, currentUser);

                // Save to repository
                await _pricingRuleRepository.AddAsync(pricingRule);

                // Map to DTO
                var pricingRuleDto = _mapper.Map<PricingRuleDto>(pricingRule);

                _logger.LogInformation("Created pricing rule {PricingRuleId} with name {Name} for organization {OrganizationId}",
                    pricingRule.Id, pricingRule.Name, organizationId);

                return OperationResultDto<PricingRuleDto>.Success(pricingRuleDto, "Pricing rule created successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating pricing rule with name {Name}", request.Name);
                return OperationResultDto<PricingRuleDto>.Failure("An error occurred while creating the pricing rule");
            }
        }
    }
}
