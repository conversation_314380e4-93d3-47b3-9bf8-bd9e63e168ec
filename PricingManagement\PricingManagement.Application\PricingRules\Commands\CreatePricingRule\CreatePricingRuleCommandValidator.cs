using FluentValidation;
using PricingManagement.Domain.Repositories;

namespace PricingManagement.Application.PricingRules.Commands.CreatePricingRule
{
    public class CreatePricingRuleCommandValidator : AbstractValidator<CreatePricingRuleCommand>
    {
        private readonly IPricingRuleRepository _pricingRuleRepository;

        public CreatePricingRuleCommandValidator(IPricingRuleRepository pricingRuleRepository)
        {
            _pricingRuleRepository = pricingRuleRepository;

            RuleFor(x => x.Name)
                .NotEmpty().WithMessage("Name is required")
                .MaximumLength(200).WithMessage("Name must not exceed 200 characters");

            RuleFor(x => x.Description)
                .NotEmpty().WithMessage("Description is required")
                .MaximumLength(1000).WithMessage("Description must not exceed 1000 characters");

            RuleFor(x => x.Priority)
                .GreaterThan(0).WithMessage("Priority must be greater than 0")
                .LessThanOrEqualTo(1000).WithMessage("Priority must not exceed 1000");

            RuleFor(x => x.EffectiveDate)
                .GreaterThanOrEqualTo(DateTime.UtcNow.Date)
                .WithMessage("Effective date cannot be in the past");

            RuleFor(x => x.ExpirationDate)
                .GreaterThan(x => x.EffectiveDate)
                .When(x => x.ExpirationDate.HasValue)
                .WithMessage("Expiration date must be after effective date");

            RuleFor(x => x.MinWeight)
                .GreaterThanOrEqualTo(0)
                .When(x => x.MinWeight.HasValue)
                .WithMessage("Minimum weight cannot be negative");

            RuleFor(x => x.MaxWeight)
                .GreaterThanOrEqualTo(0)
                .When(x => x.MaxWeight.HasValue)
                .WithMessage("Maximum weight cannot be negative");

            RuleFor(x => x.MaxWeight)
                .GreaterThan(x => x.MinWeight)
                .When(x => x.MinWeight.HasValue && x.MaxWeight.HasValue)
                .WithMessage("Maximum weight must be greater than minimum weight");

            RuleFor(x => x.MinDistance)
                .GreaterThanOrEqualTo(0)
                .When(x => x.MinDistance.HasValue)
                .WithMessage("Minimum distance cannot be negative");

            RuleFor(x => x.MaxDistance)
                .GreaterThanOrEqualTo(0)
                .When(x => x.MaxDistance.HasValue)
                .WithMessage("Maximum distance cannot be negative");

            RuleFor(x => x.MaxDistance)
                .GreaterThan(x => x.MinDistance)
                .When(x => x.MinDistance.HasValue && x.MaxDistance.HasValue)
                .WithMessage("Maximum distance must be greater than minimum distance");

            RuleFor(x => x.MinValue)
                .GreaterThanOrEqualTo(0)
                .When(x => x.MinValue.HasValue)
                .WithMessage("Minimum value cannot be negative");

            RuleFor(x => x.MaxValue)
                .GreaterThanOrEqualTo(0)
                .When(x => x.MaxValue.HasValue)
                .WithMessage("Maximum value cannot be negative");

            RuleFor(x => x.MaxValue)
                .GreaterThan(x => x.MinValue)
                .When(x => x.MinValue.HasValue && x.MaxValue.HasValue)
                .WithMessage("Maximum value must be greater than minimum value");

            RuleFor(x => x.RuleConfiguration)
                .NotEmpty().WithMessage("Rule configuration is required");

            RuleForEach(x => x.Tiers)
                .SetValidator(new PricingRuleTierValidator());

            RuleFor(x => x.ApprovalWorkflow)
                .NotEmpty()
                .When(x => x.RequiresApproval)
                .WithMessage("Approval workflow is required when approval is required");
        }
    }

    public class PricingRuleTierValidator : AbstractValidator<DTOs.PricingRuleTierDto>
    {
        public PricingRuleTierValidator()
        {
            RuleFor(x => x.FromValue)
                .GreaterThanOrEqualTo(0)
                .WithMessage("From value cannot be negative");

            RuleFor(x => x.ToValue)
                .GreaterThan(x => x.FromValue)
                .When(x => x.ToValue.HasValue)
                .WithMessage("To value must be greater than from value");

            RuleFor(x => x.Rate)
                .GreaterThanOrEqualTo(0)
                .WithMessage("Rate cannot be negative");
        }
    }
}
