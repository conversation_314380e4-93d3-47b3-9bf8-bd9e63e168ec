using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Repositories;

namespace PricingManagement.Application.PricingRules.Queries.GetPricingRuleById
{
    public class GetPricingRuleByIdQueryHandler : IRequestHandler<GetPricingRuleByIdQuery, OperationResultDto<PricingRuleDto>>
    {
        private readonly IPricingRuleRepository _pricingRuleRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILogger<GetPricingRuleByIdQueryHandler> _logger;

        public GetPricingRuleByIdQueryHandler(
            IPricingRuleRepository pricingRuleRepository,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILogger<GetPricingRuleByIdQueryHandler> logger)
        {
            _pricingRuleRepository = pricingRuleRepository;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<OperationResultDto<PricingRuleDto>> Handle(GetPricingRuleByIdQuery request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return OperationResultDto<PricingRuleDto>.Failure("Organization context is required");
                }

                var organizationId = _currentUserService.OrganizationId.Value;

                var pricingRule = await _pricingRuleRepository.GetByIdAsync(request.Id, organizationId);
                if (pricingRule == null)
                {
                    return OperationResultDto<PricingRuleDto>.Failure("Pricing rule not found");
                }

                var pricingRuleDto = _mapper.Map<PricingRuleDto>(pricingRule);

                return OperationResultDto<PricingRuleDto>.Success(pricingRuleDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving pricing rule {PricingRuleId}", request.Id);
                return OperationResultDto<PricingRuleDto>.Failure("An error occurred while retrieving the pricing rule");
            }
        }
    }
}
