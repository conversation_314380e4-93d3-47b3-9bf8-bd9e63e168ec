using MediatR;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Enums;

namespace PricingManagement.Application.PricingRules.Queries.GetPricingRules
{
    public class GetPricingRulesQuery : IRequest<PagedResultDto<PricingRuleDto>>
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; }
        public PricingStatus? Status { get; set; }
        public PricingRuleType? RuleType { get; set; }
        public DateTime? EffectiveFrom { get; set; }
        public DateTime? EffectiveTo { get; set; }
        public List<string> Tags { get; set; } = new();
        public bool ActiveOnly { get; set; }
    }
}
