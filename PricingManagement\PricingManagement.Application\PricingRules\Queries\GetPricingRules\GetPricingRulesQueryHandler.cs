using AutoMapper;
using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Repositories;

namespace PricingManagement.Application.PricingRules.Queries.GetPricingRules
{
    public class GetPricingRulesQueryHandler : IRequestHandler<GetPricingRulesQuery, PagedResultDto<PricingRuleDto>>
    {
        private readonly IPricingRuleRepository _pricingRuleRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMapper _mapper;
        private readonly ILogger<GetPricingRulesQueryHandler> _logger;

        public GetPricingRulesQueryHandler(
            IPricingRuleRepository pricingRuleRepository,
            ICurrentUserService currentUserService,
            IMapper mapper,
            ILogger<GetPricingRulesQueryHandler> logger)
        {
            _pricingRuleRepository = pricingRuleRepository;
            _currentUserService = currentUserService;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<PagedResultDto<PricingRuleDto>> Handle(GetPricingRulesQuery request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return new PagedResultDto<PricingRuleDto>
                    {
                        Items = new List<PricingRuleDto>(),
                        TotalCount = 0,
                        PageNumber = request.PageNumber,
                        PageSize = request.PageSize
                    };
                }

                var organizationId = _currentUserService.OrganizationId.Value;

                // Get all pricing rules for the organization
                var allRules = await _pricingRuleRepository.GetByOrganizationAsync(organizationId);

                // Apply filters
                var filteredRules = allRules.AsQueryable();

                if (!string.IsNullOrWhiteSpace(request.SearchTerm))
                {
                    filteredRules = filteredRules.Where(r => 
                        r.Name.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        r.Description.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase));
                }

                if (request.Status.HasValue)
                {
                    filteredRules = filteredRules.Where(r => r.Status == request.Status.Value);
                }

                if (request.RuleType.HasValue)
                {
                    filteredRules = filteredRules.Where(r => r.RuleType == request.RuleType.Value);
                }

                if (request.EffectiveFrom.HasValue)
                {
                    filteredRules = filteredRules.Where(r => r.EffectiveDate >= request.EffectiveFrom.Value);
                }

                if (request.EffectiveTo.HasValue)
                {
                    filteredRules = filteredRules.Where(r => r.EffectiveDate <= request.EffectiveTo.Value);
                }

                if (request.ActiveOnly)
                {
                    var currentDate = DateTime.UtcNow;
                    filteredRules = filteredRules.Where(r => 
                        r.Status == Domain.Enums.PricingStatus.Active &&
                        r.EffectiveDate <= currentDate &&
                        (!r.ExpirationDate.HasValue || r.ExpirationDate > currentDate));
                }

                if (request.Tags.Any())
                {
                    filteredRules = filteredRules.Where(r => 
                        !string.IsNullOrEmpty(r.Tags) && 
                        request.Tags.Any(tag => r.Tags.Contains($"\"{tag}\"")));
                }

                // Apply sorting
                if (!string.IsNullOrWhiteSpace(request.SortBy))
                {
                    filteredRules = request.SortBy.ToLower() switch
                    {
                        "name" => request.SortDescending 
                            ? filteredRules.OrderByDescending(r => r.Name)
                            : filteredRules.OrderBy(r => r.Name),
                        "priority" => request.SortDescending 
                            ? filteredRules.OrderByDescending(r => r.Priority)
                            : filteredRules.OrderBy(r => r.Priority),
                        "effectivedate" => request.SortDescending 
                            ? filteredRules.OrderByDescending(r => r.EffectiveDate)
                            : filteredRules.OrderBy(r => r.EffectiveDate),
                        "status" => request.SortDescending 
                            ? filteredRules.OrderByDescending(r => r.Status)
                            : filteredRules.OrderBy(r => r.Status),
                        "createdat" => request.SortDescending 
                            ? filteredRules.OrderByDescending(r => r.CreatedAt)
                            : filteredRules.OrderBy(r => r.CreatedAt),
                        _ => filteredRules.OrderBy(r => r.Priority).ThenBy(r => r.Name)
                    };
                }
                else
                {
                    filteredRules = filteredRules.OrderBy(r => r.Priority).ThenBy(r => r.Name);
                }

                var totalCount = filteredRules.Count();

                // Apply pagination
                var pagedRules = filteredRules
                    .Skip((request.PageNumber - 1) * request.PageSize)
                    .Take(request.PageSize)
                    .ToList();

                // Map to DTOs
                var pricingRuleDtos = _mapper.Map<List<PricingRuleDto>>(pagedRules);

                _logger.LogInformation("Retrieved {Count} pricing rules for organization {OrganizationId}", 
                    pagedRules.Count, organizationId);

                return new PagedResultDto<PricingRuleDto>
                {
                    Items = pricingRuleDtos,
                    TotalCount = totalCount,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving pricing rules");
                return new PagedResultDto<PricingRuleDto>
                {
                    Items = new List<PricingRuleDto>(),
                    TotalCount = 0,
                    PageNumber = request.PageNumber,
                    PageSize = request.PageSize
                };
            }
        }
    }
}
