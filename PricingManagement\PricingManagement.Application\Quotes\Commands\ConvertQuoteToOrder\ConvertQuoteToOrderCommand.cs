using MediatR;
using PricingManagement.Application.DTOs;
using System;

namespace PricingManagement.Application.Quotes.Commands.ConvertQuoteToOrder
{
    public class ConvertQuoteToOrderCommand : IRequest<OperationResultDto<Guid>>
    {
        public Guid QuoteId { get; set; }
        public string? SpecialInstructions { get; set; }
        public DateTime? RequestedPickupDate { get; set; }
        public DateTime? RequestedDeliveryDate { get; set; }

        public ConvertQuoteToOrderCommand(Guid quoteId)
        {
            QuoteId = quoteId;
        }
    }
}
