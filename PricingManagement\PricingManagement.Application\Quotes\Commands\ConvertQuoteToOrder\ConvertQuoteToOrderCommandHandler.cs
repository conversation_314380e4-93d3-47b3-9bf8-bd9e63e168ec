using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Services;
using PricingManagement.Domain.Repositories;

namespace PricingManagement.Application.Quotes.Commands.ConvertQuoteToOrder
{
    public class ConvertQuoteToOrderCommandHandler : IRequestHandler<ConvertQuoteToOrderCommand, OperationResultDto<Guid>>
    {
        private readonly IQuoteRepository _quoteRepository;
        private readonly IOrderManagementService _orderManagementService;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<ConvertQuoteToOrderCommandHandler> _logger;

        public ConvertQuoteToOrderCommandHandler(
            IQuoteRepository quoteRepository,
            IOrderManagementService orderManagementService,
            ICurrentUserService currentUserService,
            ILogger<ConvertQuoteToOrderCommandHandler> logger)
        {
            _quoteRepository = quoteRepository;
            _orderManagementService = orderManagementService;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<OperationResultDto<Guid>> Handle(ConvertQuoteToOrderCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return OperationResultDto<Guid>.Failure("Organization context is required");
                }

                var organizationId = _currentUserService.OrganizationId.Value;
                var currentUser = _currentUserService.Username ?? "System";

                var quote = await _quoteRepository.GetByIdAsync(request.QuoteId, organizationId);
                if (quote == null)
                {
                    return OperationResultDto<Guid>.Failure("Quote not found");
                }

                if (!quote.CustomerId.HasValue)
                {
                    return OperationResultDto<Guid>.Failure("Quote must have a customer to convert to order");
                }

                // Create order from quote
                var createOrderRequest = new CreateOrderFromQuoteDto
                {
                    QuoteId = quote.Id,
                    CustomerId = quote.CustomerId.Value,
                    SpecialInstructions = request.SpecialInstructions,
                    RequestedPickupDate = request.RequestedPickupDate,
                    RequestedDeliveryDate = request.RequestedDeliveryDate
                };

                var orderId = await _orderManagementService.CreateOrderFromQuoteAsync(quote.Id, createOrderRequest);

                // Convert the quote
                quote.ConvertToOrder(orderId, currentUser);

                await _quoteRepository.UpdateAsync(quote);

                _logger.LogInformation("Converted quote {QuoteNumber} to order {OrderId} for organization {OrganizationId}",
                    quote.QuoteNumber, orderId, organizationId);

                return OperationResultDto<Guid>.Success(orderId, "Quote converted to order successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error converting quote {QuoteId} to order", request.QuoteId);
                return OperationResultDto<Guid>.Failure("An error occurred while converting the quote to order");
            }
        }
    }
}
