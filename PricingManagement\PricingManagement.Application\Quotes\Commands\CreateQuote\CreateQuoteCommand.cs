using MediatR;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using System;
using System.Collections.Generic;

namespace PricingManagement.Application.Quotes.Commands.CreateQuote
{
    public class CreateQuoteCommand : IRequest<OperationResultDto<QuoteDto>>
    {
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public string? CustomerName { get; set; }
        public string? ShipperName { get; set; }
        public DateTime ExpirationDate { get; set; }
        public string OriginAddress { get; set; } = string.Empty;
        public string DestinationAddress { get; set; } = string.Empty;
        public string? OriginZoneId { get; set; }
        public string? DestinationZoneId { get; set; }
        public decimal? Distance { get; set; }
        public string ServiceType { get; set; } = string.Empty;
        public WeightDto PackageWeight { get; set; } = new();
        public DimensionsDto? PackageDimensions { get; set; }
        public decimal? DeclaredValue { get; set; }
        public List<string> SpecialServices { get; set; } = new();
        public CurrencyCode Currency { get; set; } = CurrencyCode.USD;
        public string? Notes { get; set; }
        public string? Terms { get; set; }
        public List<CreateQuoteLineItemDto> LineItems { get; set; } = new();
        public bool AutoCalculateRates { get; set; } = true;
    }
}
