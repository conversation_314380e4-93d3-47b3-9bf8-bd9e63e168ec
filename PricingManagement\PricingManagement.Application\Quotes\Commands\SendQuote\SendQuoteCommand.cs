using MediatR;
using PricingManagement.Application.DTOs;
using System;

namespace PricingManagement.Application.Quotes.Commands.SendQuote
{
    public class SendQuoteCommand : IRequest<OperationResultDto<bool>>
    {
        public Guid QuoteId { get; set; }
        public string? EmailAddress { get; set; }
        public string? AdditionalMessage { get; set; }
        public bool SendCopy { get; set; } = true;

        public SendQuoteCommand(Guid quoteId)
        {
            QuoteId = quoteId;
        }
    }
}
