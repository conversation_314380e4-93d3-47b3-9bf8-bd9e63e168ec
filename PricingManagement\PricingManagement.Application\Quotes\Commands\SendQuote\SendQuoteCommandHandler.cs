using MediatR;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.Common;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Repositories;

namespace PricingManagement.Application.Quotes.Commands.SendQuote
{
    public class SendQuoteCommandHandler : IRequestHandler<SendQuoteCommand, OperationResultDto<bool>>
    {
        private readonly IQuoteRepository _quoteRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly ILogger<SendQuoteCommandHandler> _logger;

        public SendQuoteCommandHandler(
            IQuoteRepository quoteRepository,
            ICurrentUserService currentUserService,
            ILogger<SendQuoteCommandHandler> logger)
        {
            _quoteRepository = quoteRepository;
            _currentUserService = currentUserService;
            _logger = logger;
        }

        public async Task<OperationResultDto<bool>> Handle(SendQuoteCommand request, CancellationToken cancellationToken)
        {
            try
            {
                if (!_currentUserService.OrganizationId.HasValue)
                {
                    return OperationResultDto<bool>.Failure("Organization context is required");
                }

                var organizationId = _currentUserService.OrganizationId.Value;
                var currentUser = _currentUserService.Username ?? "System";

                var quote = await _quoteRepository.GetByIdAsync(request.QuoteId, organizationId);
                if (quote == null)
                {
                    return OperationResultDto<bool>.Failure("Quote not found");
                }

                // Send the quote
                quote.Send(currentUser);

                // TODO: Implement actual email sending logic
                // This would integrate with an email service to send the quote to the customer

                await _quoteRepository.UpdateAsync(quote);

                _logger.LogInformation("Sent quote {QuoteNumber} for organization {OrganizationId}",
                    quote.QuoteNumber, organizationId);

                return OperationResultDto<bool>.Success(true, "Quote sent successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending quote {QuoteId}", request.QuoteId);
                return OperationResultDto<bool>.Failure("An error occurred while sending the quote");
            }
        }
    }
}
