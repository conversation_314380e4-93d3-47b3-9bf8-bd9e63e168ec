using MediatR;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Enums;
using System;

namespace PricingManagement.Application.Quotes.Queries.GetQuotes
{
    public class GetQuotesQuery : IRequest<PagedResultDto<QuoteDto>>
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public string? SortBy { get; set; }
        public bool SortDescending { get; set; }
        public QuoteStatus? Status { get; set; }
        public Guid? CustomerId { get; set; }
        public Guid? ShipperId { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public DateTime? ExpiringBefore { get; set; }
        public bool ConvertedOnly { get; set; }
        public decimal? MinAmount { get; set; }
        public decimal? MaxAmount { get; set; }
    }
}
