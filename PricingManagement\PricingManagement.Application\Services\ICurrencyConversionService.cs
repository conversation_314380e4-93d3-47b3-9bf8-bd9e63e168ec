using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;

namespace PricingManagement.Application.Services
{
    public interface ICurrencyConversionService
    {
        Task<Money> ConvertAsync(Money amount, CurrencyCode targetCurrency);
        Task<decimal> GetExchangeRateAsync(CurrencyCode fromCurrency, CurrencyCode toCurrency);
        Task<Dictionary<CurrencyCode, decimal>> GetExchangeRatesAsync(CurrencyCode baseCurrency);
        Task<bool> IsCurrencySupportedAsync(CurrencyCode currency);
        Task<DateTime> GetLastUpdateTimeAsync();
        Task RefreshExchangeRatesAsync();
        Task<Money> ConvertWithHistoricalRateAsync(Money amount, CurrencyCode targetCurrency, DateTime date);
        Task<decimal> GetHistoricalExchangeRateAsync(CurrencyCode fromCurrency, CurrencyCode toCurrency, DateTime date);
    }
}
