using PricingManagement.Application.DTOs;

namespace PricingManagement.Application.Services
{
    public interface ISurchargeService
    {
        Task<IEnumerable<SurchargeDto>> CalculateSurchargesAsync(RateCalculationRequestDto request);
        Task<SurchargeDto> CalculateFuelSurchargeAsync(RateCalculationRequestDto request);
        Task<SurchargeDto> CalculateRemoteAreaSurchargeAsync(RateCalculationRequestDto request);
        Task<SurchargeDto> CalculateOversizeSurchargeAsync(RateCalculationRequestDto request);
        Task<SurchargeDto> CalculateHazmatSurchargeAsync(RateCalculationRequestDto request);
        Task<SurchargeDto> CalculateResidentialSurchargeAsync(RateCalculationRequestDto request);
        Task<SurchargeDto> CalculateSignatureRequiredSurchargeAsync(RateCalculationRequestDto request);
        Task<SurchargeDto> CalculateInsuranceSurchargeAsync(RateCalculationRequestDto request);
        Task<SurchargeDto> CalculateSpecialHandlingSurchargeAsync(RateCalculationRequestDto request);
    }
}
