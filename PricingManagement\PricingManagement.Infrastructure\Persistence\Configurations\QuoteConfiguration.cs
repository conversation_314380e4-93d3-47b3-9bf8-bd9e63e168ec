using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;

namespace PricingManagement.Infrastructure.Persistence.Configurations
{
    public class QuoteConfiguration : IEntityTypeConfiguration<Quote>
    {
        public void Configure(EntityTypeBuilder<Quote> builder)
        {
            builder.ToTable("Quotes");

            builder.HasKey(x => x.Id);

            builder.Property(x => x.Id)
                .ValueGeneratedNever();

            builder.Property(x => x.QuoteNumber)
                .IsRequired()
                .HasMaxLength(50);

            builder.Property(x => x.CustomerId);

            builder.Property(x => x.ShipperId);

            builder.Property(x => x.CustomerName)
                .HasMaxLength(200);

            builder.Property(x => x.ShipperName)
                .HasMaxLength(200);

            builder.Property(x => x.Status)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(x => x.ExpirationDate)
                .IsRequired();

            builder.Property(x => x.OriginAddress)
                .HasMaxLength(500);

            builder.Property(x => x.DestinationAddress)
                .HasMaxLength(500);

            builder.Property(x => x.OriginZoneId)
                .HasMaxLength(50);

            builder.Property(x => x.DestinationZoneId)
                .HasMaxLength(50);

            builder.Property(x => x.Distance)
                .HasPrecision(18, 2);

            builder.Property(x => x.ServiceType)
                .HasMaxLength(100);

            builder.Property(x => x.DeclaredValue)
                .HasPrecision(18, 2);

            builder.Property(x => x.SpecialServices)
                .HasColumnType("jsonb");

            builder.Property(x => x.Currency)
                .IsRequired()
                .HasConversion<int>();

            builder.Property(x => x.Notes)
                .HasMaxLength(2000);

            builder.Property(x => x.Terms)
                .HasMaxLength(2000);

            builder.Property(x => x.ConvertedToOrderAt);

            builder.Property(x => x.OrderId);

            builder.Property(x => x.ExternalQuoteId)
                .HasMaxLength(100);

            builder.Property(x => x.CarrierQuoteData)
                .HasColumnType("jsonb");

            // Value object configurations
            builder.OwnsOne(x => x.PackageWeight, weightBuilder =>
            {
                weightBuilder.Property(w => w.Value)
                    .HasColumnName("PackageWeightValue")
                    .HasPrecision(18, 3);

                weightBuilder.Property(w => w.Unit)
                    .HasColumnName("PackageWeightUnit")
                    .HasConversion<int>();
            });

            builder.OwnsOne(x => x.PackageDimensions, dimBuilder =>
            {
                dimBuilder.Property(d => d.Length)
                    .HasColumnName("PackageLength")
                    .HasPrecision(18, 2);

                dimBuilder.Property(d => d.Width)
                    .HasColumnName("PackageWidth")
                    .HasPrecision(18, 2);

                dimBuilder.Property(d => d.Height)
                    .HasColumnName("PackageHeight")
                    .HasPrecision(18, 2);

                dimBuilder.Property(d => d.Unit)
                    .HasColumnName("PackageDimensionUnit")
                    .HasConversion<int>();
            });

            builder.OwnsOne(x => x.DimensionalWeight, weightBuilder =>
            {
                weightBuilder.Property(w => w.Value)
                    .HasColumnName("DimensionalWeightValue")
                    .HasPrecision(18, 3);

                weightBuilder.Property(w => w.Unit)
                    .HasColumnName("DimensionalWeightUnit")
                    .HasConversion<int>();
            });

            builder.OwnsOne(x => x.BillableWeight, weightBuilder =>
            {
                weightBuilder.Property(w => w.Value)
                    .HasColumnName("BillableWeightValue")
                    .HasPrecision(18, 3);

                weightBuilder.Property(w => w.Unit)
                    .HasColumnName("BillableWeightUnit")
                    .HasConversion<int>();
            });

            builder.OwnsOne(x => x.BaseRate, moneyBuilder =>
            {
                moneyBuilder.Property(m => m.Amount)
                    .HasColumnName("BaseRateAmount")
                    .HasPrecision(18, 4);

                moneyBuilder.Property(m => m.Currency)
                    .HasColumnName("BaseRateCurrency")
                    .HasConversion<int>();
            });

            builder.OwnsOne(x => x.TotalSurcharges, moneyBuilder =>
            {
                moneyBuilder.Property(m => m.Amount)
                    .HasColumnName("TotalSurchargesAmount")
                    .HasPrecision(18, 4);

                moneyBuilder.Property(m => m.Currency)
                    .HasColumnName("TotalSurchargesCurrency")
                    .HasConversion<int>();
            });

            builder.OwnsOne(x => x.TotalDiscounts, moneyBuilder =>
            {
                moneyBuilder.Property(m => m.Amount)
                    .HasColumnName("TotalDiscountsAmount")
                    .HasPrecision(18, 4);

                moneyBuilder.Property(m => m.Currency)
                    .HasColumnName("TotalDiscountsCurrency")
                    .HasConversion<int>();
            });

            builder.OwnsOne(x => x.TotalTaxes, moneyBuilder =>
            {
                moneyBuilder.Property(m => m.Amount)
                    .HasColumnName("TotalTaxesAmount")
                    .HasPrecision(18, 4);

                moneyBuilder.Property(m => m.Currency)
                    .HasColumnName("TotalTaxesCurrency")
                    .HasConversion<int>();
            });

            builder.OwnsOne(x => x.TotalAmount, moneyBuilder =>
            {
                moneyBuilder.Property(m => m.Amount)
                    .HasColumnName("TotalAmountAmount")
                    .HasPrecision(18, 4);

                moneyBuilder.Property(m => m.Currency)
                    .HasColumnName("TotalAmountCurrency")
                    .HasConversion<int>();
            });

            // Base entity properties
            builder.Property(x => x.CreatedAt)
                .IsRequired();

            builder.Property(x => x.UpdatedAt);

            builder.Property(x => x.CreatedBy)
                .IsRequired()
                .HasMaxLength(100);

            builder.Property(x => x.UpdatedBy)
                .HasMaxLength(100);

            builder.Property(x => x.IsDeleted)
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(x => x.DeletedAt);

            builder.Property(x => x.DeletedBy)
                .HasMaxLength(100);

            builder.Property(x => x.OrganizationId)
                .IsRequired();

            // Indexes
            builder.HasIndex(x => x.OrganizationId);
            builder.HasIndex(x => new { x.OrganizationId, x.QuoteNumber }).IsUnique();
            builder.HasIndex(x => new { x.OrganizationId, x.Status });
            builder.HasIndex(x => new { x.OrganizationId, x.CustomerId });
            builder.HasIndex(x => new { x.OrganizationId, x.ShipperId });
            builder.HasIndex(x => new { x.OrganizationId, x.ExpirationDate });
            builder.HasIndex(x => new { x.OrganizationId, x.CreatedAt });
        }
    }
}
