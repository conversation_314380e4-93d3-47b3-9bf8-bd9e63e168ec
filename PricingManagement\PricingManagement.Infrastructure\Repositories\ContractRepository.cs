using Microsoft.EntityFrameworkCore;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Repositories;
using PricingManagement.Infrastructure.Persistence;

namespace PricingManagement.Infrastructure.Repositories
{
    public class ContractRepository : IContractRepository
    {
        private readonly PricingManagementDbContext _context;

        public ContractRepository(PricingManagementDbContext context)
        {
            _context = context;
        }

        public async Task<Contract?> GetByIdAsync(Guid id)
        {
            return await _context.Contracts
                .Include(x => x.Rates)
                .Include(x => x.Discounts)
                .Include(x => x.Surcharges)
                .Include(x => x.Commitments)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<Contract?> GetByIdAsync(Guid id, Guid organizationId)
        {
            return await _context.Contracts
                .Include(x => x.Rates)
                .Include(x => x.Discounts)
                .Include(x => x.Surcharges)
                .Include(x => x.Commitments)
                .FirstOrDefaultAsync(x => x.Id == id && x.OrganizationId == organizationId);
        }

        public async Task<Contract?> GetByContractNumberAsync(string contractNumber, Guid organizationId)
        {
            return await _context.Contracts
                .Include(x => x.Rates)
                .Include(x => x.Discounts)
                .Include(x => x.Surcharges)
                .Include(x => x.Commitments)
                .FirstOrDefaultAsync(x => x.ContractNumber == contractNumber && x.OrganizationId == organizationId);
        }

        public async Task<IEnumerable<Contract>> GetByOrganizationAsync(Guid organizationId)
        {
            return await _context.Contracts
                .Where(x => x.OrganizationId == organizationId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Contract>> GetByCustomerAsync(Guid customerId, Guid organizationId)
        {
            return await _context.Contracts
                .Where(x => x.CustomerId == customerId && x.OrganizationId == organizationId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Contract>> GetByShipperAsync(Guid shipperId, Guid organizationId)
        {
            return await _context.Contracts
                .Where(x => x.ShipperId == shipperId && x.OrganizationId == organizationId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Contract>> GetByStatusAsync(ContractStatus status, Guid organizationId)
        {
            return await _context.Contracts
                .Where(x => x.Status == status && x.OrganizationId == organizationId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Contract>> GetActiveContractsAsync(Guid organizationId)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.Contracts
                .Where(x => x.OrganizationId == organizationId &&
                           x.Status == ContractStatus.Active &&
                           x.EffectiveDate <= currentDate &&
                           x.ExpirationDate > currentDate)
                .OrderBy(x => x.Name)
                .ToListAsync();
        }

        public async Task<IEnumerable<Contract>> GetExpiringContractsAsync(Guid organizationId, DateTime beforeDate)
        {
            return await _context.Contracts
                .Where(x => x.OrganizationId == organizationId &&
                           x.Status == ContractStatus.Active &&
                           x.ExpirationDate <= beforeDate)
                .OrderBy(x => x.ExpirationDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Contract>> GetContractsByDateRangeAsync(Guid organizationId, DateTime fromDate, DateTime toDate)
        {
            return await _context.Contracts
                .Where(x => x.OrganizationId == organizationId &&
                           x.CreatedAt >= fromDate &&
                           x.CreatedAt <= toDate)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Contract>> GetContractsForReviewAsync(Guid organizationId, DateTime beforeDate)
        {
            return await _context.Contracts
                .Where(x => x.OrganizationId == organizationId &&
                           x.Status == ContractStatus.Active &&
                           x.NextReviewDate.HasValue &&
                           x.NextReviewDate <= beforeDate)
                .OrderBy(x => x.NextReviewDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Contract>> GetAmendmentsAsync(Guid parentContractId, Guid organizationId)
        {
            return await _context.Contracts
                .Where(x => x.ParentContractId == parentContractId && x.OrganizationId == organizationId)
                .OrderBy(x => x.Version)
                .ToListAsync();
        }

        public async Task<Contract?> GetActiveContractForCustomerAsync(Guid customerId, Guid organizationId)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.Contracts
                .Include(x => x.Rates)
                .Include(x => x.Discounts)
                .Include(x => x.Surcharges)
                .Include(x => x.Commitments)
                .Where(x => x.CustomerId == customerId &&
                           x.OrganizationId == organizationId &&
                           x.Status == ContractStatus.Active &&
                           x.EffectiveDate <= currentDate &&
                           x.ExpirationDate > currentDate)
                .OrderByDescending(x => x.Version)
                .FirstOrDefaultAsync();
        }

        public async Task<Contract?> GetActiveContractForShipperAsync(Guid shipperId, Guid organizationId)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.Contracts
                .Include(x => x.Rates)
                .Include(x => x.Discounts)
                .Include(x => x.Surcharges)
                .Include(x => x.Commitments)
                .Where(x => x.ShipperId == shipperId &&
                           x.OrganizationId == organizationId &&
                           x.Status == ContractStatus.Active &&
                           x.EffectiveDate <= currentDate &&
                           x.ExpirationDate > currentDate)
                .OrderByDescending(x => x.Version)
                .FirstOrDefaultAsync();
        }

        public async Task<bool> ExistsAsync(Guid id, Guid organizationId)
        {
            return await _context.Contracts
                .AnyAsync(x => x.Id == id && x.OrganizationId == organizationId);
        }

        public async Task<bool> ContractNumberExistsAsync(string contractNumber, Guid organizationId)
        {
            return await _context.Contracts
                .AnyAsync(x => x.ContractNumber == contractNumber && x.OrganizationId == organizationId);
        }

        public async Task<string> GenerateContractNumberAsync(Guid organizationId)
        {
            var today = DateTime.UtcNow;
            var prefix = $"C{today:yyyyMMdd}";
            
            var lastContract = await _context.Contracts
                .Where(x => x.OrganizationId == organizationId && x.ContractNumber.StartsWith(prefix))
                .OrderByDescending(x => x.ContractNumber)
                .FirstOrDefaultAsync();

            if (lastContract == null)
            {
                return $"{prefix}-001";
            }

            var lastNumber = lastContract.ContractNumber.Split('-').LastOrDefault();
            if (int.TryParse(lastNumber, out var number))
            {
                return $"{prefix}-{(number + 1):D3}";
            }

            return $"{prefix}-001";
        }

        public async Task AddAsync(Contract contract)
        {
            await _context.Contracts.AddAsync(contract);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Contract contract)
        {
            _context.Contracts.Update(contract);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Contract contract)
        {
            _context.Contracts.Remove(contract);
            await _context.SaveChangesAsync();
        }

        public async Task<int> CountByOrganizationAsync(Guid organizationId)
        {
            return await _context.Contracts
                .CountAsync(x => x.OrganizationId == organizationId);
        }

        public async Task<int> CountByStatusAsync(ContractStatus status, Guid organizationId)
        {
            return await _context.Contracts
                .CountAsync(x => x.Status == status && x.OrganizationId == organizationId);
        }

        public async Task<int> CountActiveByOrganizationAsync(Guid organizationId)
        {
            var currentDate = DateTime.UtcNow;
            return await _context.Contracts
                .CountAsync(x => x.OrganizationId == organizationId &&
                                x.Status == ContractStatus.Active &&
                                x.EffectiveDate <= currentDate &&
                                x.ExpirationDate > currentDate);
        }
    }
}
