using Microsoft.EntityFrameworkCore;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Repositories;
using PricingManagement.Infrastructure.Persistence;

namespace PricingManagement.Infrastructure.Repositories
{
    public class QuoteRepository : IQuoteRepository
    {
        private readonly PricingManagementDbContext _context;

        public QuoteRepository(PricingManagementDbContext context)
        {
            _context = context;
        }

        public async Task<Quote?> GetByIdAsync(Guid id)
        {
            return await _context.Quotes
                .Include(x => x.LineItems)
                .Include(x => x.Surcharges)
                .Include(x => x.Discounts)
                .Include(x => x.Taxes)
                .FirstOrDefaultAsync(x => x.Id == id);
        }

        public async Task<Quote?> GetByIdAsync(Guid id, Guid organizationId)
        {
            return await _context.Quotes
                .Include(x => x.LineItems)
                .Include(x => x.Surcharges)
                .Include(x => x.Discounts)
                .Include(x => x.Taxes)
                .FirstOrDefaultAsync(x => x.Id == id && x.OrganizationId == organizationId);
        }

        public async Task<Quote?> GetByQuoteNumberAsync(string quoteNumber, Guid organizationId)
        {
            return await _context.Quotes
                .Include(x => x.LineItems)
                .Include(x => x.Surcharges)
                .Include(x => x.Discounts)
                .Include(x => x.Taxes)
                .FirstOrDefaultAsync(x => x.QuoteNumber == quoteNumber && x.OrganizationId == organizationId);
        }

        public async Task<IEnumerable<Quote>> GetByOrganizationAsync(Guid organizationId)
        {
            return await _context.Quotes
                .Where(x => x.OrganizationId == organizationId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Quote>> GetByCustomerAsync(Guid customerId, Guid organizationId)
        {
            return await _context.Quotes
                .Where(x => x.CustomerId == customerId && x.OrganizationId == organizationId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Quote>> GetByShipperAsync(Guid shipperId, Guid organizationId)
        {
            return await _context.Quotes
                .Where(x => x.ShipperId == shipperId && x.OrganizationId == organizationId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Quote>> GetByStatusAsync(QuoteStatus status, Guid organizationId)
        {
            return await _context.Quotes
                .Where(x => x.Status == status && x.OrganizationId == organizationId)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Quote>> GetExpiringQuotesAsync(Guid organizationId, DateTime beforeDate)
        {
            return await _context.Quotes
                .Where(x => x.OrganizationId == organizationId &&
                           x.Status == QuoteStatus.Sent &&
                           x.ExpirationDate <= beforeDate)
                .OrderBy(x => x.ExpirationDate)
                .ToListAsync();
        }

        public async Task<IEnumerable<Quote>> GetQuotesByDateRangeAsync(Guid organizationId, DateTime fromDate, DateTime toDate)
        {
            return await _context.Quotes
                .Where(x => x.OrganizationId == organizationId &&
                           x.CreatedAt >= fromDate &&
                           x.CreatedAt <= toDate)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();
        }

        public async Task<IEnumerable<Quote>> GetConvertedQuotesAsync(Guid organizationId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.Quotes
                .Where(x => x.OrganizationId == organizationId && x.Status == QuoteStatus.Converted);

            if (fromDate.HasValue)
                query = query.Where(x => x.ConvertedToOrderAt >= fromDate);

            if (toDate.HasValue)
                query = query.Where(x => x.ConvertedToOrderAt <= toDate);

            return await query.OrderByDescending(x => x.ConvertedToOrderAt).ToListAsync();
        }

        public async Task<bool> ExistsAsync(Guid id, Guid organizationId)
        {
            return await _context.Quotes
                .AnyAsync(x => x.Id == id && x.OrganizationId == organizationId);
        }

        public async Task<bool> QuoteNumberExistsAsync(string quoteNumber, Guid organizationId)
        {
            return await _context.Quotes
                .AnyAsync(x => x.QuoteNumber == quoteNumber && x.OrganizationId == organizationId);
        }

        public async Task<string> GenerateQuoteNumberAsync(Guid organizationId)
        {
            var today = DateTime.UtcNow;
            var prefix = $"Q{today:yyyyMMdd}";
            
            var lastQuote = await _context.Quotes
                .Where(x => x.OrganizationId == organizationId && x.QuoteNumber.StartsWith(prefix))
                .OrderByDescending(x => x.QuoteNumber)
                .FirstOrDefaultAsync();

            if (lastQuote == null)
            {
                return $"{prefix}-001";
            }

            var lastNumber = lastQuote.QuoteNumber.Split('-').LastOrDefault();
            if (int.TryParse(lastNumber, out var number))
            {
                return $"{prefix}-{(number + 1):D3}";
            }

            return $"{prefix}-001";
        }

        public async Task AddAsync(Quote quote)
        {
            await _context.Quotes.AddAsync(quote);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(Quote quote)
        {
            _context.Quotes.Update(quote);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(Quote quote)
        {
            _context.Quotes.Remove(quote);
            await _context.SaveChangesAsync();
        }

        public async Task<int> CountByOrganizationAsync(Guid organizationId)
        {
            return await _context.Quotes
                .CountAsync(x => x.OrganizationId == organizationId);
        }

        public async Task<int> CountByStatusAsync(QuoteStatus status, Guid organizationId)
        {
            return await _context.Quotes
                .CountAsync(x => x.Status == status && x.OrganizationId == organizationId);
        }

        public async Task<decimal> GetTotalQuoteValueAsync(Guid organizationId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.Quotes
                .Where(x => x.OrganizationId == organizationId);

            if (fromDate.HasValue)
                query = query.Where(x => x.CreatedAt >= fromDate);

            if (toDate.HasValue)
                query = query.Where(x => x.CreatedAt <= toDate);

            return await query.SumAsync(x => x.TotalAmount.Amount);
        }

        public async Task<decimal> GetConversionRateAsync(Guid organizationId, DateTime? fromDate = null, DateTime? toDate = null)
        {
            var query = _context.Quotes
                .Where(x => x.OrganizationId == organizationId);

            if (fromDate.HasValue)
                query = query.Where(x => x.CreatedAt >= fromDate);

            if (toDate.HasValue)
                query = query.Where(x => x.CreatedAt <= toDate);

            var totalQuotes = await query.CountAsync();
            var convertedQuotes = await query.CountAsync(x => x.Status == QuoteStatus.Converted);

            return totalQuotes > 0 ? (decimal)convertedQuotes / totalQuotes * 100 : 0;
        }
    }
}
