using Microsoft.AspNetCore.Http;
using PricingManagement.Application.Common;
using System.Security.Claims;

namespace PricingManagement.Infrastructure.Services
{
    public class CurrentUserService : ICurrentUserService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public CurrentUserService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public Guid? UserId
        {
            get
            {
                var userIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                return Guid.TryParse(userIdClaim, out var userId) ? userId : null;
            }
        }

        public string? Username
        {
            get
            {
                return _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Name)?.Value;
            }
        }

        public Guid? OrganizationId
        {
            get
            {
                var orgIdClaim = _httpContextAccessor.HttpContext?.User?.FindFirst("OrganizationId")?.Value;
                return Guid.TryParse(orgIdClaim, out var orgId) ? orgId : null;
            }
        }

        public string? Email
        {
            get
            {
                return _httpContextAccessor.HttpContext?.User?.FindFirst(ClaimTypes.Email)?.Value;
            }
        }

        public bool IsAuthenticated
        {
            get
            {
                return _httpContextAccessor.HttpContext?.User?.Identity?.IsAuthenticated ?? false;
            }
        }

        public bool HasPermission(string permission)
        {
            var permissions = _httpContextAccessor.HttpContext?.User?.FindAll("Permission")?.Select(c => c.Value);
            return permissions?.Contains(permission) ?? false;
        }

        public bool IsInRole(string role)
        {
            return _httpContextAccessor.HttpContext?.User?.IsInRole(role) ?? false;
        }
    }
}
