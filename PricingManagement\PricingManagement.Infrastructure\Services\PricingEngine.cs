using Microsoft.Extensions.Logging;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Services;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Repositories;
using PricingManagement.Domain.ValueObjects;

namespace PricingManagement.Infrastructure.Services
{
    public class PricingEngine : IPricingEngine
    {
        private readonly IPricingRuleRepository _pricingRuleRepository;
        private readonly IContractRepository _contractRepository;
        private readonly IRateCalculationService _rateCalculationService;
        private readonly IDiscountService _discountService;
        private readonly ISurchargeService _surchargeService;
        private readonly ITaxCalculationService _taxCalculationService;
        private readonly ICurrencyConversionService _currencyConversionService;
        private readonly ILogger<PricingEngine> _logger;

        public PricingEngine(
            IPricingRuleRepository pricingRuleRepository,
            IContractRepository contractRepository,
            IRateCalculationService rateCalculationService,
            IDiscountService discountService,
            ISurchargeService surchargeService,
            ITaxCalculationService taxCalculationService,
            ICurrencyConversionService currencyConversionService,
            ILogger<PricingEngine> logger)
        {
            _pricingRuleRepository = pricingRuleRepository;
            _contractRepository = contractRepository;
            _rateCalculationService = rateCalculationService;
            _discountService = discountService;
            _surchargeService = surchargeService;
            _taxCalculationService = taxCalculationService;
            _currencyConversionService = currencyConversionService;
            _logger = logger;
        }

        public async Task<PricingResultDto> CalculateRateAsync(RateCalculationRequestDto request)
        {
            _logger.LogInformation("Starting rate calculation for organization {OrganizationId}", request.OrganizationId);

            var result = new PricingResultDto
            {
                CalculationId = Guid.NewGuid().ToString(),
                CalculatedAt = DateTime.UtcNow
            };

            try
            {
                // Get applicable pricing rules
                var applicableRules = await GetApplicableRulesInternalAsync(request);
                
                // Calculate base rate using applicable rules
                result.BaseRate = await _rateCalculationService.CalculateBaseRateAsync(request, applicableRules);
                
                // Calculate billable weight
                result.BillableWeight = CalculateBillableWeight(request.PackageWeight, request.PackageDimensions);
                result.DimensionalWeight = request.PackageDimensions?.CalculateDimensionalWeight(139, WeightUnit.Pounds);

                // Apply surcharges
                var surcharges = await _surchargeService.CalculateSurchargesAsync(request);
                result.Surcharges = surcharges.ToList();
                result.TotalSurcharges = surcharges.Aggregate(Money.Zero(request.Currency), (sum, s) => sum.Add(s.Amount));

                // Apply discounts
                var discounts = await _discountService.CalculateDiscountsAsync(request, result.BaseRate);
                result.Discounts = discounts.ToList();
                result.TotalDiscounts = discounts.Aggregate(Money.Zero(request.Currency), (sum, d) => sum.Add(d.Amount));

                // Calculate taxes
                var subtotal = result.BaseRate.Add(result.TotalSurcharges).Subtract(result.TotalDiscounts);
                var taxes = await _taxCalculationService.CalculateTaxesAsync(request, subtotal);
                result.Taxes = taxes.ToList();
                result.TotalTaxes = taxes.Aggregate(Money.Zero(request.Currency), (sum, t) => sum.Add(t.Amount));

                // Calculate total amount
                result.TotalAmount = subtotal.Add(result.TotalTaxes);

                // Add applied rules information
                result.AppliedRules = applicableRules.Select(r => $"{r.Name} (Priority: {r.Priority})").ToList();

                _logger.LogInformation("Rate calculation completed. Total: {TotalAmount}", result.TotalAmount);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating rate for organization {OrganizationId}", request.OrganizationId);
                throw;
            }
        }

        public async Task<IEnumerable<CarrierRateComparisonDto>> CompareCarrierRatesAsync(RateCalculationRequestDto request)
        {
            _logger.LogInformation("Starting carrier rate comparison for organization {OrganizationId}", request.OrganizationId);

            var comparisons = new List<CarrierRateComparisonDto>();

            try
            {
                // Get our internal rate
                var internalRate = await CalculateRateAsync(request);
                comparisons.Add(new CarrierRateComparisonDto
                {
                    CarrierName = "TriTrackz",
                    ServiceLevel = request.ServiceType,
                    PricingResult = internalRate,
                    IsRecommended = true,
                    RecommendationReason = "Best value"
                });

                // TODO: Add external carrier integrations
                // This would integrate with carrier APIs to get live rates

                return comparisons.OrderBy(c => c.PricingResult.TotalAmount.Amount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error comparing carrier rates for organization {OrganizationId}", request.OrganizationId);
                throw;
            }
        }

        public async Task<PricingResultDto> ApplyContractPricingAsync(RateCalculationRequestDto request, Guid contractId)
        {
            _logger.LogInformation("Applying contract pricing {ContractId} for organization {OrganizationId}", 
                contractId, request.OrganizationId);

            try
            {
                var contract = await _contractRepository.GetByIdAsync(contractId, request.OrganizationId);
                if (contract == null)
                {
                    throw new ArgumentException($"Contract {contractId} not found");
                }

                if (!contract.IsActive())
                {
                    throw new ArgumentException($"Contract {contractId} is not active");
                }

                // Apply contract-specific pricing logic
                var result = await CalculateRateAsync(request);
                
                // Apply contract rates and discounts
                await ApplyContractRatesAsync(result, contract, request);
                await ApplyContractDiscountsAsync(result, contract, request);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying contract pricing {ContractId}", contractId);
                throw;
            }
        }

        public async Task<PricingResultDto> ApplyPromotionalPricingAsync(RateCalculationRequestDto request, string promotionCode)
        {
            _logger.LogInformation("Applying promotional pricing {PromotionCode} for organization {OrganizationId}", 
                promotionCode, request.OrganizationId);

            try
            {
                // Get base rate
                var result = await CalculateRateAsync(request);

                // TODO: Implement promotion code validation and application
                // This would check promotion validity and apply appropriate discounts

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error applying promotional pricing {PromotionCode}", promotionCode);
                throw;
            }
        }

        public async Task<PricingSimulationResultDto> SimulatePricingAsync(PricingSimulationRequestDto request)
        {
            _logger.LogInformation("Starting pricing simulation for organization {OrganizationId}", 
                request.BaseRequest.OrganizationId);

            try
            {
                var result = new PricingSimulationResultDto
                {
                    BaseResult = await CalculateRateAsync(request.BaseRequest)
                };

                // Run scenarios
                foreach (var scenario in request.Scenarios)
                {
                    var scenarioRequest = ApplyScenarioParameters(request.BaseRequest, scenario);
                    var scenarioResult = await CalculateRateAsync(scenarioRequest);
                    
                    var variance = scenarioResult.TotalAmount.Subtract(result.BaseResult.TotalAmount);
                    var variancePercentage = result.BaseResult.TotalAmount.Amount > 0 
                        ? (variance.Amount / result.BaseResult.TotalAmount.Amount) * 100 
                        : 0;

                    result.ScenarioResults.Add(new ScenarioResultDto
                    {
                        ScenarioName = scenario.Name,
                        Result = scenarioResult,
                        VarianceAmount = variance,
                        VariancePercentage = variancePercentage
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error simulating pricing");
                throw;
            }
        }

        public async Task<IEnumerable<PricingRuleDto>> GetApplicableRulesAsync(RateCalculationRequestDto request)
        {
            var rules = await GetApplicableRulesInternalAsync(request);
            return rules.Select(r => new PricingRuleDto
            {
                Id = r.Id,
                Name = r.Name,
                Description = r.Description,
                RuleType = r.RuleType,
                Priority = r.Priority,
                EffectiveDate = r.EffectiveDate,
                ExpirationDate = r.ExpirationDate
            });
        }

        public async Task<bool> ValidateRateCalculationAsync(RateCalculationRequestDto request)
        {
            try
            {
                // Validate required fields
                if (request.OrganizationId == Guid.Empty)
                    return false;

                if (string.IsNullOrWhiteSpace(request.ServiceType))
                    return false;

                if (string.IsNullOrWhiteSpace(request.OriginAddress))
                    return false;

                if (string.IsNullOrWhiteSpace(request.DestinationAddress))
                    return false;

                if (request.PackageWeight.Value <= 0)
                    return false;

                // Check if we have applicable pricing rules
                var applicableRules = await GetApplicableRulesInternalAsync(request);
                return applicableRules.Any();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating rate calculation request");
                return false;
            }
        }

        private async Task<IEnumerable<PricingRule>> GetApplicableRulesInternalAsync(RateCalculationRequestDto request)
        {
            return await _pricingRuleRepository.GetApplicableRulesAsync(
                request.OrganizationId,
                request.ServiceType,
                request.OriginZoneId,
                request.DestinationZoneId,
                request.PackageWeight.Value,
                request.Distance,
                request.DeclaredValue,
                request.CustomerSegment,
                request.ShipperType);
        }

        private Weight CalculateBillableWeight(Weight packageWeight, Dimensions? dimensions)
        {
            if (dimensions == null)
                return packageWeight;

            var dimensionalWeight = dimensions.CalculateDimensionalWeight(139, WeightUnit.Pounds);
            return packageWeight > dimensionalWeight ? packageWeight : dimensionalWeight;
        }

        private async Task ApplyContractRatesAsync(PricingResultDto result, Contract contract, RateCalculationRequestDto request)
        {
            // Find applicable contract rate
            var applicableRate = contract.Rates.FirstOrDefault(r => 
                r.IsApplicable(DateTime.UtcNow, request.ServiceType, request.OriginZoneId, 
                              request.DestinationZoneId, request.PackageWeight.Value));

            if (applicableRate != null)
            {
                result.BaseRate = applicableRate.Rate;
                result.AppliedRules.Add($"Contract Rate: {contract.Name}");
            }
        }

        private async Task ApplyContractDiscountsAsync(PricingResultDto result, Contract contract, RateCalculationRequestDto request)
        {
            foreach (var contractDiscount in contract.Discounts)
            {
                if (contractDiscount.IsApplicable(DateTime.UtcNow, null)) // TODO: Add volume calculation
                {
                    var discountAmount = contractDiscount.Percentage.HasValue
                        ? result.BaseRate.ApplyPercentage(contractDiscount.Percentage.Value)
                        : contractDiscount.FixedAmount ?? Money.Zero(request.Currency);

                    result.Discounts.Add(new DiscountDto
                    {
                        DiscountType = contractDiscount.DiscountType,
                        Description = contractDiscount.Description,
                        Amount = discountAmount,
                        Percentage = contractDiscount.Percentage,
                        CalculationBasis = "Contract"
                    });
                }
            }

            result.TotalDiscounts = result.Discounts.Aggregate(Money.Zero(request.Currency), (sum, d) => sum.Add(d.Amount));
        }

        private RateCalculationRequestDto ApplyScenarioParameters(RateCalculationRequestDto baseRequest, PricingScenarioDto scenario)
        {
            var scenarioRequest = new RateCalculationRequestDto
            {
                OrganizationId = baseRequest.OrganizationId,
                CustomerId = baseRequest.CustomerId,
                ShipperId = baseRequest.ShipperId,
                CustomerSegment = baseRequest.CustomerSegment,
                ShipperType = baseRequest.ShipperType,
                ServiceType = baseRequest.ServiceType,
                OriginAddress = baseRequest.OriginAddress,
                DestinationAddress = baseRequest.DestinationAddress,
                OriginZoneId = baseRequest.OriginZoneId,
                DestinationZoneId = baseRequest.DestinationZoneId,
                Distance = baseRequest.Distance,
                PackageWeight = baseRequest.PackageWeight,
                PackageDimensions = baseRequest.PackageDimensions,
                DeclaredValue = baseRequest.DeclaredValue,
                SpecialServices = baseRequest.SpecialServices,
                Currency = baseRequest.Currency,
                ShipDate = baseRequest.ShipDate,
                IncludeSurcharges = baseRequest.IncludeSurcharges,
                IncludeDiscounts = baseRequest.IncludeDiscounts,
                IncludeTaxes = baseRequest.IncludeTaxes,
                AdditionalAttributes = new Dictionary<string, object>(baseRequest.AdditionalAttributes)
            };

            // Apply scenario parameters
            foreach (var parameter in scenario.Parameters)
            {
                switch (parameter.Key.ToLower())
                {
                    case "weight":
                        if (parameter.Value is decimal weightValue)
                            scenarioRequest.PackageWeight = new Weight(weightValue, scenarioRequest.PackageWeight.Unit);
                        break;
                    case "distance":
                        if (parameter.Value is decimal distanceValue)
                            scenarioRequest.Distance = distanceValue;
                        break;
                    case "declaredvalue":
                        if (parameter.Value is decimal valueAmount)
                            scenarioRequest.DeclaredValue = valueAmount;
                        break;
                    // Add more scenario parameters as needed
                }
            }

            return scenarioRequest;
        }
    }
}
