using Microsoft.Extensions.Logging;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Services;
using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using System.Text.Json;

namespace PricingManagement.Infrastructure.Services
{
    public class RateCalculationService : IRateCalculationService
    {
        private readonly ILogger<RateCalculationService> _logger;

        public RateCalculationService(ILogger<RateCalculationService> logger)
        {
            _logger = logger;
        }

        public async Task<Money> CalculateBaseRateAsync(RateCalculationRequestDto request, IEnumerable<PricingRule> applicableRules)
        {
            _logger.LogInformation("Calculating base rate using {RuleCount} applicable rules", applicableRules.Count());

            var baseRate = Money.Zero(request.Currency);
            var rulesApplied = 0;

            // Apply rules in priority order
            foreach (var rule in applicableRules.OrderBy(r => r.Priority))
            {
                try
                {
                    var ruleRate = await CalculateRateByRuleAsync(request, rule);
                    
                    // For now, we'll use the highest priority rule that produces a rate
                    if (ruleRate.Amount > 0 && rulesApplied == 0)
                    {
                        baseRate = ruleRate;
                        rulesApplied++;
                        _logger.LogInformation("Applied rule {RuleName} with rate {Rate}", rule.Name, ruleRate);
                        break; // Use first applicable rule for now
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to apply pricing rule {RuleName}", rule.Name);
                }
            }

            // If no rules produced a rate, use a default calculation
            if (baseRate.Amount == 0)
            {
                baseRate = CalculateDefaultRate(request);
                _logger.LogInformation("No applicable rules found, using default rate {Rate}", baseRate);
            }

            return baseRate;
        }

        public async Task<Money> CalculateRateByRuleAsync(RateCalculationRequestDto request, PricingRule rule)
        {
            return rule.RuleType switch
            {
                PricingRuleType.BaseRate => await CalculateBaseRateByRuleAsync(request, rule),
                PricingRuleType.WeightBased => await CalculateWeightBasedRateAsync(request, rule),
                PricingRuleType.DistanceBased => await CalculateDistanceBasedRateAsync(request, rule),
                PricingRuleType.ZoneBased => await CalculateZoneBasedRateAsync(request, rule),
                PricingRuleType.DimensionalWeight => await CalculateDimensionalWeightRateAsync(request, rule),
                PricingRuleType.ServiceLevel => await CalculateServiceLevelRateAsync(request, rule),
                _ => Money.Zero(request.Currency)
            };
        }

        public async Task<Money> CalculateWeightBasedRateAsync(RateCalculationRequestDto request, PricingRule rule)
        {
            var weight = request.PackageWeight.Value;
            var config = ParseRuleConfiguration(rule.RuleConfiguration);

            if (config.TryGetValue("ratePerPound", out var ratePerPoundObj) && 
                ratePerPoundObj is JsonElement ratePerPoundElement &&
                ratePerPoundElement.TryGetDecimal(out var ratePerPound))
            {
                var baseAmount = new Money(ratePerPound * weight, request.Currency);
                
                // Apply tiered pricing if tiers exist
                if (rule.Tiers.Any())
                {
                    return await ApplyTieredPricingAsync(weight, rule.Tiers, baseAmount);
                }

                return baseAmount;
            }

            return Money.Zero(request.Currency);
        }

        public async Task<Money> CalculateDistanceBasedRateAsync(RateCalculationRequestDto request, PricingRule rule)
        {
            if (!request.Distance.HasValue)
                return Money.Zero(request.Currency);

            var distance = request.Distance.Value;
            var config = ParseRuleConfiguration(rule.RuleConfiguration);

            if (config.TryGetValue("ratePerMile", out var ratePerMileObj) && 
                ratePerMileObj is JsonElement ratePerMileElement &&
                ratePerMileElement.TryGetDecimal(out var ratePerMile))
            {
                var baseAmount = new Money(ratePerMile * distance, request.Currency);
                
                // Apply tiered pricing if tiers exist
                if (rule.Tiers.Any())
                {
                    return await ApplyTieredPricingAsync(distance, rule.Tiers, baseAmount);
                }

                return baseAmount;
            }

            return Money.Zero(request.Currency);
        }

        public async Task<Money> CalculateZoneBasedRateAsync(RateCalculationRequestDto request, PricingRule rule)
        {
            var config = ParseRuleConfiguration(rule.RuleConfiguration);

            // Create zone pair key
            var zonePair = $"{request.OriginZoneId}-{request.DestinationZoneId}";
            
            if (config.TryGetValue("zoneRates", out var zoneRatesObj) && 
                zoneRatesObj is JsonElement zoneRatesElement)
            {
                if (zoneRatesElement.TryGetProperty(zonePair, out var rateElement) &&
                    rateElement.TryGetDecimal(out var rate))
                {
                    return new Money(rate, request.Currency);
                }
            }

            return Money.Zero(request.Currency);
        }

        public async Task<Money> CalculateDimensionalWeightRateAsync(RateCalculationRequestDto request, PricingRule rule)
        {
            if (request.PackageDimensions == null)
                return Money.Zero(request.Currency);

            var config = ParseRuleConfiguration(rule.RuleConfiguration);
            
            if (config.TryGetValue("divisor", out var divisorObj) && 
                divisorObj is JsonElement divisorElement &&
                divisorElement.TryGetDecimal(out var divisor))
            {
                var dimWeight = request.PackageDimensions.CalculateDimensionalWeight(divisor, WeightUnit.Pounds);
                var billableWeight = request.PackageWeight > dimWeight ? request.PackageWeight : dimWeight;

                if (config.TryGetValue("ratePerPound", out var ratePerPoundObj) && 
                    ratePerPoundObj is JsonElement ratePerPoundElement &&
                    ratePerPoundElement.TryGetDecimal(out var ratePerPound))
                {
                    return new Money(ratePerPound * billableWeight.Value, request.Currency);
                }
            }

            return Money.Zero(request.Currency);
        }

        public async Task<Money> CalculateServiceLevelRateAsync(RateCalculationRequestDto request, PricingRule rule)
        {
            var config = ParseRuleConfiguration(rule.RuleConfiguration);

            if (config.TryGetValue("serviceLevelRates", out var serviceLevelRatesObj) && 
                serviceLevelRatesObj is JsonElement serviceLevelRatesElement)
            {
                if (serviceLevelRatesElement.TryGetProperty(request.ServiceType, out var rateElement) &&
                    rateElement.TryGetDecimal(out var rate))
                {
                    return new Money(rate, request.Currency);
                }
            }

            return Money.Zero(request.Currency);
        }

        public async Task<Money> ApplyTieredPricingAsync(decimal value, IEnumerable<PricingRuleTier> tiers, Money baseAmount)
        {
            var applicableTier = tiers.FirstOrDefault(t => t.IsInRange(value));
            
            if (applicableTier == null)
                return baseAmount;

            return applicableTier.RateType?.ToLower() switch
            {
                "fixed" => new Money(applicableTier.Rate, baseAmount.Currency),
                "percentage" => baseAmount.ApplyPercentage(applicableTier.Rate),
                "per_unit" => new Money(applicableTier.Rate * value, baseAmount.Currency),
                _ => baseAmount
            };
        }

        private async Task<Money> CalculateBaseRateByRuleAsync(RateCalculationRequestDto request, PricingRule rule)
        {
            var config = ParseRuleConfiguration(rule.RuleConfiguration);

            if (config.TryGetValue("baseRate", out var baseRateObj) && 
                baseRateObj is JsonElement baseRateElement &&
                baseRateElement.TryGetDecimal(out var baseRate))
            {
                return new Money(baseRate, request.Currency);
            }

            return Money.Zero(request.Currency);
        }

        private Money CalculateDefaultRate(RateCalculationRequestDto request)
        {
            // Simple default calculation: $1.50 per pound + $0.50 per mile
            var weightRate = request.PackageWeight.Value * 1.50m;
            var distanceRate = (request.Distance ?? 100) * 0.50m;
            var baseRate = Math.Max(weightRate + distanceRate, 10.00m); // Minimum $10

            return new Money(baseRate, request.Currency);
        }

        private Dictionary<string, object> ParseRuleConfiguration(string ruleConfiguration)
        {
            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(ruleConfiguration) 
                       ?? new Dictionary<string, object>();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to parse rule configuration: {Configuration}", ruleConfiguration);
                return new Dictionary<string, object>();
            }
        }
    }
}
