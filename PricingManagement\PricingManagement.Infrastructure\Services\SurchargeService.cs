using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Services;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;

namespace PricingManagement.Infrastructure.Services
{
    public class SurchargeService : ISurchargeService
    {
        private readonly IFuelSurchargeService _fuelSurchargeService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<SurchargeService> _logger;

        public SurchargeService(
            IFuelSurchargeService fuelSurchargeService,
            IConfiguration configuration,
            ILogger<SurchargeService> logger)
        {
            _fuelSurchargeService = fuelSurchargeService;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<IEnumerable<SurchargeDto>> CalculateSurchargesAsync(RateCalculationRequestDto request)
        {
            var surcharges = new List<SurchargeDto>();

            try
            {
                // Calculate fuel surcharge
                var fuelSurcharge = await CalculateFuelSurchargeAsync(request);
                if (fuelSurcharge.Amount.Amount > 0)
                    surcharges.Add(fuelSurcharge);

                // Calculate remote area surcharge
                var remoteAreaSurcharge = await CalculateRemoteAreaSurchargeAsync(request);
                if (remoteAreaSurcharge.Amount.Amount > 0)
                    surcharges.Add(remoteAreaSurcharge);

                // Calculate oversize surcharge
                var oversizeSurcharge = await CalculateOversizeSurchargeAsync(request);
                if (oversizeSurcharge.Amount.Amount > 0)
                    surcharges.Add(oversizeSurcharge);

                // Calculate special service surcharges
                foreach (var specialService in request.SpecialServices)
                {
                    var serviceSurcharge = await CalculateSpecialServiceSurcharge(request, specialService);
                    if (serviceSurcharge.Amount.Amount > 0)
                        surcharges.Add(serviceSurcharge);
                }

                return surcharges;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating surcharges for organization {OrganizationId}", request.OrganizationId);
                return new List<SurchargeDto>();
            }
        }

        public async Task<SurchargeDto> CalculateFuelSurchargeAsync(RateCalculationRequestDto request)
        {
            var surcharge = new SurchargeDto
            {
                SurchargeType = SurchargeType.FuelSurcharge,
                Description = "Fuel Surcharge",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                var fuelSurchargePercentage = await _fuelSurchargeService.GetCurrentFuelSurchargeAsync();
                if (fuelSurchargePercentage > 0)
                {
                    // Apply fuel surcharge as percentage of base rate
                    // For now, we'll use a mock base rate calculation
                    var estimatedBaseRate = new Money(request.PackageWeight.Value * 2.5m, request.Currency);
                    surcharge.Amount = estimatedBaseRate.ApplyPercentage(fuelSurchargePercentage);
                    surcharge.CalculationBasis = $"Fuel surcharge: {fuelSurchargePercentage}%";
                }

                return surcharge;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating fuel surcharge");
                return surcharge;
            }
        }

        public async Task<SurchargeDto> CalculateRemoteAreaSurchargeAsync(RateCalculationRequestDto request)
        {
            var surcharge = new SurchargeDto
            {
                SurchargeType = SurchargeType.RemoteArea,
                Description = "Remote Area Surcharge",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                // TODO: Integrate with zone service to determine if origin/destination is remote
                // For now, apply remote area surcharge based on distance
                if (request.Distance.HasValue && request.Distance > 500)
                {
                    var remoteAreaSurcharge = _configuration.GetValue<decimal>("PricingSettings:RemoteAreaSurcharge", 25.00m);
                    surcharge.Amount = new Money(remoteAreaSurcharge, request.Currency);
                    surcharge.CalculationBasis = "Distance > 500 miles";
                }

                return surcharge;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating remote area surcharge");
                return surcharge;
            }
        }

        public async Task<SurchargeDto> CalculateOversizeSurchargeAsync(RateCalculationRequestDto request)
        {
            var surcharge = new SurchargeDto
            {
                SurchargeType = SurchargeType.Oversize,
                Description = "Oversize Surcharge",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                if (request.PackageDimensions != null)
                {
                    var longestSide = request.PackageDimensions.LongestSide;
                    var lengthPlusGirth = request.PackageDimensions.LengthPlusGirth;

                    // Apply oversize surcharge based on dimensions
                    if (longestSide > 96 || lengthPlusGirth > 165)
                    {
                        var oversizeSurcharge = _configuration.GetValue<decimal>("PricingSettings:OversizeSurcharge", 75.00m);
                        surcharge.Amount = new Money(oversizeSurcharge, request.Currency);
                        surcharge.CalculationBasis = "Package exceeds standard size limits";
                    }
                }

                return surcharge;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating oversize surcharge");
                return surcharge;
            }
        }

        public async Task<SurchargeDto> CalculateHazmatSurchargeAsync(RateCalculationRequestDto request)
        {
            var surcharge = new SurchargeDto
            {
                SurchargeType = SurchargeType.Hazmat,
                Description = "Hazardous Materials Surcharge",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                if (request.SpecialServices.Contains("HAZMAT", StringComparer.OrdinalIgnoreCase))
                {
                    var hazmatSurcharge = _configuration.GetValue<decimal>("PricingSettings:HazmatSurcharge", 50.00m);
                    surcharge.Amount = new Money(hazmatSurcharge, request.Currency);
                    surcharge.CalculationBasis = "Hazardous materials handling";
                }

                return surcharge;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating hazmat surcharge");
                return surcharge;
            }
        }

        public async Task<SurchargeDto> CalculateResidentialSurchargeAsync(RateCalculationRequestDto request)
        {
            var surcharge = new SurchargeDto
            {
                SurchargeType = SurchargeType.Residential,
                Description = "Residential Delivery Surcharge",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                if (request.SpecialServices.Contains("RESIDENTIAL", StringComparer.OrdinalIgnoreCase))
                {
                    var residentialSurcharge = _configuration.GetValue<decimal>("PricingSettings:ResidentialSurcharge", 15.00m);
                    surcharge.Amount = new Money(residentialSurcharge, request.Currency);
                    surcharge.CalculationBasis = "Residential delivery";
                }

                return surcharge;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating residential surcharge");
                return surcharge;
            }
        }

        public async Task<SurchargeDto> CalculateSignatureRequiredSurchargeAsync(RateCalculationRequestDto request)
        {
            var surcharge = new SurchargeDto
            {
                SurchargeType = SurchargeType.SignatureRequired,
                Description = "Signature Required Surcharge",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                if (request.SpecialServices.Contains("SIGNATURE", StringComparer.OrdinalIgnoreCase))
                {
                    var signatureSurcharge = _configuration.GetValue<decimal>("PricingSettings:SignatureSurcharge", 8.00m);
                    surcharge.Amount = new Money(signatureSurcharge, request.Currency);
                    surcharge.CalculationBasis = "Signature required";
                }

                return surcharge;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating signature required surcharge");
                return surcharge;
            }
        }

        public async Task<SurchargeDto> CalculateInsuranceSurchargeAsync(RateCalculationRequestDto request)
        {
            var surcharge = new SurchargeDto
            {
                SurchargeType = SurchargeType.Insurance,
                Description = "Insurance Surcharge",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                if (request.DeclaredValue.HasValue && request.DeclaredValue > 100)
                {
                    var insuranceRate = _configuration.GetValue<decimal>("PricingSettings:InsuranceRate", 0.01m); // 1% of declared value
                    var insuranceAmount = request.DeclaredValue.Value * insuranceRate;
                    var minInsurance = _configuration.GetValue<decimal>("PricingSettings:MinInsuranceSurcharge", 5.00m);
                    
                    surcharge.Amount = new Money(Math.Max(insuranceAmount, minInsurance), request.Currency);
                    surcharge.CalculationBasis = $"Insurance for declared value: ${request.DeclaredValue:F2}";
                }

                return surcharge;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating insurance surcharge");
                return surcharge;
            }
        }

        public async Task<SurchargeDto> CalculateSpecialHandlingSurchargeAsync(RateCalculationRequestDto request)
        {
            var surcharge = new SurchargeDto
            {
                SurchargeType = SurchargeType.SpecialHandling,
                Description = "Special Handling Surcharge",
                Amount = Money.Zero(request.Currency)
            };

            try
            {
                if (request.SpecialServices.Contains("SPECIAL_HANDLING", StringComparer.OrdinalIgnoreCase))
                {
                    var specialHandlingSurcharge = _configuration.GetValue<decimal>("PricingSettings:SpecialHandlingSurcharge", 20.00m);
                    surcharge.Amount = new Money(specialHandlingSurcharge, request.Currency);
                    surcharge.CalculationBasis = "Special handling required";
                }

                return surcharge;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating special handling surcharge");
                return surcharge;
            }
        }

        private async Task<SurchargeDto> CalculateSpecialServiceSurcharge(RateCalculationRequestDto request, string specialService)
        {
            return specialService.ToUpper() switch
            {
                "HAZMAT" => await CalculateHazmatSurchargeAsync(request),
                "RESIDENTIAL" => await CalculateResidentialSurchargeAsync(request),
                "SIGNATURE" => await CalculateSignatureRequiredSurchargeAsync(request),
                "INSURANCE" => await CalculateInsuranceSurchargeAsync(request),
                "SPECIAL_HANDLING" => await CalculateSpecialHandlingSurchargeAsync(request),
                _ => new SurchargeDto
                {
                    SurchargeType = SurchargeType.Other,
                    Description = $"Special Service: {specialService}",
                    Amount = Money.Zero(request.Currency)
                }
            };
        }
    }
}
