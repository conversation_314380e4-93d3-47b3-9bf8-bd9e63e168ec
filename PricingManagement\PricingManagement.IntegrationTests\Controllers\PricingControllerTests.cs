using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using PricingManagement.Application.DTOs;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using PricingManagement.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using System.Security.Claims;
using System.Text;
using System.Text.Json;
using Xunit;

namespace PricingManagement.IntegrationTests.Controllers
{
    public class PricingControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public PricingControllerTests(TestWebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
            
            // Add test authentication headers
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer test-token");
            _client.DefaultRequestHeaders.Add("X-Organization-Id", TestDataSeeder.TestOrganizationId.ToString());
        }

        public async Task InitializeAsync()
        {
            await _factory.SeedTestDataAsync();
        }

        public async Task DisposeAsync()
        {
            await _factory.CleanupTestDataAsync();
        }

        [Fact]
        public async Task CalculateRate_WithValidRequest_ShouldReturnPricingResult()
        {
            // Arrange
            var request = new RateCalculationRequestDto
            {
                OrganizationId = TestDataSeeder.TestOrganizationId,
                ServiceType = "Ground",
                OriginAddress = "123 Main St, Los Angeles, CA 90210",
                DestinationAddress = "456 Oak Ave, New York, NY 10001",
                OriginZoneId = "Zone1",
                DestinationZoneId = "Zone2",
                Distance = 2500,
                PackageWeight = new Weight(25m, WeightUnit.Pounds),
                PackageDimensions = new Dimensions(12, 8, 6, DimensionUnit.Inches),
                DeclaredValue = 500m,
                SpecialServices = new List<string> { "SIGNATURE" },
                Currency = CurrencyCode.USD,
                ShipDate = DateTime.UtcNow.AddDays(1)
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/pricing/calculate", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<OperationResultDto<PricingResultDto>>();
            result.Should().NotBeNull();
            result!.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.BaseRate.Should().NotBeNull();
            result.Data.BaseRate.Amount.Should().BeGreaterThan(0);
            result.Data.TotalAmount.Should().NotBeNull();
            result.Data.TotalAmount.Amount.Should().BeGreaterThan(0);
        }

        [Fact]
        public async Task CalculateRate_WithInvalidRequest_ShouldReturnBadRequest()
        {
            // Arrange
            var request = new RateCalculationRequestDto
            {
                // Missing required fields
                ServiceType = "",
                OriginAddress = "",
                DestinationAddress = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/pricing/calculate", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task CompareRates_WithValidRequest_ShouldReturnCarrierComparison()
        {
            // Arrange
            var request = new RateCalculationRequestDto
            {
                OrganizationId = TestDataSeeder.TestOrganizationId,
                ServiceType = "Ground",
                OriginAddress = "123 Main St, Los Angeles, CA 90210",
                DestinationAddress = "456 Oak Ave, New York, NY 10001",
                PackageWeight = new Weight(25m, WeightUnit.Pounds),
                Currency = CurrencyCode.USD
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/pricing/compare", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<OperationResultDto<List<CarrierRateComparisonDto>>>();
            result.Should().NotBeNull();
            result!.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Should().NotBeEmpty();
        }

        [Fact]
        public async Task SimulatePricing_WithValidRequest_ShouldReturnSimulationResult()
        {
            // Arrange
            var baseRequest = new RateCalculationRequestDto
            {
                OrganizationId = TestDataSeeder.TestOrganizationId,
                ServiceType = "Ground",
                OriginAddress = "123 Main St, Los Angeles, CA 90210",
                DestinationAddress = "456 Oak Ave, New York, NY 10001",
                PackageWeight = new Weight(25m, WeightUnit.Pounds),
                Currency = CurrencyCode.USD
            };

            var simulationRequest = new PricingSimulationRequestDto
            {
                BaseRequest = baseRequest,
                Scenarios = new List<PricingScenarioDto>
                {
                    new PricingScenarioDto
                    {
                        Name = "Heavy Package",
                        Parameters = new Dictionary<string, object>
                        {
                            ["weight"] = 50m
                        }
                    },
                    new PricingScenarioDto
                    {
                        Name = "Long Distance",
                        Parameters = new Dictionary<string, object>
                        {
                            ["distance"] = 3000m
                        }
                    }
                }
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/pricing/simulate", simulationRequest);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<OperationResultDto<PricingSimulationResultDto>>();
            result.Should().NotBeNull();
            result!.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.BaseResult.Should().NotBeNull();
            result.Data.ScenarioResults.Should().HaveCount(2);
        }

        [Fact]
        public async Task ValidateRateCalculation_WithValidRequest_ShouldReturnTrue()
        {
            // Arrange
            var request = new RateCalculationRequestDto
            {
                OrganizationId = TestDataSeeder.TestOrganizationId,
                ServiceType = "Ground",
                OriginAddress = "123 Main St, Los Angeles, CA 90210",
                DestinationAddress = "456 Oak Ave, New York, NY 10001",
                PackageWeight = new Weight(25m, WeightUnit.Pounds),
                Currency = CurrencyCode.USD
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/pricing/validate", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<OperationResultDto<bool>>();
            result.Should().NotBeNull();
            result!.IsSuccess.Should().BeTrue();
            result.Data.Should().BeTrue();
        }

        [Fact]
        public async Task ValidateRateCalculation_WithInvalidRequest_ShouldReturnFalse()
        {
            // Arrange
            var request = new RateCalculationRequestDto
            {
                OrganizationId = Guid.Empty, // Invalid organization ID
                ServiceType = "",
                OriginAddress = "",
                DestinationAddress = "",
                PackageWeight = new Weight(0, WeightUnit.Pounds) // Invalid weight
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/pricing/validate", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<OperationResultDto<bool>>();
            result.Should().NotBeNull();
            result!.IsSuccess.Should().BeTrue();
            result.Data.Should().BeFalse();
        }

        [Fact]
        public async Task GetApplicableRules_WithValidRequest_ShouldReturnRules()
        {
            // Arrange
            var request = new RateCalculationRequestDto
            {
                OrganizationId = TestDataSeeder.TestOrganizationId,
                ServiceType = "Ground",
                OriginAddress = "123 Main St, Los Angeles, CA 90210",
                DestinationAddress = "456 Oak Ave, New York, NY 10001",
                PackageWeight = new Weight(25m, WeightUnit.Pounds),
                Currency = CurrencyCode.USD
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/pricing/applicable-rules", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.OK);
            
            var result = await response.Content.ReadFromJsonAsync<OperationResultDto<List<PricingRuleDto>>>();
            result.Should().NotBeNull();
            result!.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.Should().NotBeEmpty();
        }
    }
}
