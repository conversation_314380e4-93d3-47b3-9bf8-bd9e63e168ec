using FluentAssertions;
using PricingManagement.Application.DTOs;
using PricingManagement.Application.Quotes.Commands.CreateQuote;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using PricingManagement.IntegrationTests.Infrastructure;
using System.Net;
using System.Net.Http.Json;
using Xunit;

namespace PricingManagement.IntegrationTests.Controllers
{
    public class QuotesControllerTests : IClassFixture<TestWebApplicationFactory<Program>>, IAsyncLifetime
    {
        private readonly TestWebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public QuotesControllerTests(TestWebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
            
            // Add test authentication headers
            _client.DefaultRequestHeaders.Add("Authorization", "Bearer test-token");
            _client.DefaultRequestHeaders.Add("X-Organization-Id", TestDataSeeder.TestOrganizationId.ToString());
        }

        public async Task InitializeAsync()
        {
            await _factory.SeedTestDataAsync();
        }

        public async Task DisposeAsync()
        {
            await _factory.CleanupTestDataAsync();
        }

        [Fact]
        public async Task CreateQuote_WithValidRequest_ShouldReturnCreatedQuote()
        {
            // Arrange
            var command = new CreateQuoteCommand
            {
                CustomerId = TestDataSeeder.TestCustomerId,
                CustomerName = "Test Customer",
                ExpirationDate = DateTime.UtcNow.AddDays(30),
                OriginAddress = "123 Test St, Los Angeles, CA 90210",
                DestinationAddress = "456 Test Ave, New York, NY 10001",
                ServiceType = "Ground",
                PackageWeight = new WeightDto { Value = 25m, Unit = WeightUnit.Pounds },
                PackageDimensions = new DimensionsDto 
                { 
                    Length = 12, 
                    Width = 8, 
                    Height = 6, 
                    Unit = DimensionUnit.Inches 
                },
                DeclaredValue = 500m,
                SpecialServices = new List<string> { "SIGNATURE" },
                Currency = CurrencyCode.USD,
                Notes = "Test quote creation",
                AutoCalculateRates = true,
                LineItems = new List<CreateQuoteLineItemDto>
                {
                    new CreateQuoteLineItemDto
                    {
                        Description = "Test Item",
                        Quantity = 1,
                        UnitPrice = new MoneyDto { Amount = 25.00m, Currency = CurrencyCode.USD },
                        ItemType = "Shipping"
                    }
                }
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/quotes", command);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Created);
            
            var result = await response.Content.ReadFromJsonAsync<OperationResultDto<QuoteDto>>();
            result.Should().NotBeNull();
            result!.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data!.QuoteNumber.Should().NotBeNullOrEmpty();
            result.Data.CustomerId.Should().Be(TestDataSeeder.TestCustomerId);
            result.Data.CustomerName.Should().Be("Test Customer");
            result.Data.Status.Should().Be(QuoteStatus.Draft);
            result.Data.LineItems.Should().HaveCount(1);
        }

        [Fact]
        public async Task CreateQuote_WithInvalidRequest_ShouldReturnBadRequest()
        {
            // Arrange
            var command = new CreateQuoteCommand
            {
                // Missing required fields
                ExpirationDate = DateTime.UtcNow.AddDays(-1), // Past date
                OriginAddress = "",
                DestinationAddress = "",
                ServiceType = ""
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/quotes", command);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task GenerateQuote_WithValidRequest_ShouldReturnGeneratedQuote()
        {
            // Arrange
            var request = new QuoteCalculationRequestDto
            {
                CustomerId = TestDataSeeder.TestCustomerId,
                CustomerName = "Test Customer",
                ServiceType = "Ground",
                OriginAddress = "123 Test St, Los Angeles, CA 90210",
                DestinationAddress = "456 Test Ave, New York, NY 10001",
                PackageWeight = new WeightDto { Value = 25m, Unit = WeightUnit.Pounds },
                PackageDimensions = new DimensionsDto 
                { 
                    Length = 12, 
                    Width = 8, 
                    Height = 6, 
                    Unit = DimensionUnit.Inches 
                },
                DeclaredValue = 500m,
                SpecialServices = new List<string> { "SIGNATURE" },
                Currency = CurrencyCode.USD,
                ShipDate = DateTime.UtcNow.AddDays(1)
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/v1/quotes/generate", request);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest); // Not yet implemented
        }

        [Fact]
        public async Task GetQuotes_ShouldReturnPagedQuotes()
        {
            // Act
            var response = await _client.GetAsync("/api/v1/quotes?pageNumber=1&pageSize=10");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest); // Not yet implemented
        }

        [Fact]
        public async Task GetQuote_WithExistingId_ShouldReturnQuote()
        {
            // First, get a quote ID from the seeded data
            var quoteId = await _factory.ExecuteDbContextAsync(async context =>
            {
                var quote = context.Quotes.First(q => q.OrganizationId == TestDataSeeder.TestOrganizationId);
                return quote.Id;
            });

            // Act
            var response = await _client.GetAsync($"/api/v1/quotes/{quoteId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest); // Not yet implemented
        }

        [Fact]
        public async Task GetQuote_WithNonExistentId_ShouldReturnNotFound()
        {
            // Arrange
            var nonExistentId = Guid.NewGuid();

            // Act
            var response = await _client.GetAsync($"/api/v1/quotes/{nonExistentId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest); // Not yet implemented
        }

        [Fact]
        public async Task SendQuote_WithExistingId_ShouldReturnSuccess()
        {
            // First, get a quote ID from the seeded data
            var quoteId = await _factory.ExecuteDbContextAsync(async context =>
            {
                var quote = context.Quotes.First(q => q.OrganizationId == TestDataSeeder.TestOrganizationId);
                return quote.Id;
            });

            // Act
            var response = await _client.PostAsync($"/api/v1/quotes/{quoteId}/send", null);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest); // Not yet implemented
        }

        [Fact]
        public async Task AcceptQuote_WithExistingId_ShouldReturnSuccess()
        {
            // First, get a quote ID from the seeded data
            var quoteId = await _factory.ExecuteDbContextAsync(async context =>
            {
                var quote = context.Quotes.First(q => q.OrganizationId == TestDataSeeder.TestOrganizationId);
                return quote.Id;
            });

            // Act
            var response = await _client.PostAsync($"/api/v1/quotes/{quoteId}/accept", null);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest); // Not yet implemented
        }

        [Fact]
        public async Task ConvertQuoteToOrder_WithExistingId_ShouldReturnOrderId()
        {
            // First, get a quote ID from the seeded data
            var quoteId = await _factory.ExecuteDbContextAsync(async context =>
            {
                var quote = context.Quotes.First(q => q.OrganizationId == TestDataSeeder.TestOrganizationId);
                return quote.Id;
            });

            // Act
            var response = await _client.PostAsync($"/api/v1/quotes/{quoteId}/convert", null);

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest); // Not yet implemented
        }

        [Fact]
        public async Task DeleteQuote_WithExistingId_ShouldReturnSuccess()
        {
            // First, get a quote ID from the seeded data
            var quoteId = await _factory.ExecuteDbContextAsync(async context =>
            {
                var quote = context.Quotes.First(q => q.OrganizationId == TestDataSeeder.TestOrganizationId);
                return quote.Id;
            });

            // Act
            var response = await _client.DeleteAsync($"/api/v1/quotes/{quoteId}");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.BadRequest); // Not yet implemented
        }
    }
}
