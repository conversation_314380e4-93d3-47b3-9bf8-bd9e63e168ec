using PricingManagement.Domain.Entities;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.ValueObjects;
using PricingManagement.Infrastructure.Persistence;
using System.Text.Json;

namespace PricingManagement.IntegrationTests.Infrastructure
{
    public class TestDataSeeder
    {
        public static readonly Guid TestOrganizationId = Guid.Parse("11111111-1111-1111-1111-111111111111");
        public static readonly Guid TestCustomerId = Guid.Parse("AAAAAAAA-AAAA-AAAA-AAAA-AAAAAAAAAAAA");
        public static readonly Guid TestShipperId = Guid.Parse("BBBBBBBB-BBBB-BBBB-BBBB-BBBBBBBBBBBB");

        public async Task SeedAsync(PricingManagementDbContext context)
        {
            await SeedPricingRulesAsync(context);
            await SeedContractsAsync(context);
            await SeedQuotesAsync(context);
            await context.SaveChangesAsync();
        }

        private async Task SeedPricingRulesAsync(PricingManagementDbContext context)
        {
            var pricingRules = new List<PricingRule>
            {
                new PricingRule(
                    "Test Base Rate",
                    "Test base rate rule",
                    PricingRuleType.BaseRate,
                    1,
                    DateTime.UtcNow.AddDays(-30),
                    DateTime.UtcNow.AddYears(1),
                    TestOrganizationId,
                    "TestUser")
                {
                    RuleConfiguration = JsonSerializer.Serialize(new { baseRate = 15.00m }),
                    ServiceTypes = JsonSerializer.Serialize(new[] { "Ground" }),
                    Tags = JsonSerializer.Serialize(new[] { "test", "base" })
                },

                new PricingRule(
                    "Test Weight-Based",
                    "Test weight-based pricing",
                    PricingRuleType.WeightBased,
                    2,
                    DateTime.UtcNow.AddDays(-30),
                    DateTime.UtcNow.AddYears(1),
                    TestOrganizationId,
                    "TestUser")
                {
                    RuleConfiguration = JsonSerializer.Serialize(new { ratePerPound = 2.50m }),
                    ServiceTypes = JsonSerializer.Serialize(new[] { "Ground", "Express" }),
                    Tags = JsonSerializer.Serialize(new[] { "test", "weight" })
                }
            };

            // Add tiers to weight-based rule
            var weightRule = pricingRules.Last();
            weightRule.AddTier(0, 50, 2.50m, "per_unit", "TestUser");
            weightRule.AddTier(50, 100, 2.00m, "per_unit", "TestUser");

            await context.PricingRules.AddRangeAsync(pricingRules);
        }

        private async Task SeedContractsAsync(PricingManagementDbContext context)
        {
            var contract = new Contract(
                "TEST-CONTRACT-001",
                "Test Contract",
                "Test contract for integration tests",
                DateTime.UtcNow.AddDays(-30),
                DateTime.UtcNow.AddYears(1),
                CurrencyCode.USD,
                TestOrganizationId,
                "TestUser")
            {
                CustomerId = TestCustomerId,
                CustomerName = "Test Customer",
                Terms = "Net 30",
                PaymentTerms = "Net 30",
                MinimumCommitment = 10000m,
                CommitmentType = "Annual Revenue",
                AutoRenewal = true,
                ServiceTypes = JsonSerializer.Serialize(new[] { "Ground", "Express" })
            };

            // Add rates
            contract.AddRate("Ground", "Zone1", "Zone2", 0, 50, new Money(20.00m, CurrencyCode.USD), "Fixed", null, null, "TestUser");
            contract.AddRate("Express", "Zone1", "Zone2", 0, 50, new Money(35.00m, CurrencyCode.USD), "Fixed", null, null, "TestUser");

            // Add discounts
            contract.AddDiscount(DiscountType.VolumeDiscount, "Test volume discount", 10m, null, 100, null, null, null, "TestUser");

            await context.Contracts.AddAsync(contract);
        }

        private async Task SeedQuotesAsync(PricingManagementDbContext context)
        {
            var quote = new Quote(
                "TEST-QUOTE-001",
                DateTime.UtcNow.AddDays(30),
                CurrencyCode.USD,
                TestOrganizationId,
                "TestUser")
            {
                CustomerId = TestCustomerId,
                CustomerName = "Test Customer",
                OriginAddress = "123 Test St, Los Angeles, CA 90210",
                DestinationAddress = "456 Test Ave, New York, NY 10001",
                ServiceType = "Ground",
                PackageWeight = new Weight(25m, WeightUnit.Pounds),
                PackageDimensions = new Dimensions(12, 8, 6, DimensionUnit.Inches),
                DeclaredValue = 500m,
                Notes = "Test quote for integration tests"
            };

            // Add line items
            quote.AddLineItem("Test Shipping", 1, new Money(25.00m, CurrencyCode.USD), "Shipping", "TestUser");
            quote.AddSurcharge(SurchargeType.FuelSurcharge, "Test fuel surcharge", new Money(2.50m, CurrencyCode.USD), "Test", "TestUser");

            await context.Quotes.AddAsync(quote);
        }
    }
}
