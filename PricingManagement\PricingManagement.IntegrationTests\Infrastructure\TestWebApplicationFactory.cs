using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using PricingManagement.Infrastructure.Persistence;
using Testcontainers.PostgreSql;

namespace PricingManagement.IntegrationTests.Infrastructure
{
    public class TestWebApplicationFactory<TProgram> : WebApplicationFactory<TProgram>, IAsyncLifetime
        where TProgram : class
    {
        private readonly PostgreSqlContainer _dbContainer = new PostgreSqlBuilder()
            .WithImage("postgres:15-alpine")
            .WithDatabase("tritrackz_pricing_test")
            .WithUsername("test")
            .WithPassword("test")
            .WithCleanUp(true)
            .Build();

        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureServices(services =>
            {
                // Remove the existing DbContext registration
                var descriptor = services.SingleOrDefault(
                    d => d.ServiceType == typeof(DbContextOptions<PricingManagementDbContext>));
                if (descriptor != null)
                    services.Remove(descriptor);

                // Add test database
                services.AddDbContext<PricingManagementDbContext>(options =>
                {
                    options.UseNpgsql(_dbContainer.GetConnectionString());
                });

                // Build the service provider
                var serviceProvider = services.BuildServiceProvider();

                // Create the database and apply migrations
                using var scope = serviceProvider.CreateScope();
                var context = scope.ServiceProvider.GetRequiredService<PricingManagementDbContext>();
                context.Database.EnsureCreated();
            });

            builder.UseEnvironment("Testing");
        }

        public async Task InitializeAsync()
        {
            await _dbContainer.StartAsync();
        }

        public new async Task DisposeAsync()
        {
            await _dbContainer.StopAsync();
            await base.DisposeAsync();
        }

        public async Task<T> ExecuteDbContextAsync<T>(Func<PricingManagementDbContext, Task<T>> action)
        {
            using var scope = Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<PricingManagementDbContext>();
            return await action(context);
        }

        public async Task ExecuteDbContextAsync(Func<PricingManagementDbContext, Task> action)
        {
            using var scope = Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<PricingManagementDbContext>();
            await action(context);
        }

        public async Task SeedTestDataAsync()
        {
            await ExecuteDbContextAsync(async context =>
            {
                // Clear existing data
                context.Quotes.RemoveRange(context.Quotes);
                context.Contracts.RemoveRange(context.Contracts);
                context.PricingRules.RemoveRange(context.PricingRules);
                await context.SaveChangesAsync();

                // Add test data
                var seeder = new TestDataSeeder();
                await seeder.SeedAsync(context);
            });
        }

        public async Task CleanupTestDataAsync()
        {
            await ExecuteDbContextAsync(async context =>
            {
                context.Quotes.RemoveRange(context.Quotes);
                context.Contracts.RemoveRange(context.Contracts);
                context.PricingRules.RemoveRange(context.PricingRules);
                await context.SaveChangesAsync();
            });
        }
    }
}
