using FluentAssertions;
using PricingManagement.Domain.Enums;
using PricingManagement.Domain.Exceptions;
using PricingManagement.Domain.ValueObjects;
using Xunit;

namespace PricingManagement.Tests.Domain.ValueObjects
{
    public class MoneyTests
    {
        [Fact]
        public void Constructor_WithValidValues_ShouldCreateMoney()
        {
            // Arrange
            var amount = 100.50m;
            var currency = CurrencyCode.USD;

            // Act
            var money = new Money(amount, currency);

            // Assert
            money.Amount.Should().Be(100.50m);
            money.Currency.Should().Be(CurrencyCode.USD);
        }

        [Fact]
        public void Constructor_WithNegativeAmount_ShouldThrowException()
        {
            // Arrange
            var amount = -10.00m;
            var currency = CurrencyCode.USD;

            // Act & Assert
            var action = () => new Money(amount, currency);
            action.Should().Throw<DomainException>()
                .WithMessage("Amount cannot be negative");
        }

        [Fact]
        public void Add_WithSameCurrency_ShouldReturnCorrectSum()
        {
            // Arrange
            var money1 = new Money(100.00m, CurrencyCode.USD);
            var money2 = new Money(50.00m, CurrencyCode.USD);

            // Act
            var result = money1.Add(money2);

            // Assert
            result.Amount.Should().Be(150.00m);
            result.Currency.Should().Be(CurrencyCode.USD);
        }

        [Fact]
        public void Add_WithDifferentCurrency_ShouldThrowException()
        {
            // Arrange
            var money1 = new Money(100.00m, CurrencyCode.USD);
            var money2 = new Money(50.00m, CurrencyCode.EUR);

            // Act & Assert
            var action = () => money1.Add(money2);
            action.Should().Throw<InvalidCurrencyException>();
        }

        [Fact]
        public void Subtract_WithSameCurrency_ShouldReturnCorrectDifference()
        {
            // Arrange
            var money1 = new Money(100.00m, CurrencyCode.USD);
            var money2 = new Money(30.00m, CurrencyCode.USD);

            // Act
            var result = money1.Subtract(money2);

            // Assert
            result.Amount.Should().Be(70.00m);
            result.Currency.Should().Be(CurrencyCode.USD);
        }

        [Fact]
        public void Subtract_ResultingInNegative_ShouldThrowException()
        {
            // Arrange
            var money1 = new Money(50.00m, CurrencyCode.USD);
            var money2 = new Money(100.00m, CurrencyCode.USD);

            // Act & Assert
            var action = () => money1.Subtract(money2);
            action.Should().Throw<DomainException>()
                .WithMessage("Result cannot be negative");
        }

        [Fact]
        public void Multiply_WithPositiveFactor_ShouldReturnCorrectProduct()
        {
            // Arrange
            var money = new Money(100.00m, CurrencyCode.USD);
            var factor = 2.5m;

            // Act
            var result = money.Multiply(factor);

            // Assert
            result.Amount.Should().Be(250.00m);
            result.Currency.Should().Be(CurrencyCode.USD);
        }

        [Fact]
        public void Multiply_WithNegativeFactor_ShouldThrowException()
        {
            // Arrange
            var money = new Money(100.00m, CurrencyCode.USD);
            var factor = -1.5m;

            // Act & Assert
            var action = () => money.Multiply(factor);
            action.Should().Throw<DomainException>()
                .WithMessage("Factor cannot be negative");
        }

        [Fact]
        public void ApplyPercentage_ShouldReturnCorrectAmount()
        {
            // Arrange
            var money = new Money(100.00m, CurrencyCode.USD);
            var percentage = 15m; // 15%

            // Act
            var result = money.ApplyPercentage(percentage);

            // Assert
            result.Amount.Should().Be(15.00m);
            result.Currency.Should().Be(CurrencyCode.USD);
        }

        [Fact]
        public void Equals_WithSameAmountAndCurrency_ShouldReturnTrue()
        {
            // Arrange
            var money1 = new Money(100.00m, CurrencyCode.USD);
            var money2 = new Money(100.00m, CurrencyCode.USD);

            // Act & Assert
            money1.Equals(money2).Should().BeTrue();
            (money1 == money2).Should().BeTrue();
        }

        [Fact]
        public void Equals_WithDifferentAmount_ShouldReturnFalse()
        {
            // Arrange
            var money1 = new Money(100.00m, CurrencyCode.USD);
            var money2 = new Money(150.00m, CurrencyCode.USD);

            // Act & Assert
            money1.Equals(money2).Should().BeFalse();
            (money1 == money2).Should().BeFalse();
        }

        [Fact]
        public void Equals_WithDifferentCurrency_ShouldReturnFalse()
        {
            // Arrange
            var money1 = new Money(100.00m, CurrencyCode.USD);
            var money2 = new Money(100.00m, CurrencyCode.EUR);

            // Act & Assert
            money1.Equals(money2).Should().BeFalse();
            (money1 == money2).Should().BeFalse();
        }

        [Fact]
        public void Zero_ShouldCreateZeroMoney()
        {
            // Act
            var zero = Money.Zero(CurrencyCode.USD);

            // Assert
            zero.Amount.Should().Be(0m);
            zero.Currency.Should().Be(CurrencyCode.USD);
        }

        [Fact]
        public void ToString_ShouldReturnFormattedString()
        {
            // Arrange
            var money = new Money(123.45m, CurrencyCode.USD);

            // Act
            var result = money.ToString();

            // Assert
            result.Should().Contain("123.45");
            result.Should().Contain("USD");
        }

        [Theory]
        [InlineData(100.123, 100.12)]
        [InlineData(100.126, 100.13)]
        [InlineData(100.125, 100.13)]
        public void Constructor_ShouldRoundToTwoDecimalPlaces(decimal input, decimal expected)
        {
            // Act
            var money = new Money(input, CurrencyCode.USD);

            // Assert
            money.Amount.Should().Be(expected);
        }
    }
}
