# TriTrackz Pricing Management Microservice

A comprehensive pricing and rating service built with .NET 8, implementing Clean Architecture with CQRS pattern for the TriTrackz logistics platform.

## 🚀 Features

### Core Functionality
- **Dynamic Pricing Engine** - Real-time rate calculation with rule-based logic
- **Multi-Carrier Rate Comparison** - Compare rates across multiple carriers
- **Contract-Based Pricing** - Customer-specific pricing with version control
- **Promotional Pricing** - Time-limited promotions and discount management
- **Quote Management** - Generate, track, and convert quotes to orders
- **Surcharge Management** - Fuel, remote area, and special service surcharges
- **Tax Calculation** - Multi-jurisdiction tax calculation with exemptions
- **Currency Conversion** - Real-time currency conversion with multiple currencies
- **Pricing Simulation** - What-if scenarios and margin analysis

### Advanced Features
- **Approval Workflows** - Multi-level approval for discounts and pricing overrides
- **Pricing Intelligence** - Competitor analysis and market benchmarking
- **Volume Discounts** - Tiered pricing based on volume commitments
- **Dimensional Weight** - Automatic dimensional weight calculation
- **Multi-Tenant Architecture** - Organization-based data isolation
- **Event-Driven Integration** - Real-time updates via RabbitMQ

## 🏗️ Architecture

### Clean Architecture Layers
```
PricingManagement/
├── PricingManagement.Domain/          # Domain entities, value objects, events
├── PricingManagement.Application/     # CQRS handlers, DTOs, behaviors
├── PricingManagement.Infrastructure/  # Data access, external services
├── PricingManagement.API/            # REST API controllers
└── PricingManagement.Tests/          # Unit and integration tests
```

### Technology Stack
- **.NET 8** - Latest LTS framework
- **Entity Framework Core** - ORM with PostgreSQL
- **MediatR** - CQRS and mediator pattern
- **FluentValidation** - Input validation
- **AutoMapper** - Object mapping
- **JWT** - Authentication integration
- **RabbitMQ** - Event messaging
- **Redis** - Caching for performance
- **Swagger/OpenAPI** - API documentation
- **Serilog** - Structured logging
- **xUnit** - Testing framework

## 📊 Domain Model

### Core Entities

#### PricingRule
- Rule-based pricing logic with priorities
- Configurable criteria (weight, distance, zones, etc.)
- Tiered pricing structures
- Effective date management
- Approval workflows

#### Quote
- Multi-line item quotes
- Surcharges, discounts, and taxes
- Expiration tracking
- Order conversion
- Currency support

#### Contract
- Customer/shipper-specific pricing
- Rate tables with effective periods
- Volume commitments
- Auto-renewal capabilities
- Amendment tracking

### Value Objects
- **Money** - Currency-aware monetary values
- **Weight** - Multi-unit weight calculations
- **Dimensions** - Package dimensions with conversions

## 🔌 Integration Points

### Internal Services
- **Identity Service** - Authentication and authorization
- **UserManagement Service** - Organization and user context
- **MasterManagement Service** - Geographic and service data
- **CustomerManagement Service** - Customer profiles and segments
- **ShipperManagement Service** - Shipper profiles and preferences
- **OrderManagement Service** - Order creation and management
- **ShipmentManagement Service** - Shipment tracking and updates

### External Services
- **Currency APIs** - Real-time exchange rates
- **Tax APIs** - Tax calculation and compliance
- **Carrier APIs** - Live carrier rates and services
- **Fuel Surcharge APIs** - Current fuel surcharge data

## 🚀 Getting Started

### Prerequisites
- .NET 8 SDK
- PostgreSQL 13+
- RabbitMQ
- Redis (optional)

### Installation

1. **Clone and navigate to the service**
   ```bash
   cd PricingManagement
   ```

2. **Restore dependencies**
   ```bash
   dotnet restore
   ```

3. **Update database connection**
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Host=localhost;Database=TriTrackzPricingManagement;Username=timescale;Password=timescale"
     }
   }
   ```

4. **Run database migrations**
   ```bash
   dotnet ef database update --project PricingManagement.Infrastructure --startup-project PricingManagement.API
   ```

5. **Start the service**
   ```bash
   dotnet run --project PricingManagement.API
   ```

The service will be available at `https://localhost:5009`

## 📚 API Documentation

### Core Endpoints

#### Pricing Engine
- `POST /api/v1/pricing/calculate` - Calculate shipping rates
- `POST /api/v1/pricing/compare` - Compare carrier rates
- `POST /api/v1/pricing/simulate` - Run pricing simulations
- `POST /api/v1/pricing/validate` - Validate rate requests

#### Pricing Rules
- `GET /api/v1/pricing-rules` - List pricing rules
- `POST /api/v1/pricing-rules` - Create pricing rule
- `PUT /api/v1/pricing-rules/{id}` - Update pricing rule
- `POST /api/v1/pricing-rules/{id}/activate` - Activate rule

#### Quotes
- `GET /api/v1/quotes` - List quotes
- `POST /api/v1/quotes` - Create quote
- `POST /api/v1/quotes/{id}/generate` - Generate quote
- `POST /api/v1/quotes/{id}/convert` - Convert to order

#### Contracts
- `GET /api/v1/contracts` - List contracts
- `POST /api/v1/contracts` - Create contract
- `POST /api/v1/contracts/{id}/activate` - Activate contract

### Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## 🧪 Testing

### Run Unit Tests
```bash
dotnet test PricingManagement.Tests --filter Category=Unit
```

### Run Integration Tests
```bash
dotnet test PricingManagement.Tests --filter Category=Integration
```

### Run All Tests
```bash
dotnet test PricingManagement.Tests
```

## 📈 Performance

### Caching Strategy
- **Redis** for frequently accessed pricing rules
- **Memory cache** for currency conversion rates
- **Query result caching** for complex calculations

### Performance Targets
- **Rate Calculation**: < 200ms response time
- **Quote Generation**: < 500ms response time
- **Throughput**: 1000+ requests/second
- **Availability**: 99.9% uptime

## 🔧 Configuration

### Key Settings
```json
{
  "PricingSettings": {
    "DefaultCurrency": "USD",
    "MaxQuoteValidityDays": 30,
    "DefaultDimensionalWeightDivisor": 139,
    "RequireApprovalThreshold": 1000.00,
    "MaxDiscountPercentage": 50.0
  },
  "FeatureFlags": {
    "EnableCaching": true,
    "EnableRealTimePricing": true,
    "EnableCarrierIntegration": false,
    "EnableAdvancedAnalytics": true
  }
}
```

## 🔒 Security

### Multi-Tenant Security
- **Organization Scoping** - All data scoped to organization
- **Query Filters** - Automatic organization filtering
- **User Context** - Current user and organization tracking
- **Data Isolation** - Complete tenant data separation

### Authentication & Authorization
- **JWT Integration** - Seamless integration with Identity service
- **Role-Based Access** - Fine-grained permission control
- **API Key Support** - For external integrations
- **Rate Limiting** - Protection against abuse

## 📊 Monitoring

### Health Checks
- Database connectivity
- External service availability
- Cache performance
- Message queue status

### Metrics
- Request/response times
- Error rates
- Cache hit ratios
- Pricing calculation accuracy

### Logging
- Structured logging with Serilog
- Request/response logging
- Performance monitoring
- Error tracking

## 🚀 Deployment

### Docker Support
```dockerfile
# Build and run with Docker
docker build -t tritrackz-pricing .
docker run -p 5009:80 tritrackz-pricing
```

### Environment Variables
- `ASPNETCORE_ENVIRONMENT` - Environment (Development/Production)
- `ConnectionStrings__DefaultConnection` - Database connection
- `JwtSettings__Secret` - JWT signing key
- `RabbitMQ__Host` - Message broker host

## 🤝 Contributing

1. Follow Clean Architecture principles
2. Write comprehensive tests
3. Update documentation
4. Follow coding standards
5. Submit pull requests

## 📄 License

This project is part of the TriTrackz logistics platform.
